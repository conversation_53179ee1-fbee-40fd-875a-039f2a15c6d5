import pytest
from base.model.document import DocumentInfo, Segment, SegmentProperties, str_to_bool, BlockElement, TableElement
import json


@pytest.fixture(
    scope="module",
    params=[
        "test/model/DataLoadTool_output.json",
    ],
)
def load_test_data(request):
    with open(request.param, "r") as f:
        return json.load(f)


class TestDocumentModels:
    def test_document_info(self, load_test_data):
        document_info = DocumentInfo(**load_test_data)  # Assuming the first item is a DocumentInfo
        assert isinstance(document_info, DocumentInfo)

    def test_str_to_bool_conversion(self):
        # Test valid string conversions
        assert str_to_bool("true") is True
        assert str_to_bool("false") is False
        assert str_to_bool("True") is True
        assert str_to_bool("False") is False

        # Test non-string boolean values (should return as is)
        assert str_to_bool(True) is True
        assert str_to_bool(False) is False

    def test_document_info_full_load(self, load_test_data):
        # Assuming DataLoadTool_output.json is the last item loaded by the fixture
        document_data = load_test_data
        document_info = DocumentInfo(**document_data)
        assert isinstance(document_info, DocumentInfo)
        assert document_info.version == "1.0"
        assert document_info.document_id == "ae87115d-d32f-4b1e-9d8f-db12ebaaecf9.temp.dox"
        assert len(document_info.elements) > 0
        assert document_info.annotations is not None
        assert document_info.native_revisions is not None

    def test_document_elements(self, load_test_data):
        document_data = load_test_data
        document_info = DocumentInfo(**document_data)

        # Check for ParagraphElement
        paragraph_element = next(
            (e for e in document_info.elements if e.element_type == "Paragraph" and e.element_id == "40718D36"), None
        )
        assert paragraph_element is not None
        assert isinstance(paragraph_element, BlockElement)
        assert paragraph_element.plain_text == "Discharge by frustration"
        assert paragraph_element.properties.style == "Heading1"
        assert paragraph_element.properties.indent_left == 120
        assert len(paragraph_element.segments) == 1

        # Check for TableElement
        table_element = next(
            (e for e in document_info.elements if e.element_type == "Table" and e.element_id == "tbl-0"), None
        )
        assert table_element is not None
        assert isinstance(table_element, TableElement)
        assert len(table_element.rows) > 0
        assert len(table_element.rows[0].cells) > 0

    def test_table_element_and_cells(self, load_test_data):
        document_data = load_test_data
        document_info = DocumentInfo(**document_data)

        table_element = next(
            (e for e in document_info.elements if e.element_type == "Table" and e.element_id == "tbl-0"), None
        )
        assert table_element is not None

        first_row = table_element.rows[0]
        assert first_row.row_id == 0
        assert len(first_row.cells) == 2

        first_cell = first_row.cells[0]
        assert first_cell.cell_id == 0
        assert len(first_cell.elements) == 1
        assert first_cell.elements[0].plain_text == "-"

        second_cell = first_row.cells[1]
        assert second_cell.cell_id == 1
        assert len(second_cell.elements) == 1
        assert second_cell.elements[0].plain_text == "New South Wales"

    def test_iter_paragraph(self, load_test_data):
        document_data = load_test_data
        document_info = DocumentInfo(**document_data)

        paragraphs = list(document_info.iter_paragraph())
        assert len(paragraphs) > 0

        # Check if paragraphs from both BlockElement and TableElement are included
        found_paragraph_in_table = False
        for p in paragraphs:
            if p.element_id == "40718DA3":  # A paragraph inside a table cell
                found_paragraph_in_table = True
                break
        assert found_paragraph_in_table

    def test_fulltext_property(self, load_test_data):
        document_data = load_test_data
        document_info = DocumentInfo(**document_data)

        full_text = document_info.fulltext
        assert isinstance(full_text, str)
        assert len(full_text) > 0
        assert "Discharge by frustration" in full_text
        assert "New South Wales" in full_text  # Text from inside a table

    def test_annotations(self, load_test_data):
        document_data = load_test_data
        document_info = DocumentInfo(**document_data)

        annotations = document_info.annotations
        assert annotations is not None
        assert len(annotations.comments) > 0
        assert len(annotations.hyperlinks) > 0

        # Test a specific comment
        comment = annotations.comments.get("4")
        assert comment is not None
        assert comment.author == "Moschou, Florie (LNG-HBE)"
        assert "Please link to heading below" in comment.text

        # Test a specific hyperlink
        hyperlink = annotations.hyperlinks.get("rId13")
        assert hyperlink is not None
        assert (
            hyperlink.uri
            == "https://advance.lexis.com/api/document?collection=practical-guidance-au&id=urn:contentItem:69WT-94H1-JS5Y-B251-00000-00&context=1201009"
        )
        assert len(hyperlink.targets) > 0

    def test_native_revisions(self, load_test_data):
        document_data = load_test_data
        document_info = DocumentInfo(**document_data)

        native_revisions = document_info.native_revisions
        assert native_revisions is not None
        assert len(native_revisions) > 0

        # Test a specific revision
        revision = native_revisions.get("1")
        assert revision is not None
        assert revision.key == "NativeRevision"
        assert revision.rev_type == "DeletedRun"
        assert revision.author == "Moschou, Florie (LNG-HBE)"
        assert revision.date == "7/1/2025 9:35:00 AM"
