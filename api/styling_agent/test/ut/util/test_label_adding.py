import difflib

import pytest
from base.util.label_adding import classify_by_opcodes
from hypothesis import given
from hypothesis import strategies as st



def classify(old: str, new: str) -> str:
    """
    Classify the edit type between old and new strings using token-level diff.
    """
    a = list(old)
    b = list(new)
    sm = difflib.SequenceMatcher(None, a, b, autojunk=False)
    return classify_by_opcodes(sm.get_opcodes(), len(a))


CASES = [
    ("abc", "xabc", "prefix"),  # 头插
    ("abc", "abcx", "suffix"),  # 尾插
    ("abc", "xabcx", "wrap"),  # 头尾插
    ("abc", "aXc", "inplace"),  # replace
    ("abc", "ac", "inplace"),  # delete
    ("abc", "abxc", "inplace"),  # 中插
    # 空串
    ("", "", "inplace"),
    ("", "x", "suffix"),
    # 多段 wrap
    ("abc", "xxabcxx", "wrap"),
    # 头+中、尾+中
    ("abc", "xabxc", "inplace"),
    ("abc", "abxcx", "inplace"),
    # replace + suffix
    ("abc", "abx", "inplace"),
    ("abc", "axbcx", "inplace"),
]


@pytest.mark.parametrize("old,new,expected", CASES)
def test_basic_classification(old, new, expected):
    assert classify(old, new) == expected



def not_in(chars):
    """策略: 任选 ascii_lowercase 中不在 chars 里的字符"""
    return st.sampled_from([c for c in "abcdefghijklmnopqrstuvwxyz" if c not in chars])


alpha = st.text("abcxyz", min_size=1, max_size=100)  # old 字符来源


@given(old=alpha, h=not_in("abcxyz"), t=not_in("abcxyz"))
def test_prefix_suffix_wrap(old, h, t):
    # prefix：头插一个与 old[0] 不同的 h
    assert classify(old, h + old) == "prefix"
    # suffix：尾插一个与 old[-1] 不同的 t
    assert classify(old, old + t) == "suffix"
    # wrap：头尾各插不同字符
    assert classify(old, h + old + t) == "wrap"


# ---------- Hypothesis：中间插 / 删除 / 替换 一律 inplace ----------
@given(
    old=alpha.filter(lambda s: len(s) >= 2),
    mid=not_in("abcxyz"),  # 确保 mid 与 old 完全不同, 否则结果会有歧义
)
def test_middle_insert_inplace(old, mid):
    idx = len(old) // 2
    new = old[:idx] + mid + old[idx:]
    assert classify(old, new) == "inplace"
