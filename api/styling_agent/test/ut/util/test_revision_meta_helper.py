import pytest

from base.util.convert import Span
from base.util.revision_meta_helper import RevisionMetaHelper, find_changed_segments


def revision_validate(target: list[dict], base_revision: list[dict]):
    assert len(target) == len(base_revision)
    for inx, item in enumerate(base_revision):
        assert item["Key"] == target[inx]["Key"]
        assert item["Text"] == target[inx]["Text"]
        if "Author" in item:
            assert item["Author"] == target[inx]["Author"]

def test_empty_revision_data_and_change_texts():
    # Test with empty revision data and change texts
    revision_data, change_texts = None, None
    helper = RevisionMetaHelper(revision_data)
    result = helper.run(change_texts)
    assert result is None


def test_success_from_cases(revision_test_case):
    for item in revision_test_case:
        original_revison = item["original_revision"]
        change_texts = item["change_texts"]

        helper = RevisionMetaHelper(original_revison)
        result = helper.run(change_texts)

        assert result is not None
        final_change_texts = item["final_change_texts"]
        revision_validate(result, final_change_texts)


"""Comprehensive unit tests for `find_changed_segments`.

These tests assume your production code exposes `Span` and
`find_changed_segments` in a single importable module.  Edit the import
line to match your project’s package structure.

pytest‑style assertions are used so you can simply run:

    pytest test_find_changed_segments.py -q

All test cases focus on edge‑conditions the algorithm must handle:

1. **No change** – identical strings should return an empty dict.
2. **Pure delete** – text present in *old* but gone in *new*.
3. **Pure insert** – text added in *new* (anchored in same revision).
4. **Replace** – combined delete+insert in the same location.
5. **Insert at boundary where the *left* span is a revision** – ensures
   `anchor_span` correctly selects that span.
6. **Insert at boundary where the *left* span is BASE** – forces
   `anchor_span` to fall through to the right‑hand revision when the
   immediate left candidate is BASE.
"""

def mk_span(rev_id: str | None, kind: str, start: int, end: int):
    """Convenience wrapper so tests are compact."""
    return Span(rev_id=rev_id or "", kind=kind, start=start, end=end)


@pytest.mark.parametrize(
    "old_plain,new_plain,spans,expected",
    [
        # 1. No change – result should be empty
        (
            "Simple text.",
            "Simple text.",
            [mk_span("1", "BASE", 0, len("Simple text."))],
            {},
        ),
        # 2. Pure delete inside a revision span
        (
            "abcXdef",
            "abcdef",
            [
                mk_span("", "BASE", 0, 3),  # "abc"
                mk_span("1", "INS", 3, 4),  # "X"
                mk_span("", "BASE", 4, 7),  # "def"
            ],
            {
                "1": {
                    "kind": "INS",
                    "old_seg": "X",
                    "new_seg": "",
                }
            },
        ),
        # 3. Pure insert inside a single‑revision span (whole string)
        (
            "abcdef",
            "abcXdef",
            [mk_span("1", "BASE", 0, 6)],
            {
                "1": {
                    "kind": "BASE",
                    "old_seg": "abcdef",
                    "new_seg": "abcXdef",
                }
            },
        ),
        # 4. Replace one character inside a revision span
        (
            "abcXdef",
            "abcYdef",
            [
                mk_span("", "BASE", 0, 3),
                mk_span("1", "INS", 3, 4),
                mk_span("", "BASE", 4, 7),
            ],
            {
                "1": {
                    "kind": "INS",
                    "old_seg": "X",
                    "new_seg": "Y",
                }
            },
        ),
        # 5. Insert at boundary where left span is a revision (non‑BASE)
        (
            "AB",
            "AxB",
            [
                mk_span("1", "INS", 0, 1),  # "A"
                mk_span("", "BASE", 1, 2),  # "B"
            ],
            {
                "1": {
                    "kind": "INS",
                    "old_seg": "A",
                    "new_seg": "Ax",
                }
            },
        ),
        # 6. Insert at boundary where left span is BASE → anchor right span
        (
            "AB",
            "AxB",
            [
                mk_span("", "BASE", 0, 1),  # "A"
                mk_span("1", "INS", 1, 2),  # "B"
            ],
            {
                "1": {
                    "kind": "INS",
                    "old_seg": "B",
                    "new_seg": "xB",
                }
            },
        ),
        # 7. Two edits in the same revision – delete then insert
        (
            "abcXYdef",
            "abcZdef",
            [
                mk_span("", "BASE", 0, 3),  # abc
                mk_span("1", "INS", 3, 5),  # XY
                mk_span("", "BASE", 5, 8),  # def
            ],
            {
                "1": {
                    "kind": "INS",
                    "old_seg": "XY",
                    "new_seg": "Z",
                }
            },
        ),
        # 8. Replace spanning two different revisions
        (
            "A B",
            "a b",
            [
                mk_span("1", "INS", 0, 1),  # A
                mk_span("2", "INS", 2, 3),  # B
            ],
            {
                "1": {"kind": "INS", "old_seg": "A", "new_seg": "a"},
                "2": {"kind": "INS", "old_seg": "B", "new_seg": "b"},
            },
        ),
        # 9. Insertion at very start (char_pos == 0)
        (
            "BC",
            "xBC",
            [
                mk_span("1", "INS", 0, 1),  # B
                mk_span("", "BASE", 1, 2),  # C
            ],
            {
                "1": {"kind": "INS", "old_seg": "B", "new_seg": "xB"},
            },
        ),
    ],
)
def test_find_changed_segments(old_plain, new_plain, spans, expected):
    """Parametric test harness exercising varied edit scenarios."""
    result = find_changed_segments(old_plain, new_plain, spans)
    assert result == expected
