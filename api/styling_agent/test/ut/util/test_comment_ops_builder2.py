import pytest
from base.util.comment_ops_builder2 import build_comment_ops, MARKER_RE, MARKER_TOK_RE
from base.util.segment_parser import Segment2


@pytest.fixture
def segments():
    """设置测试数据"""
    return [
        Segment2(seg_id="seg1", start=0, end=20),
        Segment2(seg_id="seg2", start=20, end=40),
        Segment2(seg_id="seg3", start=40, end=60),
    ]


class TestCommentOpsBuilder2:
    """测试 comment_ops_builder2.py 的批注标记格式修改"""

    def test_marker_re_with_number(self):
        """测试带数字序号的批注标记格式"""
        # 测试各种带数字序号的格式
        test_cases = [
            "world[123][Grammar Check Tool]",
            "test[42][Spelling Check Tool]",
            "sample[99][Quotation Check Tool]",
        ]

        for test_case in test_cases:
            match = MARKER_RE.search(test_case)
            assert match is not None, f"应该匹配格式: {test_case}"
            expected_tool = (
                "Grammar Check Tool"
                if "Grammar" in test_case
                else ("Spelling Check Tool" if "Spelling" in test_case else "Quotation Check Tool")
            )
            # For numbered tags, group(1) contains the number, group(2) contains the tool
            assert match.group(2) == expected_tool
            assert match.group(1) is not None  # Should have a number

    def test_marker_re_without_number(self):
        """测试不带数字序号的批注标记格式"""
        # 测试各种不带数字序号的格式
        test_cases = [
            "world[Grammar Check Tool]",
            "test[Spelling Check Tool]",
            "sample[Quotation Check Tool]",
        ]

        for test_case in test_cases:
            match = MARKER_RE.search(test_case)
            assert match is not None, f"应该匹配格式: {test_case}"
            expected_tool = (
                "Grammar Check Tool"
                if "Grammar" in test_case
                else ("Spelling Check Tool" if "Spelling" in test_case else "Quotation Check Tool")
            )
            # For unnumbered tags, group(1) is None, group(2) contains the tool
            assert match.group(2) == expected_tool
            assert match.group(1) is None  # Should not have a number

    def test_marker_re_mixed_formats(self):
        """测试混合格式的批注标记"""
        # 测试文本中同时包含两种格式
        test_text = "hello[1][example Tool] and world[Grammar Check Tool]"

        matches = list(MARKER_RE.finditer(test_text))
        assert len(matches) == 2, "应该找到2个匹配"

        # 第一个匹配（带数字）
        match1 = matches[0]
        assert match1.group(1) == "1", "带数字的格式，group(1)应该是数字"
        assert match1.group(2) == "example Tool"

        # 第二个匹配（不带数字）
        match2 = matches[1]
        assert match2.group(1) is None, "不带数字的格式，group(1)为None"
        assert match2.group(2) == "Grammar Check Tool"

    def test_marker_tok_re(self):
        """测试标记token的正则表达式"""
        # 测试纯标记token
        test_cases = [
            "[123][Grammar Check Tool]",
            "[1][example tool][2][Grammar Check Tool]",
            "[example tool][Grammar Check Tool]",  # Mixed unnumbered
        ]

        for test_case in test_cases:
            assert MARKER_TOK_RE.fullmatch(test_case), f"应该匹配纯标记token: {test_case}"

    def test_build_comment_ops_with_number(self, segments):
        """测试带数字序号的批注操作构建"""
        original = "Hello world"
        commented = "Hello hahah[1][Grammar Check Tool]"
        run_style_text = "Hello world"

        patches = build_comment_ops(original, commented, run_style_text, segments)

        # 应该生成批注操作
        assert len(patches) > 0, "应该生成批注操作"

        # 检查批注内容
        patch = patches[0]
        assert patch["op"] == "commentAdd"
        assert "Grammar Check Tool" in patch["comment"]["text"]

    def test_build_comment_ops_without_number(self, segments):
        """测试不带数字序号的批注操作构建"""
        original = "Hello world"
        commented = "Hello 1111[Grammar Check Tool]"
        run_style_text = "Hello world"

        patches = build_comment_ops(original, commented, run_style_text, segments)

        # 应该生成批注操作
        assert len(patches) > 0, "应该生成批注操作"

        # 检查批注内容
        patch = patches[0]
        assert patch["op"] == "commentAdd"
        assert "Grammar Check Tool" in patch["comment"]["text"]

    def test_build_comment_ops_mixed(self, segments):
        """测试混合格式的批注操作构建"""
        original = "Hello world test example"
        commented = "Hellaaa[1][Grammar Check Tool] hahah[Spelling Check Tool] test2[2][Number Check Tool]"
        run_style_text = "Hello world test example"

        patches = build_comment_ops(original, commented, run_style_text, segments)

        # 应该生成多个批注操作
        assert len(patches) >= 2, "应该生成多个批注操作"

    def test_invalid_formats(self):
        """测试无效格式不被匹配"""
        invalid_cases = [
            "hello[example]",  # 缺少Tool结尾
            "hello[1]example tool]",  # 格式错误
            "hello[1][example tool]",  # tool 小写
            "hello[1][example",  # 缺少结尾的]
            "hello[example tool",  # 缺少结尾的]
            "hello(1)(example tool)",  # 错误的括号类型
        ]

        for invalid_case in invalid_cases:
            match = MARKER_RE.search(invalid_case)
            assert match is None, f"不应该匹配无效格式: {invalid_case}"

    def test_edge_cases(self):
        """测试边界情况"""
        # 测试空字符串
        assert MARKER_RE.search("") is None

        # 测试只有标记的情况 - 带数字
        match = MARKER_RE.search("[1][example Tool]")
        assert match is not None
        assert match.group(1) == "1"
        assert match.group(2) == "example Tool"

        # 测试只有标记的情况 - 不带数字
        match = MARKER_RE.search("[example Tool]")
        assert match is not None
        assert match.group(1) is None
        assert match.group(2) == "example Tool"

        # 测试标记在字符串开头和结尾
        match1 = MARKER_RE.search("[1][example Tool] hello")
        assert match1 is not None

        match2 = MARKER_RE.search("hello [1][example Tool]")
        assert match2 is not None

        match3 = MARKER_RE.search("[example Tool] hello")
        assert match3 is not None

        match4 = MARKER_RE.search("hello [example Tool]")
        assert match4 is not None
