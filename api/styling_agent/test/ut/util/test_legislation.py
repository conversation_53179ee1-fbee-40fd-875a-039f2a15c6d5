from unittest.mock import patch
from base.util.legislation import search_all_legislations
from base.util.constants import YEAR_NOT_FOUND, JX_NOT_FOUND, JX_NOT_FOUND


@patch("base.util.legislation.caas_repo_client.query_meta")
@patch("base.util.legislation.config")
def test_search_all_legislations_valid_legislation(mock_config, mock_query_meta):
    mock_config.CAAS_REPO_URL = "https://example.com"
    mock_config.META_ENDPOINT = "/meta"
    mock_config.META_NAME = "/search"

    mock_query_meta.return_value = {
        "data": {
            "hits": {
                "hits": [
                    {"_source": {"jurisdiction": "US", "leg_title": "Some Act 2020"}}
                ]
            }
        }
    }

    jurisdiction, year = search_all_legislations("Some Act")
    assert jurisdiction == JX_NOT_FOUND
    assert year == "2020"


@patch("base.util.legislation.caas_repo_client.query_meta")
@patch("base.util.legislation.config")
def test_search_all_legislations_valid_legislation_jx(mock_config, mock_query_meta):
    mock_config.CAAS_REPO_URL = "https://example.com"
    mock_config.META_ENDPOINT = "/meta"
    mock_config.META_NAME = "/search"

    mock_query_meta.return_value = {
        "data": {
            "hits": {
                "hits": [
                    {"_source":{"jurisdiction": "CTH", "leg_title": "Some Act 2020"}}
                ]
            }
        }
    }

    jurisdiction, year = search_all_legislations(
        "Competition and Consumer (Industry Codes—Franchising) Regulations 2024",
        jurisdiction="commonwealth"
    )
    assert jurisdiction == "Cth"
    assert year == "2020"


@patch("base.util.legislation.caas_repo_client.query_meta")
@patch("base.util.legislation.config")
def test_search_all_legislations_invalid_legislation(mock_config, mock_query_meta):
    mock_config.CAAS_REPO_URL = "https://example.com"
    mock_config.META_ENDPOINT = "/meta"
    mock_config.META_NAME = "/search"

    mock_query_meta.return_value = {
        "data": {
            "hits": {
                "hits": [
                    {"_source":{"jurisdiction": "US", "leg_title": "Some Act 2020"}}
                ]
            }
        }
    }

    jurisdiction, year = search_all_legislations("Test not found")
    assert jurisdiction == ""
    assert year == ""


@patch("base.util.legislation.caas_repo_client.query_meta")
@patch("base.util.legislation.config")
def test_search_all_legislations_no_hits(mock_config, mock_query_meta):
    mock_config.CAAS_REPO_URL = "https://example.com"
    mock_config.META_ENDPOINT = "/meta"
    mock_config.META_NAME = "/search"

    mock_query_meta.return_value = {
        "data": {
            "hits": {
                "hits": []
            }
        }
    }

    jurisdiction, year = search_all_legislations("Nonexistent Act")
    assert jurisdiction == ""
    assert year == ""


@patch("base.util.legislation.caas_repo_client.query_meta")
@patch("base.util.legislation.config")
def test_search_all_legislations_invalid_jurisdiction(mock_config, mock_query_meta):
    mock_config.CAAS_REPO_URL = "https://example.com"
    mock_config.META_ENDPOINT = "/meta"
    mock_config.META_NAME = "/search"

    mock_query_meta.return_value = {
        "data": {
            "hits": {
                "hits": [
                    {"_source":{"jurisdiction": "InvalidJX", "leg_title": "Some Act 2020"}}
                ]
            }
        }
    }

    jurisdiction, year = search_all_legislations("Some Act")
    assert jurisdiction == JX_NOT_FOUND
    assert year == "2020"


@patch("base.util.legislation.caas_repo_client.query_meta")
@patch("base.util.legislation.config")
def test_search_all_legislations_year_not_found(mock_config, mock_query_meta):
    mock_config.CAAS_REPO_URL = "https://example.com"
    mock_config.META_ENDPOINT = "/meta"
    mock_config.META_NAME = "/search"

    mock_query_meta.return_value = {
        "data": {
            "hits": {
                "hits": [
                    {"_source":{"jurisdiction": "CTH", "leg_title": "Some Act"}}
                ]
            }
        }
    }

    jurisdiction, year = search_all_legislations("Some Act")
    assert jurisdiction == "Cth"
    assert year == YEAR_NOT_FOUND


@patch("base.util.legislation.caas_repo_client.query_meta")
@patch("base.util.legislation.config")
def test_search_all_legislations_with_jurisdiction_filter(mock_config, mock_query_meta):
    mock_config.CAAS_REPO_URL = "https://example.com"
    mock_config.META_ENDPOINT = "/meta"
    mock_config.META_NAME = "/search"

    mock_query_meta.return_value = {
        "data": {
            "hits": {
                "hits": [
                    {"_source":{"jurisdiction": "CTH", "leg_title": "Some Act 2020"}},
                    {"_source":{"jurisdiction": "UK", "leg_title": "Some Act 2020"}}
                ]
            }
        }
    }

    jurisdiction, year = search_all_legislations("Some Act", jurisdiction="CTH")
    assert jurisdiction == "Cth"
    assert year == "2020"


@patch("base.util.legislation.logger.error")
@patch("base.util.legislation.caas_repo_client.query_meta")
@patch("base.util.legislation.config")
def test_search_all_legislations_exception_handling(mock_config, mock_query_meta, mock_logger_error):
    mock_config.CAAS_REPO_URL = "https://example.com"
    mock_config.META_ENDPOINT = "/meta"
    mock_config.META_NAME = "/search"

    mock_query_meta.side_effect = Exception("Test exception")

    jurisdiction, year = search_all_legislations("Some Act")
    assert jurisdiction == JX_NOT_FOUND
    assert year == YEAR_NOT_FOUND
    mock_logger_error.assert_called_once_with("Error searching legislation: Test exception")

