from typing import Any

import pytest
from base.util.edit_ops_builder import build_edit_ops
from base.util.segment_parser import Segment2 as Segment


def mk_seg(pid: str, idx: int, start: int, end: int):
    return Segment(f"{pid}-{idx:03d}", start, end)


REV = {"author": "UT", "date": "2025-06-18T00:00:00Z"}


def strip_rev(ops):
    return [{k: d[k] for k in ("op", "target", "text")} for d in ops]


cases: list[dict[str, Any]] = [
    # 1. No change
    {
        "name": "no-change",
        "before_xml": "<w:p><w:r><w:t>Simple text.</w:t></w:r></w:p>",
        "after_xml": "<w:p><w:r><w:t>Simple text.</w:t></w:r></w:p>",
        "old_txt": "Simple text.",
        "new_txt": "Simple text.",
        "segs": [mk_seg("p1", 0, 0, len("Simple text."))],
        "expected": [],
    },
    # 2. Pure delete
    {
        "name": "pure-delete",
        "before_xml": "<w:p><w:r><w:t>abcXdef</w:t></w:r></w:p>",
        "after_xml": "<w:p><w:r><w:t>abcdef</w:t></w:r></w:p>",
        "old_txt": "abcXdef",
        "new_txt": "abcdef",
        "segs": [mk_seg("p1", 0, 0, 7)],
        "expected": [
            {"op": "delete", "target": [{"segId": "p1-000", "range": {"start": 3, "end": 4}}], "text": "X"},
        ],
    },
    # 3. Pure insert
    {
        "name": "pure-insert",
        "before_xml": "<w:p><w:r><w:t>abcdef</w:t></w:r></w:p>",
        "after_xml": "<w:p><w:r><w:t>abcXdef</w:t></w:r></w:p>",
        "old_txt": "abcdef",
        "new_txt": "abcXdef",
        "segs": [mk_seg("p1", 0, 0, 6)],
        "expected": [
            {"op": "insert", "target": [{"segId": "p1-000", "range": {"start": 3, "end": 3}}], "text": "X"},
        ],
    },
    # 4. Single-char replace
    {
        "name": "single-char-replace",
        "before_xml": "<w:p><w:r><w:t>abcXdef</w:t></w:r></w:p>",
        "after_xml": "<w:p><w:r><w:t>abcYdef</w:t></w:r></w:p>",
        "old_txt": "abcXdef",
        "new_txt": "abcYdef",
        "segs": [mk_seg("p1", 0, 0, 7)],
        "expected": [
            {"op": "replace", "target": [{"segId": "p1-000", "range": {"start": 3, "end": 4}}], "text": "Y"},
            # {"op": "delete", "target": ["p1-000"], "range": {"start": 3, "end": 4}, "text": "X"},
            # {"op": "insert", "target": ["p1-000"], "range": {"start": 3, "end": 3}, "text": "Y"},
        ],
    },
    # 5. Multi-char replace
    {
        "name": "multi-char-replace",
        "before_xml": "<w:p><w:r><w:t>abcXYZdef</w:t></w:r></w:p>",
        "after_xml": "<w:p><w:r><w:t>abc1def</w:t></w:r></w:p>",
        "old_txt": "abcXYZdef",
        "new_txt": "abc1def",
        "segs": [mk_seg("p1", 0, 0, 9)],
        "expected": [
            {"op": "replace", "target": [{"segId": "p1-000", "range": {"start": 3, "end": 6}}], "text": "1"},
            # {"op": "delete", "target": ["p1-000"], "range": {"start": 3, "end": 6}, "text": "XYZ"},
            # {"op": "insert", "target": ["p1-000"], "range": {"start": 3, "end": 3}, "text": "1"},
        ],
    },
    # 6. Insert at start
    {
        "name": "insert-at-start",
        "before_xml": "<w:p><w:r><w:t>BC</w:t></w:r></w:p>",
        "after_xml": "<w:p><w:r><w:t>xBC</w:t></w:r></w:p>",
        "old_txt": "BC",
        "new_txt": "xBC",
        "segs": [mk_seg("p1", 0, 0, 2)],
        "expected": [
            {"op": "insert", "target": [{"segId": "p1-000", "range": {"start": 0, "end": 0}}], "text": "x"},
        ],
    },
    # 7. Delete whole run
    {
        "name": "delete-whole-run",
        "before_xml": "<w:p><w:r><w:t>DELETE</w:t></w:r></w:p>",
        "after_xml": "<w:p/>",
        "old_txt": "DELETE",
        "new_txt": "",
        "segs": [mk_seg("p1", 0, 0, 6)],
        "expected": [
            {"op": "delete", "target": [{"segId": "p1-000", "range": {"start": 0, "end": 6}}], "text": "DELETE"},
        ],
    },
    # 8. Case change
    {
        "name": "case-change",
        "before_xml": "<w:p><w:r><w:t>A B</w:t></w:r></w:p>",
        "after_xml": "<w:p><w:r><w:t>a b</w:t></w:r></w:p>",
        "old_txt": "A B",
        "new_txt": "a b",
        "segs": [mk_seg("p1", 0, 0, 3)],
        "expected": [
            # {"op": "delete", "target": ["p1-000"], "range": {"start": 0, "end": 3}, "text": "A B"},
            # {"op": "insert", "target": ["p1-000"], "range": {"start": 0, "end": 0}, "text": "a b"},
            {"op": "replace", "target": [{"segId": "p1-000", "range": {"start": 0, "end": 1}}], "text": "a"},
            {"op": "replace", "target": [{"segId": "p1-000", "range": {"start": 2, "end": 3}}], "text": "b"},
            # {"op": "replace", "target": [{"segId": "p1-000", "range": {"start": 0, "end": 3}}], "text": "a b"},
        ],
    },
    # 9. Cross-run replace
    {
        "name": "cross-run replace",
        "before_xml": '<w:p w14:paraId="p42"><w:r><w:t>Hello </w:t></w:r><w:r><w:t>world</w:t></w:r></w:p>',
        "after_xml": '<w:p w14:paraId="p42"><w:r><w:t>Hi </w:t></w:r><w:r><w:t>GPT</w:t></w:r></w:p>',
        "old_txt": "Hello world",
        "new_txt": "Hi GPT",
        "segs": [mk_seg("p42", 0, 0, 6), mk_seg("p42", 1, 6, 11)],
        "expected": [
            {"op": "replace", "target": [{"segId": "p42-000", "range": {"start": 1, "end": 5}}], "text": "i"},
            {"op": "replace", "target": [{"segId": "p42-001", "range": {"start": 0, "end": 5}}], "text": "GPT"},
        ],
    },
    # 10. Run-boundary insert, ???
    {
        "name": "run-boundary insert",
        "before_xml": '<w:p w14:paraId="p42"><w:r><w:t>Hello </w:t></w:r><w:r><w:t>world</w:t></w:r></w:p>',
        "after_xml": '<w:p w14:paraId="p42">'
        "<w:r><w:t>Hello </w:t></w:r><w:r><w:t>awesome </w:t></w:r>"
        "<w:r><w:t>world</w:t></w:r></w:p>",
        "old_txt": "Hello world",
        "new_txt": "Hello awesome world",
        "segs": [mk_seg("p42", 0, 0, 6), mk_seg("p42", 1, 6, 11)],
        "expected": [
            {"op": "insert", "target": [{"segId": "p42-001", "range": {"start": 0, "end": 0}}], "text": "awesome "},
        ],
    },
    # 11. Paragraph-end insert
    {
        "name": "paragraph-end insert",
        "before_xml": "<w:p><w:r><w:t>end</w:t></w:r></w:p>",
        "after_xml": "<w:p><w:r><w:t>end!!!</w:t></w:r></w:p>",
        "old_txt": "end",
        "new_txt": "end!!!",
        "segs": [mk_seg("p1", 0, 0, 3)],
        "expected": [
            {"op": "insert", "target": [{"segId": "p1-000", "range": {"start": 2, "end": 2}}], "text": "!!!"},
        ],
    },
    # 12. Unicode γ insert
    {
        "name": "unicode-insert",
        "before_xml": "<w:p><w:r><w:t>αβ</w:t></w:r></w:p>",
        "after_xml": "<w:p><w:r><w:t>α𝛾β</w:t></w:r></w:p>",
        "old_txt": "αβ",
        "new_txt": "α𝛾β",
        "segs": [mk_seg("p1", 0, 0, 2)],
        "expected": [
            {"op": "insert", "target": [{"segId": "p1-000", "range": {"start": 1, "end": 1}}], "text": "𝛾"},
        ],
    },
    {
        "name": "underscore-and-digit",
        "before_xml": "<w:p><w:r><w:t>hello_world</w:t></w:r></w:p>",
        "after_xml": "<w:p><w:r><w:t>hello_world2</w:t></w:r></w:p>",
        "old_txt": "hello_world",
        "new_txt": "hello_world2",
        "segs": [mk_seg("p3", 0, 0, len("hello_world"))],
        "expected": [
            {
                "op": "insert",
                "target": [{"segId": "p3-000", "range": {"start": 11, "end": 11}}],
                "text": "2",
            },
        ],
    },
    {
        "name": "underscore-insert",
        "before_xml": "<w:p><w:r><w:t>fooBar</w:t></w:r></w:p>",
        "after_xml": "<w:p><w:r><w:t>foo_Bar</w:t></w:r></w:p>",
        "old_txt": "fooBar",
        "new_txt": "foo_Bar",
        "segs": [mk_seg("pU", 0, 0, len("fooBar"))],
        "expected": [
            {"op": "insert", "target": [{"segId": "pU-000", "range": {"start": 3, "end": 3}}], "text": "_"},
        ],
    },
    {
        "name": "hyphenated-word",
        "before_xml": "<w:p><w:r><w:t>mother-in-law</w:t></w:r></w:p>",
        "after_xml": "<w:p><w:r><w:t>mother-in-law test</w:t></w:r></w:p>",
        "old_txt": "mother-in-law",
        "new_txt": "mother-in-law test",
        "segs": [mk_seg("pH", 0, 0, len("mother-in-law"))],
        "expected": [
            {"op": "insert", "target": [{"segId": "pH-000", "range": {"start": 12, "end": 12}}], "text": " test"},
        ],
    },
    {
        "name": "insert-comma-after-example",
        "before_xml": (
            "<w:p><w:r><w:t>interests or positions of the parties, for example an apology,</w:t></w:r></w:p>"
        ),
        "after_xml": (
            "<w:p><w:r><w:t>interests or positions of the parties, for example, an apology,</w:t></w:r></w:p>"
        ),
        "old_txt": "interests or positions of the parties, for example an apology,",
        "new_txt": "interests or positions of the parties, for example, an apology,",
        "segs": [
            mk_seg("pX", 0, 0, len("interests or positions of the parties, for example an apology,"))  # 62
        ],
        "expected": [
            {"op": "insert", "target": [{"segId": "pX-000", "range": {"start": 50, "end": 50}}], "text": ","}
        ],
    },
    # 13. Full-width comma → dash  (delete + insert)
    {
        "name": "full-width comma → dash",
        "before_xml": "<w:p><w:r><w:t>你好，世界</w:t></w:r></w:p>",
        "after_xml": "<w:p><w:r><w:t>你好——世界</w:t></w:r></w:p>",
        "old_txt": "你好，世界",
        "new_txt": "你好——世界",
        "segs": [mk_seg("p1", 0, 0, len("你好，世界"))],
        "expected": [
            {"op": "replace", "target": [{"segId": "p1-000", "range": {"start": 2, "end": 3}}], "text": "——"},
            # {"op": "delete", "target": [{"segId": "p1-000", "range": {"start": 2, "end": 3}}], "text": "，"},
            # {"op": "insert", "target": [{"segId": "p1-000", "range": {"start": 2, "end": 2}}], "text": "——"},
        ],
    },
    # 14. Single dash → double dash
    {
        "name": "single-dash → double-dash",
        "before_xml": (
            "<w:p>"
            "<w:r><w:t>Client/Matter: </w:t></w:r>"  # Run-0
            "<w:r><w:t>-None-</w:t></w:r>"  # Run-1
            "</w:p>"
        ),
        "after_xml": ("<w:p><w:r><w:t>Client/Matter: </w:t></w:r><w:r><w:t>——None——</w:t></w:r></w:p>"),
        "old_txt": "Client/Matter: -None-",
        "new_txt": "Client/Matter: ——None——",
        "segs": [
            mk_seg("p1", 0, 0, 15),  # "Client/Matter: "
            mk_seg("p1", 1, 15, 21),  # "-None-"
        ],
        "expected": [
            # {"op": "delete", "target": [{"segId": "p1-001", "range": {"start": 0, "end": 1}}], "text": "-"},
            # {"op": "insert", "target": [{"segId": "p1-001", "range": {"start": 0, "end": 0}}], "text": "——"},
            # {"op": "delete", "target": [{"segId": "p1-001", "range": {"start": 5, "end": 6}}], "text": "-"},
            # {"op": "insert", "target": [{"segId": "p1-001", "range": {"start": 5, "end": 5}}], "text": "——"},
            {"op": "replace", "target": [{"segId": "p1-001", "range": {"start": 0, "end": 1}}], "text": "——"},
            {"op": "replace", "target": [{"segId": "p1-001", "range": {"start": 5, "end": 6}}], "text": "——"},
        ],
    },
    {
        "name": "strip-invalid-legis-note",
        "before_xml": "<w:p><w:r><w:t>Hello</w:t></w:r></w:p>",
        "after_xml": "<w:p><w:r><w:t>Hello (Legislation/jurisdiction not found in solr database)</w:t></w:r></w:p>",
        "old_txt": "Hello",
        "new_txt": "Hello (Legislation/jurisdiction not found in solr database)",
        "segs": [mk_seg("pL", 0, 0, 5)],
        "expected": [],
    },
    {
        "name": "strip-invalid-abbr-note",
        "before_xml": "<w:p><w:r><w:t>Hello</w:t></w:r></w:p>",
        "after_xml": "<w:p><w:r><w:t>Hello (wrong abbr detected)</w:t></w:r></w:p>",
        "old_txt": "Hello",
        "new_txt": "Hello (wrong abbr detected)",
        "segs": [mk_seg("pL", 0, 0, 5)],
        "expected": [],
    },
    {
        "name": "strip-invalid-legis-note2",
        "before_xml": "<w:p><w:r><w:t>Hello</w:t></w:r></w:p>",
        "after_xml": "<w:p><w:r><w:t>"
        "Hello (fullname) (Legislation/jurisdiction not found in solr database)"
        "</w:t></w:r></w:p>",
        "old_txt": "Hello",
        "new_txt": "Hello (fullname) (Legislation/jurisdiction not found in solr database)",
        "segs": [mk_seg("pL", 0, 0, 5)],
        "expected": [
            {
                "op": "insert",
                "target": [{"segId": "pL-000", "range": {"start": 4, "end": 4}}],
                "text": " (fullname)",
            }
        ],
    },
    {
        "name": "quotation-separate",
        "before_xml": ("<w:p><w:r><w:t>Here is a long quotation: This is the quotation sentence.</w:t></w:r></w:p>"),
        "after_xml": ("<w:p><w:r><w:t>Here is a long quotation:\n\nThis is the quotation sentence.</w:t></w:r></w:p>"),
        "old_txt": "Here is a long quotation: This is the quotation sentence.",
        "new_txt": "Here is a long quotation:\n\nThis is the quotation sentence.",
        "segs": [mk_seg("pQ", 0, 0, len("Here is a long quotation: This is the quotation sentence."))],
        "expected": [
            {
                "op": "quotationSeparate",
                "target": [
                    {
                        "segId": "pQ-000",
                        "range": {"start": 25, "end": 26},  # 替换掉原来的空格
                    }
                ],
                "text": "\n\n",
            }
        ],
    },    
    {
        "name": "quotation-separate-case-2",
        "before_xml": ("<w:p><w:r><w:t>Here is a long quotation: This is the quotation sentence.</w:t></w:r></w:p>"),
        "after_xml": ("<w:p><w:r><w:t>Here is a long quotation:\nThis is the quotation sentence.</w:t></w:r></w:p>"),
        "old_txt": "Here is a long quotation: This is the quotation sentence.",
        "new_txt": "Here is a long quotation:\nThis is the quotation sentence.",
        "segs": [mk_seg("pQ", 0, 0, len("Here is a long quotation: This is the quotation sentence."))],
        "expected": [
            {
                "op": "quotationSeparate",
                "target": [
                    {
                        "segId": "pQ-000",
                        "range": {"start": 25, "end": 26},  # 替换掉原来的空格
                    }
                ],
                "text": "\n",
            }
        ],
    },
    {
        "name": "need-space-after-note-strip",
        "before_xml": "<w:p><w:r><w:t>foobar</w:t></w:r></w:p>",
        "after_xml": ("<w:p><w:r><w:t>foo bar</w:t></w:r></w:p>"),
        "old_txt": "foobar",
        "new_txt": "foo(Legislation/jurisdiction not found in solr database)bar",
        "segs": [mk_seg("pS", 0, 0, len("foobar"))],
        "expected": [
            {
                "op": "replace",
                "target": [{"segId": "pS-000", "range": {"start": 0, "end": 6}}],
                "text": "foo bar",
            }
        ],
    },
    {
        "name": "no-extra-space-when-surrounded",
        "before_xml": "<w:p><w:r><w:t>foo bar</w:t></w:r></w:p>",
        "after_xml": (
            "<w:p><w:r><w:t>foo (Legislation/jurisdiction not found in solr database) bar</w:t></w:r></w:p>"
        ),
        "old_txt": "foo bar",
        "new_txt": "foo (Legislation/jurisdiction not found in solr database) bar",
        "segs": [mk_seg("pN", 0, 0, len("foo bar"))],
        "expected": [],
    },
    {
        "name": "quotation-plus-suffix",
        "old_txt": "foo bar",
        "new_txt": "foo:\n\nbar",
        "segs": [mk_seg("pZ", 0, 0, len("foo bar"))],
        "expected": [
            {
                "op": "replace",  # ":" 代替原空格
                "target": [{"segId": "pZ-000", "range": {"start": 3, "end": 4}}],
                "text": ":",
            },
            {
                "op": "quotationSeparate",
                "target": [{"segId": "pZ-000", "range": {"start": 4, "end": 4}}],
                "text": "\n\n",
            },
        ],
    },
    {
        "name": "quotation-plus-suffix2",
        "old_txt": "foo bar",
        "new_txt": "foo:\n\nbar2",
        "segs": [mk_seg("pZ", 0, 0, len("foo bar"))],
        "expected": [
            {
                "op": "replace",  # ":" 代替原空格
                "target": [{"segId": "pZ-000", "range": {"start": 3, "end": 4}}],
                "text": ":",
            },
            {
                "op": "quotationSeparate",
                "target": [{"segId": "pZ-000", "range": {"start": 4, "end": 4}}],
                "text": "\n\n",
            },
            {
                "op": "replace",
                "target": [{"segId": "pZ-000", "range": {"start": 5, "end": 7}}],
                "text": "bar2",
            },
        ],
    },
    # 15. NBSP vs normal space ────────────────────────────────────────────────────
    {
        "name": "nbsp-equals-space",
        # Word 里常见：Hello<NBSP>world
        "before_xml": "<w:p><w:r><w:t>Hello&#160;world</w:t></w:r></w:p>",
        "after_xml": "<w:p><w:r><w:t>Hello world</w:t></w:r></w:p>",
        "old_txt": "Hello\u00a0world",  # NBSP
        "new_txt": "Hello world",  # 普通空格
        "segs": [mk_seg("pNB", 0, 0, len("Hello world"))],
        "expected": [],
    },
    {
        "name": "single-dash → double-dash-bad-case",
        "before_xml": (
            "<w:p>"
            "<w:r><w:t>Client/Matter: </w:t></w:r>"  # Run-0
            "<w:r><w:t>-None-</w:t></w:r>"  # Run-1
            "</w:p>"
        ),
        "after_xml": ("<w:p><w:r><w:t>Client/Matter: </w:t></w:r><w:r><w:t>——None——</w:t></w:r></w:p>"),
        "old_txt": "Client/Matter: -None-",
        "new_txt": "<b>Client/Matter: </b>[1][Grammar Check Tool]—— None ——[2][Grammar Check Tool]",
        "segs": [
            mk_seg("p1", 0, 0, 15),  # "Client/Matter: "
            mk_seg("p1", 1, 15, 21),  # "-None-"
        ],
        "expected": [
            {"op": "replace", "target": [{"segId": "p1-001", "range": {"start": 0, "end": 1}}], "text": "—— "},
            {"op": "replace", "target": [{"segId": "p1-001", "range": {"start": 5, "end": 6}}], "text": " ——"},
        ],
    },
    {
        # 16. s7 → s 7（插空格）并去掉句点
        "name": "franchise-s7-correction",
        "old_txt": (
            "However, not all licensing arrangements are franchises. "
            "A licensing arrangement will only involve a franchise if the "
            "licence agreement satisfies the definition of “franchise agreement” in "
            "s7. of the Franchising Code of Conduct. "
        ),
        "new_txt": (
            "However, not all licensing arrangements are franchises. "
            "A licensing arrangement will only involve a franchise if the "
            "licence agreement satisfies the definition of “franchise agreement” in "
            "s 7[1][Grammar Check Tool] of the Franchising Code of Conduct."
        ),
        # 6 个 run，索引 0-5，刚好覆盖 0-228（len(old_txt)==228）
        "segs": [
            mk_seg("54C808AD", 0, 0, 117),  # “However, not all … the ”
            mk_seg("54C808AD", 1, 117, 124),  # “licence”
            mk_seg("54C808AD", 2, 124, 188),  # “ agreement … in ”
            mk_seg("54C808AD", 3, 188, 190),  # “s7”
            mk_seg("54C808AD", 4, 190, 192),  # “. ”
            mk_seg("54C808AD", 5, 192, 228),  # “of the Franchising … ”
        ],
        "expected": [
            {
                "op": "replace",
                "target": [{"segId": "54C808AD-003", "range": {"start": 0, "end": 2}}],
                "text": "s ",
            },
            {
                "op": "replace",
                "target": [{"segId": "54C808AD-004", "range": {"start": 0, "end": 1}}],
                "text": "7",
            },
            {"op": "delete", "target": [{"segId": "54C808AD-005", "range": {"start": 35, "end": 36}}], "text": " "},
        ],
    },
    {
        "name": "spelling-correction-particluar",
        "before_xml": "<w:p><w:r><w:t>In any particluar case, interpreting the parties' intentions depends not only on the context and language of the contract, but also the surrounding circumstances including the policy documents under consideration. For example, the language of the relevant policy in </w:t></w:r><w:r><w:t>Romero</w:t></w:r><w:r><w:t>, taken as a whole, was found to reflect an intention to create mutually binding contractual obligations. The specific obligations relied upon by the employee were clearly ascertainable and capable of precise identification. The court explained that:</w:t></w:r></w:p>",
        "after_xml": "<w:p><w:r><w:t>In any particular case, interpreting the parties' intentions depends not only on the context and language of the contract, but also the surrounding circumstances including the policy documents under consideration. For example, the language of the relevant policy in </w:t></w:r><w:r><w:t>Romero</w:t></w:r><w:r><w:t>, taken as a whole, was found to reflect an intention to create mutually binding contractual obligations. The specific obligations relied upon by the employee were clearly ascertainable and capable of precise identification. The court explained that:</w:t></w:r></w:p>",
        "old_txt": "In any particluar case, interpreting the parties' intentions depends not only on the context and language of the contract, but also the surrounding circumstances including the policy documents under consideration. For example, the language of the relevant policy in Romero, taken as a whole, was found to reflect an intention to create mutually binding contractual obligations. The specific obligations relied upon by the employee were clearly ascertainable and capable of precise identification. The court explained that:",
        "new_txt": "In any particular[Spelling Check Tool] case, interpreting the parties' intentions depends not only on the context and language of the contract, but also the surrounding circumstances including the policy documents under consideration. For example, the language of the relevant policy in <i>Romero</i>, taken as a whole, was found to reflect an intention to create mutually binding contractual obligations. The specific obligations relied upon by the employee were clearly ascertainable and capable of precise identification. The court explained that:",
        "segs": [
            mk_seg("5532B8B2", 0, 0, 3),  # "In "
            mk_seg("5532B8B2", 1, 3, 7),  # "any "
            mk_seg("5532B8B2", 2, 7, 17),  # "particluar"
            mk_seg("5532B8B2", 3, 17, 266),  # " case, interpreting...policy in "
            mk_seg("5532B8B2", 4, 266, 272),  # "Romero"
            mk_seg("5532B8B2", 5, 272, 522),  # ", taken as a whole...that:"
        ],
        "expected": [
            {"op": "insert", "target": [{"segId": "5532B8B2-002", "range": {"start": 6, "end": 6}}], "text": "u"},
            {"op": "delete", "target": [{"segId": "5532B8B2-002", "range": {"start": 7, "end": 8}}], "text": "u"},
        ],
    },
]


@pytest.mark.parametrize("case", cases, ids=[c["name"] for c in cases])
def test_build_edit_ops_full(case):
    got = strip_rev(build_edit_ops(case["old_txt"], case["new_txt"], case["segs"], REV))
    got_without_id = [{k: v for k, v in p.items() if k != "id"} for p in got]
    assert got_without_id == case["expected"], f"Failed for case: {case['name']}"
    # assert sorted(got, key=lambda d: (d["range"]["start"], d["op"])) == sorted(
    #     case["expected"], key=lambda d: (d["range"]["start"], d["op"])
    # )
