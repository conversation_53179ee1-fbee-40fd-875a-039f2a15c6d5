from unittest.mock import MagicMock
import pytest
from base.client.s3 import S3Client


@pytest.fixture
def s3_client(mock_boto3_client):
    return S3Client()

def test_put_object_success(s3_client, mock_boto3_client):
    mock_boto3_client().put_object.return_value = {"ResponseMetadata": {"HTTPStatusCode": 200}}

    result = s3_client.put_object("test-bucket", "test-file.txt", b"test data")
    assert result is True

def test_put_object_failure(s3_client, mock_boto3_client):
    mock_boto3_client().put_object.side_effect = Exception("Some error")

    result = s3_client.put_object("test-bucket", "test-file.txt", b"test data")
    assert result is False

def test_generate_presigned_url_success(s3_client, mock_boto3_client):
    mock_boto3_client().generate_presigned_url.return_value = "https://example.com"

    result = s3_client.generate_presigned_url("test-bucket", "test-key")

    assert result == "https://example.com"

def test_generate_presigned_url_failure(s3_client, mock_boto3_client):
    mock_boto3_client().generate_presigned_url.side_effect = Exception("Some error")

    result = s3_client.generate_presigned_url("test-bucket", "test-key")

    assert result is None

def test_download_object_success(s3_client, mock_boto3_client):
    mock_boto3_client().download_file.return_value = None

    result = s3_client.download_object("test-bucket", "test-key", "/path/to/file")

    assert result is True


def test_download_object_failure(s3_client, mock_boto3_client):
    mock_boto3_client().download_file.side_effect = Exception("Some error")

    result = s3_client.download_object("test-bucket", "test-key", "/path/to/file")

    assert result is False

def test_get_object_success(s3_client, mock_boto3_client):
    mock_boto3_client().get_object.return_value = {"Body": MagicMock(read=lambda: b"test data")}

    result = s3_client.get_object("test-bucket", "test-key")

    assert result == b"test data"

def test_get_object_failure(s3_client, mock_boto3_client):
    mock_boto3_client().get_object.side_effect = Exception("Some error")

    result = s3_client.get_object("test-bucket", "test-key")

    assert result is None