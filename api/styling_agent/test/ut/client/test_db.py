import pytest
from unittest.mock import MagicMock, patch
from base.client.db import DBClient
from base.error.client import DatabaseError


@pytest.fixture
def db_client():
    """Fixture to create a DBClient instance."""
    return DBClient(
        host="mock_host",
        database="mock_database",
        user="mock_user",
        password="mock_password",
        port=3306,
    )


@patch("pymysql.connect")
def test_connect_success(mock_connect, db_client):
    """Test DBClient.connect with a successful connection."""
    # Mock successful connection
    mock_connect.return_value = MagicMock()

    # Call the connect method
    db_client.connect()

    # Assertions
    mock_connect.assert_called_once_with(
        host="mock_host",
        database="mock_database",
        user="mock_user",
        password="mock_password",
        port=3306,
    )
    assert db_client.conn is not None


@patch("pymysql.connect")
def test_connect_failure(mock_connect, db_client):
    """Test DBClient.connect when connection fails."""
    # Mock connection failure
    mock_connect.side_effect = DatabaseError("Error connecting to MySQL: Connection failed")

    # Call the connect method and assert it raises DatabaseError
    with pytest.raises(DatabaseError, match="Error connecting to MySQL: Connection failed"):
        db_client.connect()

    mock_connect.assert_called_once()


@patch("pymysql.connect")
def test_query_success(mock_connect, db_client):
    """Test DBClient.query with a successful query."""
    # Mock connection and cursor
    mock_conn = MagicMock()
    mock_cursor = MagicMock()
    mock_cursor.fetchall.return_value = [("result1",), ("result2",)]
    mock_conn.cursor.return_value.__enter__.return_value = mock_cursor
    mock_connect.return_value = mock_conn

    # Call the query method
    sql = "SELECT * FROM mock_table WHERE id = %s"
    params = (1,)
    results = db_client.query(sql, params)

    # Assertions
    assert results == [("result1",), ("result2",)]
    mock_cursor.execute.assert_called_once_with(sql, params)
    mock_cursor.fetchall.assert_called_once()
    mock_conn.close.assert_called_once()


@patch("pymysql.connect")
def test_query_failure(mock_connect, db_client):
    """Test DBClient.query when query execution fails."""
    # Mock connection and cursor
    mock_conn = MagicMock()
    mock_cursor = MagicMock()
    mock_cursor.execute.side_effect = DatabaseError("Error executing query: Query failed")
    mock_conn.cursor.return_value.__enter__.return_value = mock_cursor
    mock_connect.return_value = mock_conn

    # Call the query method and assert it raises DatabaseError
    sql = "SELECT * FROM mock_table WHERE id = %s"
    params = (1,)
    with pytest.raises(DatabaseError, match="Error executing query: Query failed"):
        db_client.query(sql, params)

    mock_cursor.execute.assert_called_once_with(sql, params)
    mock_conn.close.assert_called_once()


@patch("pymysql.connect")
def test_close_connection(mock_connect, db_client):
    """Test DBClient.close to ensure the connection is closed."""
    # Mock connection
    mock_conn = MagicMock()
    mock_connect.return_value = mock_conn

    # Call connect and close
    db_client.connect()
    db_client.close()

    # Assertions
    mock_conn.close.assert_called_once()
    assert db_client.conn is None
