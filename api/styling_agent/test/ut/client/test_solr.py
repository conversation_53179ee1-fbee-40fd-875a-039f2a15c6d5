import pytest
from unittest.mock import patch, MagicMock
from base.client.solr import SolrClient
from base.error.client import SolrClientError
import requests


@pytest.fixture
def solr_client():
    """Fixture to create a SolrClient instance."""
    return SolrClient(url="https://mock-solr-url/solr")


@patch("requests.get")
def test_query_success(mock_get, solr_client):
    """Test SolrClient.query with a successful response."""
    # Mock Solr response
    mock_response = MagicMock()
    mock_response.status_code = 200
    mock_response.json.return_value = {
        "response": {
            "docs": [{"officialtitle": "Test Act 2023", "jurisdiction": "NSW"}],
            "numFound": 1,
        }
    }
    mock_get.return_value = mock_response

    # Query parameters
    params = {"q": 'officialtitle:"Test Act*"', "rows": 10, "wt": "json"}

    # Call the query method
    result = solr_client.query(params)

    # Assertions
    assert result["response"]["numFound"] == 1
    assert result["response"]["docs"][0]["officialtitle"] == "Test Act 2023"
    assert result["response"]["docs"][0]["jurisdiction"] == "NSW"
    mock_get.assert_called_once_with("https://mock-solr-url/solr/select", params=params, timeout=30)


@patch("requests.get")
def test_query_request_error(mock_get, solr_client):
    """Test SolrClient.query when a RequestException occurs."""
    # Simulate a RequestException
    mock_get.side_effect = requests.exceptions.RequestException("Connection error")

    # Query parameters
    params = {"q": 'officialtitle:"Test Act*"', "rows": 10, "wt": "json"}

    # Call the query method and assert it raises SolrClientError
    with pytest.raises(SolrClientError, match="Request error: Connection error"):
        solr_client.query(params)

    mock_get.assert_called_once_with("https://mock-solr-url/solr/select", params=params, timeout=30)


@patch("requests.get")
def test_query_unknown_error(mock_get, solr_client):
    """Test SolrClient.query when an unknown exception occurs."""
    # Simulate an unknown exception
    mock_get.side_effect = Exception("Unknown error")

    # Query parameters
    params = {"q": 'officialtitle:"Test Act*"', "rows": 10, "wt": "json"}

    # Call the query method and assert it raises SolrClientError
    with pytest.raises(SolrClientError, match="Unknown Error: Unknown error"):
        solr_client.query(params)

    mock_get.assert_called_once_with("https://mock-solr-url/solr/select", params=params, timeout=30)
