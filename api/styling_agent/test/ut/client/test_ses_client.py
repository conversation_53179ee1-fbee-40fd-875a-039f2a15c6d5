import pytest
from unittest.mock import patch, MagicMock
from base.client.ses import SESClient

def test_send_email_success():
    with patch("base.client.ses.boto3.client") as mock_boto_client:
        mock_ses_client = MagicMock()
        mock_boto_client.return_value = mock_ses_client
        mock_ses_client.send_raw_email.return_value = {"MessageId": "12345"}

        ses_client = SESClient(sender="<EMAIL>")
        result = ses_client.send_email(
            recipients=["<EMAIL>"],
            subject="Test Subject",
            content="Test Content",
        )

        assert result is True
        mock_ses_client.send_raw_email.assert_called_once()


def test_send_email_failure():
    with patch("base.client.ses.boto3.client") as mock_boto_client:
        mock_ses_client = MagicMock()
        mock_boto_client.return_value = mock_ses_client
        mock_ses_client.send_raw_email.side_effect = Exception("SES Error")

        ses_client = SESClient(sender="<EMAIL>")
        result = ses_client.send_email(
            recipients=["<EMAIL>"],
            subject="Test Subject",
            content="Test Content",
        )

        assert result is False
        mock_ses_client.send_raw_email.assert_called_once()


def test_send_email_with_attachments():
    with patch("base.client.ses.boto3.client") as mock_boto_client:
        mock_ses_client = MagicMock()
        mock_boto_client.return_value = mock_ses_client
        mock_ses_client.send_raw_email.return_value = {"MessageId": "12345"}

        ses_client = SESClient(sender="<EMAIL>")
        result = ses_client.send_email(
            recipients=["<EMAIL>"],
            subject="Test Subject",
            content="Test Content",
            attachments={"test.txt": b"Sample content"},
        )

        assert result is True
        mock_ses_client.send_raw_email.assert_called_once()


@pytest.fixture
def ses_client():
    return SESClient(sender="<EMAIL>")


def test_get_attachment_name_basic(ses_client):
    result = ses_client._get_attachment_name("test_file.docx")
    assert result == "test_file.docx"

    result = ses_client._get_attachment_name("uVic — County Court — Form, content and filing of originating processes_original_1748246890.docx'")
    assert result == 'uVic  County Court  Form  content and filing of originating processes_original_1748246890.docx'


def test_get_attachment_name_non_ascii(ses_client):
    result = ses_client._get_attachment_name("tést_fïle.docx")
    assert result == "test_file.docx"


def test_get_attachment_name_special_characters(ses_client):
    result = ses_client._get_attachment_name("test@file#name!—.docx")
    assert result == "test file name .docx"


def test_get_attachment_name_empty_name(ses_client):
    result = ses_client._get_attachment_name("")
    assert result == "unnamed_file"


def test_get_attachment_name_exceeds_max_length(ses_client):
    long_name = "a" * 300 + ".docx"
    result = ses_client._get_attachment_name(long_name, max_length=255)
    assert len(result) <= 255
    assert result.endswith(".docx")


def test_get_attachment_name_truncate_at_last_allowed_char(ses_client):
    long_name = "a" * 250 + "-test.docx"
    result = ses_client._get_attachment_name(long_name, max_length=255)
    assert result.endswith(".docx")
    assert len(result) <= 255


def test_get_attachment_name_only_special_characters(ses_client):
    result = ses_client._get_attachment_name("@#$%^&*.docx")
    assert result == "unnamed_file.docx"

    result = ses_client._get_attachment_name("@#$%^&*")
    assert result == "unnamed_file"