import json
from unittest.mock import patch, MagicMock
import pytest
from base.client.aws_lambda import LambdaClient


@pytest.fixture
def lambda_client(mock_boto3_client):
    return LambdaClient()

def test_invoke_success(lambda_client, mock_boto3_client):
    mock_response = {
        "Payload": MagicMock(read=MagicMock(return_value=json.dumps({"key": "value"}).encode("utf-8")))
    }
    mock_boto3_client().invoke.return_value = mock_response

    result = lambda_client.invoke("test_function", '{"test": "data"}')

    assert result == {"key": "value"}

def test_invoke_json_decode_error(lambda_client, mock_boto3_client):
    mock_lambda = MagicMock()
    mock_boto3_client.return_value = mock_lambda
    mock_response = {
        "Payload": MagicMock(read=MagicMock(return_value="invalid json".encode("utf-8")))
    }
    mock_lambda.invoke.return_value = mock_response

    result = lambda_client.invoke("test_function", '{"test": "data"}')

    assert result is None

def test_invoke_general_exception(lambda_client, mock_boto3_client):
    mock_lambda = MagicMock()
    mock_boto3_client.return_value = mock_lambda
    mock_lambda.invoke.side_effect = Exception("Some error")

    result = lambda_client.invoke("test_function", '{"test": "data"}')

    assert result is None