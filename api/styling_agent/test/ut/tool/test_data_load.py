from unittest.mock import MagicMock, patch
from base.tool.data_load import DataLoadTool
import pytest
import json


@pytest.fixture
def mock_lambda_client():
    """Mock the boto3 Lambda client."""
    with patch("base.tool.data_load.lambda_client") as mock_client:
        yield mock_client


@pytest.fixture
def mock_s3_client():
    """Mock the boto3 S3 client."""
    with patch("base.tool.data_load.s3") as mock_client:
        yield mock_client


@pytest.fixture(autouse=True)
def setup_env(monkeypatch):
    """Automatically set JOB_PREFIX for all tests."""
    monkeypatch.setenv("JOB_PREFIX", "unit-test")


def test_run_valid_s3_path(mock_lambda_client, monkeypatch):
    monkeypatch.setenv("JOB_PREFIX", "unit-test")
    # Mock Lambda response
    mock_lambda_client.invoke.return_value = {
        "Payload": MagicMock(read=MagicMock(return_value=json.dumps({"Data": "<xml>content</xml>"}).encode("utf-8"))),
        "StatusCode": 200,
    }

    # Prepare test data
    tool = DataLoadTool()
    tool._parameters = {"path": "s3://test-bucket/test-key"}

    result = tool.run()
    assert result == "<xml>content</xml>"  # 如果你期望的是 bytes，可以改回 b"<xml>content</xml>"
    mock_lambda_client.invoke.assert_called_once_with(
        FunctionName="5633-pg-local-wordtool-ConvertHandler",
        Payload=json.dumps({"Bucket": "test-bucket", "Key": "test-key"}),
    )


def test_run_invalid_path(monkeypatch):
    tool = DataLoadTool()
    tool._parameters = {"path": "file:///local/path/doc.docx"}

    with pytest.raises(ValueError, match="Unsupported path format: file:///local/path/doc.docx"):
        tool.run()


def test_run_lambda_failure(mock_lambda_client):
    lambda_response = {"StatusCode": 200, "ExecutedVersion": "$LATEST", "Payload": MagicMock()}
    lambda_response["Payload"].read.return_value = json.dumps(
        {
            "errorMessage": "Lambda execution error",
            "errorType": "RuntimeError",
        }
    ).encode("utf-8")

    mock_lambda_client.invoke.return_value = lambda_response

    tool = DataLoadTool()
    tool._parameters = {"path": "s3://test-bucket/test-key"}

    try:
        tool.run()
    except Exception as e:
        assert str(e) == 'No revision found for the given document. (error code: C002)'


def test_run_missing_path(monkeypatch):
    """Test the run method when path parameter is missing."""
    monkeypatch.setenv("JOB_PREFIX", "unit-test")

    tool = DataLoadTool()

    # 验证抛出异常
    with pytest.raises(KeyError, match="'path'"):
        tool.run()
