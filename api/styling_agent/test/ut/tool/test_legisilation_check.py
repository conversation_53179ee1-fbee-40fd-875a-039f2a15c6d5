from unittest.mock import MagicMock, patch
from base.tool.legislation_check import LegislationCheckTool
from base.prompt.tooling_prompt import prompt_legislation_check
import pytest
import json

import yaml


@pytest.fixture
def mock_lambda_client():
    """Mock the boto3 Lambda client."""
    with patch("base.tool.data_load.lambda_client") as mock_client:
        yield mock_client


@pytest.fixture
def mock_s3_client():
    """Mock the boto3 S3 client."""
    with patch("base.tool.data_load.s3") as mock_client:
        yield mock_client


@pytest.fixture(autouse=True)
def setup_env(monkeypatch):
    """Automatically set JOB_PREFIX for all tests."""
    monkeypatch.setenv("JOB_PREFIX", "unit-test")


def test_prompt_template():
    tool = LegislationCheckTool()
    text = "This is a test text."
    assert tool.prompt_template(text) == prompt_legislation_check.format(text)


def test_post_process() -> None:
    """
    Test the post_process method of LegislationCheckTool.
    """
    tool = LegislationCheckTool()
    yaml_text = """
- legislation_extract_result:
  text: Privacy Act
  tool_to_fix: add_jurisdiction
    """
    json_text = tool.post_process(yaml_text)
    expected_json = [
        {
            "legislation_extract_result": None,
            "text": "Privacy Act",
            "tool_to_fix": "add_jurisdiction",
        }
    ]
    assert json.loads(json_text) == expected_json, "Post-process output does not match the expected JSON."


def test_post_process_invalid_yaml():
    """
    Test the post_process method of LegislationCheckTool with invalid YAML input.
    """
    tool = LegislationCheckTool()
    invalid_yaml_text = "This is not valid YAML."

    with patch("base.util.convert.yaml.safe_load") as mock_safe_load:
        mock_safe_load.side_effect = yaml.YAMLError("YAML error")
        result = tool.post_process(invalid_yaml_text)
        assert result == invalid_yaml_text, invalid_yaml_text


def test_post_process_empty_yaml():
    """
    Test the post_process method of LegislationCheckTool with empty YAML input.
    """
    tool = LegislationCheckTool()
    empty_yaml_text = ""

    with patch("base.util.convert.yaml.safe_load") as mock_safe_load:
        mock_safe_load.return_value = None
        result = tool.post_process(empty_yaml_text)
        assert result == empty_yaml_text, empty_yaml_text


def test_prompt_template():
    tool = LegislationCheckTool()
    text = "This is a test text."
    assert tool.prompt_template(text) == prompt_legislation_check.format(text)


def test_run_functionality_without_llm():
    tool = LegislationCheckTool()
    tool._parameters = {
        "para_id": "test_id",
        "para_content": "This is a test content.",
    }
    tool.llm_predict = MagicMock(return_value=json.dumps({"legislation_extract_result": []}))
    result = tool.run()
    assert result["para_id"] == "test_id"
    assert result["para_content"] == "This is a test content."
    assert tool.llm_predict.called


def test_error_in_llm_predict():
    tool = LegislationCheckTool()
    tool._parameters = {
        "para_id": "test_id",
        "para_content": "This is a test content.",
    }
    tool.llm_predict = MagicMock(side_effect=Exception("LLM error"))
    result = tool.run()
    assert result["para_id"] == "test_id"
    assert result["para_content"] == "This is a test content."
    assert tool.llm_predict.called


@patch.object(LegislationCheckTool, "llm_predict")
def test_na_with_two_words(mock_llm_predict):
    mock_llm_predict.return_value = '{"legislation_extract_result":[{"text":"Privacy Act","tool_to_fix":"n/a"}]}'
    tool = LegislationCheckTool()
    tool._parameters = {
        "para_id": "para1",
        "para_content": "The Privacy Act.",
    }
    result = tool.run()

    assert "Privacy Act" in result["para_content"]
    assert "(Privacy Act)" not in result["para_content"]


@patch.object(LegislationCheckTool, "llm_predict")
def test_na_with_comma(mock_llm_predict):
    mock_llm_predict.return_value = '{"legislation_extract_result":[{"text":"Privacy Act, 2023","tool_to_fix":"n/a"}]}'
    tool = LegislationCheckTool()
    tool._parameters = {
        "para_id": "para2",
        "para_content": "The Privacy Act, 2023.",
    }
    result = tool.run()

    assert "Privacy Act, 2023" in result["para_content"]
    assert "(Privacy Act, 2023)" not in result["para_content"]
