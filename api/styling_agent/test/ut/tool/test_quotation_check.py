import json
import pytest
from unittest.mock import MagicMock, patch
from base.prompt.tooling_prompt import prompt_quote_check
from base.tool.quotation_check import QuotationCheckTool


@pytest.fixture(autouse=True)
def setup_env(monkeypatch):
    monkeypatch.setenv("JOB_PREFIX", "unit-test")
    monkeypatch.setenv("ENV", "dev")


def test_prompt_template():
    tool = QuotationCheckTool()
    input_text = "key: value"
    assert tool.prompt_template(input_text) == prompt_quote_check.format(input_text)


def test_post_process() -> None:
    """
    Test the post_process method of LegislationCheckTool.
    """
    tool = QuotationCheckTool()
    yaml_text = """
- legislation_extract_result:
  text: Privacy Act
  tool_to_fix: add_jurisdiction
    """
    json_text = tool.post_process(yaml_text)
    expected_json = [
        {
            "legislation_extract_result": None,
            "text": "Privacy Act",
            "tool_to_fix": "add_jurisdiction",
        }
    ]
    assert json.loads(json_text) == expected_json, "Post-process output does not match the expected JSON."


def test_run_basic_functionality():
    tool = QuotationCheckTool()
    tool._parameters = {
        "para_id": "p3",
        "para_content": "These service providers will be required to take ‘reasonable steps’ to prevent, detect, report, respond, and disrupt scams while using their services.",
    }
    tool.llm_predict = MagicMock(return_value="{}")
    tool.post_process = MagicMock(return_value=json.dumps({"quotation_check_result": []}))

    result = tool.run()
    assert result["para_id"] == "p3"
    assert (
        result["para_content"]
        == "These service providers will be required to take ‘reasonable steps’ to prevent, detect, report, respond, and disrupt scams while using their services."
    )


@patch.object(QuotationCheckTool, "llm_predict")
def test_with_replace_quotes(mock_llm_predict):
    mock_llm_predict.return_value = (
        '{"quotation_check_result": [{"text": "CDR consumer", "tool_to_fix": "replace_quotes"}]}'
    )
    tool = QuotationCheckTool()
    tool._parameters = {
        "para_id": "p3",
        "para_content": "the definition of a ‘CDR consumer’  encompasses both individuals and business.",
    }
    expected = "the definition of a ‘CDR consumer’  encompasses both individuals and business."
    result = tool.run()
    assert result["para_id"] == "p3"
    assert "'" not in result["para_content"]
    assert "'" not in result["para_content"]
    assert " ‘CDR consumer’ " in result["para_content"]
    assert result["para_content"] == expected


@patch.object(QuotationCheckTool, "llm_predict")
def test_with_add_quotes(mock_llm_predict):
    mock_llm_predict.return_value = (
        '{"quotation_check_result": [{"text": "cognitive engagement", "tool_to_fix": "add_quotes"}]}'
    )
    tool = QuotationCheckTool()
    tool._parameters = {
        "para_id": "p3",
        "para_content": "In this study, cognitive engagement refers to sustained attention during learning tasks.",
    }
    expected = "In this study, cognitive engagement refers to sustained attention during learning tasks."
    result = tool.run()
    assert result["para_id"] == "p3"
    assert 'cognitive engagement' in result["para_content"]
    assert result["para_content"] == expected


@patch.object(QuotationCheckTool, "llm_predict")
def test_with_create_new_block(mock_llm_predict):
    mock_llm_predict.return_value = '{"quotation_check_result": [{"text": "The doctrine of communal native title, as understood in other jurisdictions, does not form part of the common law of Australia. This conclusion is based on the absence of any recognition of such rights in the colonial legal framework.", "tool_to_fix": "create_new_block"}]}'
    tool = QuotationCheckTool()
    tool._parameters = {
        "para_id": "p3",
        "para_content": "In Milirrpum v Nabalco Pty Ltd, Blackburn J stated  “The doctrine of communal native title, as understood in other jurisdictions, does not form part of the common law of Australia. This conclusion is based on the absence of any recognition of such rights in the colonial legal framework.”",
    }
    expected = 'In Milirrpum v Nabalco Pty Ltd, Blackburn J stated  “The doctrine of communal native title, as understood in other jurisdictions, does not form part of the common law of Australia. This conclusion is based on the absence of any recognition of such rights in the colonial legal framework.”'
    result = tool.run()
    assert result["para_id"] == "p3"
    assert result["para_content"] == expected


def test_error_handling():
    tool = QuotationCheckTool()
    tool._parameters = {
        "para_id": "p3",
        "para_content": "This is a test paragraph.",
    }
    tool.llm_predict = MagicMock(side_effect=Exception("LLM error"))
    tool.post_process = MagicMock(return_value=json.dumps({"quotation_check_result": []}))

    result = tool.run()
    assert result["para_id"] == "p3"
    assert result["para_content"] == "This is a test paragraph."
