from unittest.mock import patch
from base.tool.abbr import AbbrTool
import pytest


@pytest.fixture(autouse=True)
def setup_env(monkeypatch):
    """Automatically set JOB_PREFIX for all tests."""
    monkeypatch.setenv("JOB_PREFIX", "unit-test")


@patch.object(AbbrTool, "llm_predict")
def test_run_with_add_abbd_mapping(mock_llm_predict):
    mock_llm_predict.return_value = '{"abbreviation_check_result":[{"text":"Australian Competition and Consumer Commission","tool_to_fix":"add_abbr_mapping"}]}'
    tool = AbbrTool()
    tool._parameters = {
        "para_id": "para1",
        "para_content": "Door to door sales and other non-solicited sales activities are also governed by the provisions of the ACL. These provisions provide cooling off rights for unsolicited consumer agreements. Guidance on this area is available from the Australian Competition and Consumer Commission website.",
    }
    expected_result = "Door to door sales and other non-solicited sales activities are also governed by the provisions of the ACL. These provisions provide cooling off rights for unsolicited consumer agreements. Guidance on this area is available from the Australian Competition and Consumer Commission (ACCC) website."
    result = tool.run()

    assert result["para_id"] == "para1"
    assert result["para_content"] == tool._parameters["para_content"]


@patch.object(AbbrTool, "llm_predict")
def test_run_with_change_to_abbr(mock_llm_predict):
    mock_llm_predict.return_value = '{"abbreviation_check_result":[{"text":"section","tool_to_fix":"change_to_abbr"}]}'
    tool = AbbrTool()
    tool._parameters = {
        "para_id": "para1",
        "para_content": "Pursuant to section 661A of the Corporations Act 2001 (Cth) (Corporations Act) a bidder under an off-market takeover bid may compulsorily acquire any securities in the bid class if during, or at the end of, the offer period the bidder and its associates have:",
    }
    expected_result = "Pursuant to section 661A of the Corporations Act 2001 (Cth) (Corporations Act) a bidder under an off-market takeover bid may compulsorily acquire any securities in the bid class if during, or at the end of, the offer period the bidder and its associates have:"
    result = tool.run()
    print(result)
    assert result["para_id"] == "para1"
    assert result["para_content"] == expected_result


@patch.object(AbbrTool, "llm_predict")
def test_run_with_abbr_caution(mock_llm_predict):
    mock_llm_predict.return_value = '{"abbreviation_check_result":[{"text":"CDR","tool_to_fix":"abbr_caution"}]}'
    tool = AbbrTool()
    tool._parameters = {
        "para_id": "para1",
        "para_content": "CDR regime operates under a co-regulator model. At the commencement of the CDR regime the lead regulator was the ACCC.",
    }
    expected_result = "CDR regime operates under a co-regulator model. At the commencement of the CDR regime the lead regulator was the ACCC."
    result = tool.run()
    print(result["para_content"])
    assert result["para_id"] == "para1"
    assert result["para_content"] == expected_result
