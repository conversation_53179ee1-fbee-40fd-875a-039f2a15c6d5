from base.tool.word_count_check import WordCountCheckTool

# Minimal mock XML structure required for testing
mock_xml_content = {
    "Elements": [
        {
            "Key": "ParagraphElement",
            "ElementType": "Paragraph",
            "ElementId": "para1",
            "PlainText": "This is a test paragraph.",
            "Properties": {"Style": ""},
            "Segments": [
                {
                    "SegmentId": "seg1",
                    "Start": 0,
                    "End": 30,
                }
            ],
        }
    ]
}
# {
#   "Version": "1.0",
#   "DocumentId": "598068c3-d0f7-4770-8313-247dac455394.temp.dox",
#   "Elements": [
#     {
#       "Key": "ParagraphElement",
#       "ElementType": "Paragraph",
#       "ElementId": "04FEAC3C",
#       "PlainText": "",
#       "Properties": {
#         "Style": ""
#       },
#       "Segments": []
#     },


def test_word_count_below_limit():
    """Test case when word count is below limit"""
    text = "word " * 2499  # 2499 words
    tool = WordCountCheckTool(fulltext=text.strip(), xml_content=mock_xml_content)
    result = tool.run()
    assert len(result) == 1
    assert result[0]["patches"][0]["comment"]["text"] == "Word count (2499) is within the limit (2500)."


def test_word_count_above_limit():
    """Test case when word count exceeds limit"""
    text = "word " * 2501  # 2501 words
    tool = WordCountCheckTool(fulltext=text.strip(), xml_content=mock_xml_content)
    result = tool.run()
    assert len(result) == 1
    assert (
        result[0]["patches"][0]["comment"]["text"]
        == "Word count (2501) exceeds the limit (2500). Please consider reducing the content."
    )
