import pytest
import json
from unittest.mock import MagicMock, patch, Mock
from base.tool.revision_reg import RevisionRegTool
from base.prompt.tooling_prompt import prompt_revision_reg_check


@pytest.fixture(autouse=True)
def setup_env(monkeypatch):
    monkeypatch.setenv("JOB_PREFIX", "unit-test")


@pytest.fixture
def sample_revision_paragraphs():
    """提供测试用的 revision paragraphs XML 数据"""
    return [
        '<Xml><Paragraph paraId="p1">First paragraph content</Paragraph></Xml>',
        '<Xml><Paragraph paraId="p2">Second paragraph content</Paragraph></Xml>'
    ]


@pytest.fixture
def sample_xml_content():
    """提供测试用的 XML 内容"""
    return {"Elements": [{"ElementId": "p1", "Text": "Test content"}]}


class TestRevisionRegTool:
    """RevisionRegTool 测试类"""

    def test_prompt_template(self):
        """测试 prompt_template 方法"""
        tool = RevisionRegTool()
        input_text = "key: value"
        result = tool.prompt_template(input_text)
        expected = prompt_revision_reg_check.format(input_text)
        assert result == expected

    def test_post_process_with_empty_input(self):
        """测试空输入的 post_process"""
        tool = RevisionRegTool()
        result = tool.post_process("")
        assert result == []

    def test_post_process_with_none_input(self):
        """测试 None 输入的 post_process"""
        tool = RevisionRegTool()
        result = tool.post_process(None)
        assert result == []

    def test_post_process_with_invalid_json(self):
        """测试无效 JSON 的 post_process"""
        tool = RevisionRegTool()
        invalid_json = "{ missing closing bracket"
        
        with patch("base.tool.revision_reg.convert_yaml_to_json_if_possible") as mock_convert:
            mock_convert.return_value = invalid_json
            
            result = tool.post_process(invalid_json)
            
            assert result == []
            mock_convert.assert_called_once_with(invalid_json)

    def test_post_process_with_convert_exception(self):
        """测试转换异常的 post_process"""
        tool = RevisionRegTool()
        input_str = "Some input"
        
        with patch("base.tool.revision_reg.convert_yaml_to_json_if_possible") as mock_convert:
            mock_convert.side_effect = Exception("Conversion error")
            
            result = tool.post_process(input_str)
            
            assert result == []
            mock_convert.assert_called_once_with(input_str)

    def test_post_process_with_valid_yaml(self):
        """测试有效 YAML 的 post_process"""
        tool = RevisionRegTool()
        valid_yaml = """
        - para_id: "p1"
          quotation: true
          link: false
          abbr: true
        """
        
        result = tool.post_process(valid_yaml)
        
        expected_result = [{
            "para_id": "p1",
            "quotation": True,
            "link": False,
            "abbr": True
        }]
        assert result == expected_result

    def test_run_with_empty_parameters(self):
        """测试空参数的 run 方法"""
        tool = RevisionRegTool()
        tool._parameters = {}
        
        result = tool.run()
        
        assert result == []

    def test_run_with_no_revision_paragraphs(self):
        """测试没有 revision_paragraphs_xml 的 run 方法"""
        tool = RevisionRegTool()
        tool._parameters = {
            "xml_content": {"Elements": []},
            "revision_paragraphs_xml": None
        }
        
        result = tool.run()
        
        assert result == []

    @patch("base.tool.revision_reg.extract_para_content")
    @patch("base.tool.revision_reg.extract_para_styles")
    @patch("base.tool.revision_reg.extract_para_links")
    def test_run_with_valid_data(self, mock_extract_links, mock_extract_styles, mock_extract_content, sample_revision_paragraphs, sample_xml_content):
        """测试有效数据的 run 方法"""
        # 设置 mocks
        mock_extract_content.return_value = "Test paragraph content"
        mock_extract_styles.return_value = ("<pStyle>Normal</pStyle>", "<rStyle>Bold</rStyle>")
        mock_extract_links.return_value = ["https://example.com"]
        
        # 创建工具实例
        tool = RevisionRegTool()
        tool._parameters = {
            "revision_paragraphs_xml": sample_revision_paragraphs,
            "xml_content": sample_xml_content
        }
        
        # Mock LLM 预测结果
        llm_response = """
        - para_id: "p1"
          quotation: false
          link: true
          abbr: false
          capital: true
          leg_ac: false
          heading: false
        """
        
        with patch.object(tool, 'llm_predict') as mock_llm_predict:
            mock_llm_predict.return_value = llm_response
            
            result = tool.run()
            
            # 验证结果
            assert len(result) == len(sample_revision_paragraphs)
            assert result[0]["para_id"] == "p1"
            assert result[0]["para_content"] == "Test paragraph content"
            assert result[0]["quotation"] is False
            assert result[0]["link"] is True
            assert result[0]["abbr"] is False
            assert result[0]["capital"] is True
            assert result[0]["leg_ac"] is False
            assert result[0]["heading"] is False
            assert result[0]["par_style_text"] == "<pStyle>Normal</pStyle>"
            assert result[0]["run_style_text"] == "<rStyle>Bold</rStyle>"
            assert result[0]["links"] == ["https://example.com"]
            
            # 验证 LLM 被调用的次数
            assert mock_llm_predict.call_count == len(sample_revision_paragraphs)

    @patch("base.tool.revision_reg.extract_para_content")
    @patch("base.tool.revision_reg.extract_para_styles") 
    @patch("base.tool.revision_reg.extract_para_links")
    def test_run_with_llm_prediction_error(self, mock_extract_links, mock_extract_styles, mock_extract_content, sample_revision_paragraphs, sample_xml_content):
        """测试 LLM 预测错误的情况"""
        # 设置 mocks
        mock_extract_content.return_value = "Test content"
        mock_extract_styles.return_value = ("", "")
        mock_extract_links.return_value = []
        
        tool = RevisionRegTool()
        tool._parameters = {
            "revision_paragraphs_xml": sample_revision_paragraphs,
            "xml_content": sample_xml_content
        }
        
        # Mock LLM 预测抛出异常
        with patch.object(tool, 'llm_predict') as mock_llm_predict:
            mock_llm_predict.side_effect = Exception("LLM prediction error")
            
            result = tool.run()
            
            # 应该返回空列表，因为所有预测都失败了
            assert result == []

    @patch("base.tool.revision_reg.extract_para_content")
    @patch("base.tool.revision_reg.extract_para_styles")
    @patch("base.tool.revision_reg.extract_para_links")
    def test_run_with_invalid_para_id(self, mock_extract_links, mock_extract_styles, mock_extract_content, sample_revision_paragraphs, sample_xml_content):
        """测试无效 para_id 的情况"""
        # 设置 mocks
        mock_extract_content.return_value = "Test content"
        mock_extract_styles.return_value = ("", "")
        mock_extract_links.return_value = []
        
        tool = RevisionRegTool()
        tool._parameters = {
            "revision_paragraphs_xml": sample_revision_paragraphs,
            "xml_content": sample_xml_content
        }
        
        # Mock LLM 返回无效的 para_id
        llm_response = """
        - para_id: 123  # 数字而不是字符串
          quotation: false
          link: false
        - para_id: null  # null 值
          quotation: true
        - quotation: false  # 缺少 para_id
          link: true
        """
        
        with patch.object(tool, 'llm_predict') as mock_llm_predict:
            mock_llm_predict.return_value = llm_response
            
            result = tool.run()
            
            # 应该过滤掉无效的条目
            assert result == []

    def test_run_with_post_process_failure(self, sample_revision_paragraphs, sample_xml_content):
        """测试 post_process 失败的情况"""
        tool = RevisionRegTool()
        tool._parameters = {
            "revision_paragraphs_xml": sample_revision_paragraphs,
            "xml_content": sample_xml_content
        }
        
        with patch.object(tool, 'llm_predict') as mock_llm_predict, \
             patch.object(tool, 'post_process') as mock_post_process:
            
            mock_llm_predict.return_value = "some response"
            mock_post_process.return_value = []  # post_process 返回空列表
            
            result = tool.run()
            
            assert result == []

    @patch("base.tool.revision_reg.as_completed")
    @patch("base.tool.revision_reg.ThreadPoolExecutor")
    def test_run_with_concurrent_execution_error(self, mock_executor_class, mock_as_completed, sample_revision_paragraphs, sample_xml_content):
        """测试并发执行错误的情况"""
        tool = RevisionRegTool()
        tool._parameters = {
            "revision_paragraphs_xml": sample_revision_paragraphs,
            "xml_content": sample_xml_content
        }
        
        # Mock ThreadPoolExecutor 的上下文管理器
        mock_executor = MagicMock()
        mock_executor_class.return_value.__enter__.return_value = mock_executor
        
        # Mock submit 抛出异常
        mock_executor.submit.side_effect = Exception("Concurrent execution error")
        
        result = tool.run()
        
        # 应该返回空列表
        assert result == []

    def test_integration_with_real_data(self):
        """集成测试：使用真实数据结构"""
        tool = RevisionRegTool()
        
        # 真实的测试数据
        revision_paragraphs_xml = [
            '<Xml><Paragraph paraId="40B5CB57">References: Calderbank v Calderbank [1975] 3 All ER 333</Paragraph></Xml>'
        ]
        
        xml_content = {
            "Elements": [
                {
                    "ElementId": "40B5CB57",
                    "Text": "References: Calderbank v Calderbank [1975] 3 All ER 333",
                    "ElementType": "Paragraph"
                }
            ]
        }
        
        tool._parameters = {
            "revision_paragraphs_xml": revision_paragraphs_xml,
            "xml_content": xml_content
        }
        
        # Mock 所有外部依赖
        with patch.object(tool, 'llm_predict') as mock_llm_predict, \
             patch("base.tool.revision_reg.extract_para_content") as mock_extract_content, \
             patch("base.tool.revision_reg.extract_para_styles") as mock_extract_styles, \
             patch("base.tool.revision_reg.extract_para_links") as mock_extract_links:
            
            # 设置预期的返回值
            mock_llm_predict.return_value = """
            - para_id: "40B5CB57"
              quotation: false
              link: false
              abbr: true
              capital: false
              leg_ac: true
              heading: false
            """
            
            mock_extract_content.return_value = "References: Calderbank v Calderbank [1975] 3 All ER 333"
            mock_extract_styles.return_value = ("", "<b>References: </b><i>Calderbank v Calderbank </i>")
            mock_extract_links.return_value = []
            
            result = tool.run()
            
            # 验证结果
            assert len(result) == 1
            assert result[0]["para_id"] == "40B5CB57"
            assert result[0]["abbr"] is True
            assert result[0]["leg_ac"] is True
            assert result[0]["quotation"] is False


class TestRevisionRegToolNewFeatures:
    """测试 RevisionRegTool 新增功能的集成"""
    
    def test_flag_processors_initialization(self):
        """测试标志处理器正确初始化"""
        tool = RevisionRegTool()
        
        # 验证所有处理器都被正确初始化
        assert hasattr(tool, 'navigation_section_processor')
        assert hasattr(tool, 'reference_processor')
        assert hasattr(tool, 'other_resources_processor')
        assert hasattr(tool, 'title_processor')
        
        # 验证处理器类型
        from base.tool.revision_reg import (
            NavigationSectionFlagProcessor,
            ReferenceFlagProcessor,
            OtherResourcesFlagProcessor,
            TitleFlagProcessor
        )
        
        assert isinstance(tool.navigation_section_processor, NavigationSectionFlagProcessor)
        assert isinstance(tool.reference_processor, ReferenceFlagProcessor)
        assert isinstance(tool.other_resources_processor, OtherResourcesFlagProcessor)
        assert isinstance(tool.title_processor, TitleFlagProcessor)
    
    @patch('base.tool.revision_reg.extract_paragraph_data')
    def test_new_data_extraction_integration(self, mock_extract):
        """测试新的数据提取流程集成"""
        tool = RevisionRegTool()
        tool._parameters = {
            "revision_paragraphs_xml": [
                '<Xml><Paragraph paraId="test_id">Test content</Paragraph></Xml>'
            ],
            "xml_content": {"Elements": []}
        }
        
        # 设置 extract_paragraph_data 的返回值
        mock_extract.return_value = {
            "content": "Test content",
            "links": ["http://example.com"],
            "par_style_text": "<pStyle>Normal</pStyle>",
            "run_style_text": "<b>Test</b> content"
        }
        
        with patch.object(tool, 'llm_predict') as mock_llm_predict:
            mock_llm_predict.return_value = '''
- para_id: "test_id"
  quotation: true
  link: false
  abbr: false
  capital: false
  leg_ac: false
  heading: false
            '''
            
            results = tool.run()
            
            # 验证新的数据提取被调用
            mock_extract.assert_called_once()
            
            # 验证结果包含提取的数据
            assert len(results) == 1
            result = results[0]
            assert result["para_content"] == "Test content"
            assert result["links"] == ["http://example.com"]
            assert result["par_style_text"] == "<pStyle>Normal</pStyle>"
            assert result["run_style_text"] == "<b>Test</b> content"
    
    def test_flag_processors_application_order(self):
        """测试标志处理器应用顺序"""
        tool = RevisionRegTool()
        
        # 创建包含多种元素的测试数据
        xml_content = {
            "Elements": [
                {"ElementId": "ref1", "PlainText": "References: Test"},
                {"ElementId": "other1", "PlainText": "Other Resources"},
                {"ElementId": "title1", "PlainText": "Title", "Properties": {"Style": "Heading1"}},
                {"ElementId": "jump1", "PlainText": "jump to section"}
            ]
        }
        
        revision_paragraphs = [
            '<Xml><Paragraph paraId="ref1">References: Test</Paragraph></Xml>',
            '<Xml><Paragraph paraId="other1">Other Resources</Paragraph></Xml>',
            '<Xml><Paragraph paraId="title1">Title</Paragraph></Xml>',
            '<Xml><Paragraph paraId="jump1">jump to section</Paragraph></Xml>'
        ]
        
        tool._parameters = {
            "revision_paragraphs_xml": revision_paragraphs,
            "xml_content": xml_content
        }
        
        with patch.object(tool, 'llm_predict') as mock_llm_predict, \
             patch('base.tool.revision_reg.extract_paragraph_data') as mock_extract:
            
            mock_llm_predict.side_effect = [
                '- para_id: "ref1"\n  quotation: false\n  link: false',
                '- para_id: "other1"\n  quotation: false\n  link: false',
                '- para_id: "title1"\n  quotation: false\n  link: false',
                '- para_id: "jump1"\n  quotation: false\n  link: false'
            ]
            
            mock_extract.return_value = {
                "content": "test",
                "links": [],
                "par_style_text": "",
                "run_style_text": ""
            }
            
            results = tool.run()
            results_by_id = {r["para_id"]: r for r in results}
            
            # 验证每个处理器的效果
            assert results_by_id["ref1"]["link"] is True  # ReferenceFlagProcessor
            assert results_by_id["other1"]["link"] is False  # OtherResourcesFlagProcessor 重置
            assert results_by_id["title1"]["heading"] is True  # TitleFlagProcessor
            assert results_by_id["jump1"]["heading"] is True  # NavigationSectionFlagProcessor


if __name__ == "__main__":
    pytest.main([__file__, "-v"])