import json
import os
import datetime
import pytest
from unittest.mock import patch, MagicMock, mock_open

from base.tool.word_update import WordUpdateTool


@pytest.fixture(autouse=True)
def setup_env(monkeypatch):
    """Automatically set JOB_PREFIX for all tests."""
    monkeypatch.setenv("JOB_PREFIX", "unit-test")


@patch("boto3.client")
def test_run_with_invalid_path(mock_boto_client):
    """Test the run method with an invalid S3 path."""
    tool = WordUpdateTool()
    tool._parameters = {"path": "invalid_path"}
    with pytest.raises(ValueError, match="Unsupported path format: invalid_path"):
        tool.run()


@patch("base.tool.word_update.lambda_client")
@patch("base.tool.word_update.boto3")
def test_run_with_valid_path(mock_boto3, mock_lambda_client):
    """Test the run method with a valid S3 path."""
    # Prepare test data
    test_path = "s3://test-bucket/documents/test-document.docx"
    test_revisions = [
        {"ParaId": "p1", "Text": "Original text 1", "ChangeText": "Modified text 1"},
        {"ParaId": "p2", "Text": "Original text 2", "ChangeText": "Modified text 2"},
    ]
    expected_s3_result = "s3://result-bucket/updated-document.docx"

    # Create separate mock clients
    mock_s3_client = MagicMock()
    mock_boto3.client.return_value = mock_s3_client

    mock_response = {"StatusCode": 200, "Payload": MagicMock()}
    mock_response["Payload"].read.return_value = json.dumps({"Data": expected_s3_result}).encode("utf-8")
    mock_lambda_client.invoke.return_value = mock_response
    # Create tool instance and set parameters
    tool = WordUpdateTool()
    tool._parameters = {"path": test_path, "revisions": test_revisions}

    # Execute the method
    result = tool.run()
    # Verify the result
    assert result["Data"] == expected_s3_result

    # Verify S3 client was called correctly for uploading JSON
    mock_s3_client.put_object.assert_called_once()
    s3_call_args = mock_s3_client.put_object.call_args[1]
    assert s3_call_args["Bucket"] == "test-bucket"
    assert s3_call_args["Key"] == "unit-test/ai_output.json"
    assert s3_call_args["ContentType"] == "application/json"

    # Verify the uploaded JSON content contains our revisions
    uploaded_content = json.loads(s3_call_args["Body"].decode("utf-8"))
    assert uploaded_content == test_revisions

    # Verify Lambda was called with correct parameters
    mock_lambda_client.invoke.assert_called_once()
    lambda_call_args = mock_lambda_client.invoke.call_args[1]
    assert lambda_call_args["FunctionName"] == "5633-pg-local-wordtool-ApplyRevisionsHandler"

    # Verify the Lambda payload
    lambda_payload = json.loads(lambda_call_args["Payload"])
    assert lambda_payload["Document"]["Bucket"] == "test-bucket"
    assert lambda_payload["Document"]["Key"] == "documents/test-document.docx"
    assert lambda_payload["Revision"]["Bucket"] == "test-bucket"
    assert lambda_payload["Revision"]["Key"] == "unit-test/ai_output.json"


@patch("base.tool.word_update.lambda_client")
@patch("base.tool.word_update.boto3")
@patch("builtins.open", new_callable=mock_open, read_data="{}")
def test_run_with_local_runner(
    mock_file_open,
    mock_boto3,
    mock_lambda_client,
):
    """Test the run method with local runner."""
    # Prepare test data
    test_path = "s3://test-bucket/documents/test-document.docx"
    test_revisions = [
        {"ParaId": "p1", "Text": "Original text 1", "ChangeText": "Modified text 1"},
        {"ParaId": "p2", "Text": "Original text 2", "ChangeText": "Modified text 2"},
    ]
    expected_s3_result = "s3://result-bucket/updated-document.docx"

    # Create separate mock clients
    mock_s3_client = MagicMock()
    mock_boto3.return_value = mock_s3_client

    mock_response = {"StatusCode": 200, "Payload": MagicMock()}
    mock_response["Payload"].read.return_value = json.dumps({"Data": expected_s3_result}).encode("utf-8")
    mock_lambda_client.invoke.return_value = mock_response

    # Create tool instance and set parameters
    tool = WordUpdateTool()
    tool._parameters = {"path": test_path, "revisions": test_revisions}

    # Set JOB_PREFIX to local-run
    os.environ["JOB_PREFIX"] = "local-run"

    # Execute the method
    result = tool.run()

    # Verify the result
    assert result["Data"] == expected_s3_result

    # Verify Lambda was called with correct parameters
    mock_lambda_client.invoke.assert_called_once()
