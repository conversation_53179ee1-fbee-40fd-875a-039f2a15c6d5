from unittest.mock import patch
from base.tool.preprocess import PreProcessTool
import pytest


@pytest.fixture(autouse=True)
def setup_env(monkeypatch):
    """Automatically set JOB_PREFIX for all tests."""
    monkeypatch.setenv("JOB_PREFIX", "unit-test")


def test_run_with_empty():
    tool = PreProcessTool()
    tool._parameters = {"xml_content": ""}
    result = tool.run()
    assert result == "<Xml/>", "Expected None for empty XML content"


def test_run_with_valid_xml():
    tool = PreProcessTool()
    valid_xml = """<Xml><Paragraph paraId="p1"><Text>First paragraph</Text></Paragraph><Paragraph paraId="p2"><Text>Second paragraph</Text></Paragraph></Xml>"""
    tool._parameters = {
        "xml_content": [{"ParaId": "p1", "Text": "First paragraph"}, {"ParaId": "p2", "Text": "Second paragraph"}]
    }
    result = tool.run()
    print(result)
    assert result == valid_xml, "Expected the same XML content for valid input"


def test_run_with_empty_text():
    tool = PreProcessTool()
    valid_xml = """<Xml><Paragraph paraId="p1"><Text>First paragraph</Text></Paragraph></Xml>"""
    tool._parameters = {"xml_content": [{"ParaId": "p1", "Text": "First paragraph"}, {"ParaId": "p2", "Text": ""}]}
    result = tool.run()
    assert result == valid_xml, "Expected the same XML content for empty text"


@patch("base.tool.revision_extract.extract_paragraphs_xml")
def test_run_with_invalid_xml(mock_extract_paragraphs_dicts):
    mock_extract_paragraphs_dicts.side_effect = Exception("Invalid XML")
    tool = PreProcessTool()
    tool._parameters = {"xml_content": "<InvalidXml>"}
    with pytest.raises(Exception):
        tool.run()


def test_run_missing_parameters():
    tool = PreProcessTool()
    tool._parameters = {}
    with pytest.raises(KeyError):
        tool.run()
