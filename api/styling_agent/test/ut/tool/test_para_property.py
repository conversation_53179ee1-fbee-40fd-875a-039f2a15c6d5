import unittest
from base.util.heading import ParaPropertyCheker, para_checker


class TestParaPropertyCheker(unittest.TestCase):
    """测试 ParaPropertyCheker 类的功能"""

    def setUp(self):
        """设置测试数据"""
        self.checker = ParaPropertyCheker()

    def test_check_heading_removes_period(self):
        """测试用例1：标题样式应该移除末尾句号"""
        # 测试数据
        test_cases = [
            {
                "name": "Heading1 with period",
                "par_style": "<pStyle>Heading1</pStyle>",
                "para_content": "Introduction to Legal Framework.",
                "expected": "Introduction to Legal Framework",
            },
            {
                "name": "Heading2 with period",
                "par_style": "<pStyle>Heading2</pStyle>",
                "para_content": "Key Considerations and Requirements.",
                "expected": "Key Considerations and Requirements",
            },
            {
                "name": "heading3 with period (lowercase)",
                "par_style": "<pStyle>heading3</pStyle>",
                "para_content": "Summary of Findings.",
                "expected": "Summary of Findings",
            },
            {
                "name": "Multiple pStyle tags with heading",
                "par_style": "<pStyle>Normal</pStyle><pStyle>Heading1</pStyle>",
                "para_content": "Mixed Styles Content.",
                "expected": "Mixed Styles Content",
            },
        ]

        for case in test_cases:
            with self.subTest(case=case["name"]):
                result = self.checker.check_heading(
                    case["par_style"], case["para_content"]
                )
                self.assertEqual(
                    result,
                    case["expected"],
                    f"Failed for {case['name']}: expected '{case['expected']}', got '{result}'",
                )

    def test_check_heading_no_changes_needed(self):
        """测试用例2：不需要修改的情况"""
        # 测试数据
        test_cases = [
            {
                "name": "Heading without period",
                "par_style": "<pStyle>Heading1</pStyle>",
                "para_content": "Introduction to Legal Framework",
                "expected": "Introduction to Legal Framework",
            },
            {
                "name": "Normal style with period",
                "par_style": "<pStyle>Normal</pStyle>",
                "para_content": "This is normal text content.",
                "expected": "This is normal text content.",
            },
            {
                "name": "No pStyle tag",
                "par_style": "",
                "para_content": "Content without style tag.",
                "expected": "Content without style tag.",
            },
            {
                "name": "Non-heading style",
                "par_style": "<pStyle>Quote</pStyle>",
                "para_content": "This is a quoted text.",
                "expected": "This is a quoted text.",
            },
            {
                "name": "Empty content",
                "par_style": "<pStyle>Heading1</pStyle>",
                "para_content": "",
                "expected": "",
            },
            {
                "name": "Heading with question mark",
                "par_style": "<pStyle>Heading1</pStyle>",
                "para_content": "What are the requirements?",
                "expected": "What are the requirements?",
            },
            {
                "name": "Heading with exclamation mark",
                "par_style": "<pStyle>Heading2</pStyle>",
                "para_content": "Important Notice!",
                "expected": "Important Notice!",
            },
            {
                "name": "Period in middle of content",
                "par_style": "<pStyle>Heading1</pStyle>",
                "para_content": "U.S. Legal Framework Overview",
                "expected": "U.S. Legal Framework Overview",
            },
            {
                "name": "Multiple periods only last removed",
                "par_style": "<pStyle>Heading1</pStyle>",
                "para_content": "U.S.A. Legal Framework.",
                "expected": "U.S.A. Legal Framework",
            },
        ]

        for case in test_cases:
            with self.subTest(case=case["name"]):
                result = self.checker.check_heading(
                    case["par_style"], case["para_content"]
                )
                self.assertEqual(
                    result,
                    case["expected"],
                    f"Failed for {case['name']}: expected '{case['expected']}', got '{result}'",
                )


class TestParaPropertyChekerEdgeCases(unittest.TestCase):
    """测试边界情况和异常情况"""

    def setUp(self):
        self.checker = ParaPropertyCheker()

    def test_edge_cases(self):
        """测试边界情况"""
        edge_cases = [
            {
                "name": "Malformed pStyle tag",
                "par_style": "<pStyle>Heading1",  # 缺少结束标签
                "para_content": "Malformed content.",
                "expected": "Malformed content.",  # 应该不做修改
            },
            {
                "name": "Empty pStyle tag",
                "par_style": "<pStyle></pStyle>",
                "para_content": "Empty style content.",
                "expected": "Empty style content.",
            },
            {
                "name": "Nested pStyle tags",
                "par_style": "<pStyle>Heading1<pStyle>Heading2</pStyle></pStyle>",
                "para_content": "Nested content.",
                "expected": "Nested content",  # 应该移除句号
            },
            {
                "name": "Case sensitivity test",
                "par_style": "<pStyle>HEADING1</pStyle>",
                "para_content": "Uppercase heading style.",
                "expected": "Uppercase heading style",
            },
        ]

        for case in edge_cases:
            with self.subTest(case=case["name"]):
                result = self.checker.check_heading(
                    case["par_style"], case["para_content"]
                )
                self.assertEqual(
                    result,
                    case["expected"],
                    f"Failed for {case['name']}: expected '{case['expected']}', got '{result}'",
                )

    def test_static_instance(self):
        """测试静态实例 para_checker"""
        par_style = "<pStyle>Heading1</pStyle>"
        para_content = "Test with static instance."
        expected = "Test with static instance"

        result = para_checker.check_heading(par_style, para_content)
        self.assertEqual(result, expected)


if __name__ == "__main__":
    # 运行特定测试
    suite = unittest.TestSuite()

    # 添加测试用例
    suite.addTest(TestParaPropertyCheker("test_check_heading_removes_period"))
    suite.addTest(TestParaPropertyCheker("test_check_heading_no_changes_needed"))
    suite.addTest(TestParaPropertyChekerEdgeCases("test_edge_cases"))

    # 运行测试
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)

    # 或者运行所有测试
    # unittest.main()
