import pytest
from unittest.mock import Mock, patch

from base.error import NoAIModifiedRevisionError, NoAIProcessParagraphError
from base.tool.concurrent import ConcurrentTool


class TestConcurrentTool:
    """Test cases for ConcurrentTool"""

    def setup_method(self):
        def mock_process_function(paragraph, xml_content):
            return {
                "ParaId": paragraph["para_id"],
                "Text": paragraph["para_content"],
                "ChangeText": [f"Modified {paragraph['para_content']}"],
            }

        """Setup method called before each test method"""
        self.sample_paragraphs = [
            {"para_id": "para_1", "para_content": "Test content 1"},
            {"para_id": "para_2", "para_content": "Test content 2"},
            {"para_id": "para_3", "para_content": "Test content 3"},
        ]

        self.sample_xml_content = {"xml_data": "sample xml content"}
        self.mock_process_function = mock_process_function
        self.mock_logger = Mock()

    def test_successful_execution(self):
        """Test successful execution with valid parameters"""
        # Arrange
        expected_results = [
            {
                "ParaId": "para_1",
                "Text": "Test content 1",
                "ChangeText": ["Modified content 1"],
            },
            {
                "ParaId": "para_2",
                "Text": "Test content 2",
                "ChangeText": ["Modified content 2"],
            },
            {
                "ParaId": "para_3",
                "Text": "Test content 3",
                "ChangeText": ["Modified content 3"],
            },
        ]

        self.mock_process_function.side_effect = expected_results

        tool = ConcurrentTool(
            paragraphs=self.sample_paragraphs,
            xml_content=self.sample_xml_content,
            process_function=self.mock_process_function,
        )
        tool.logger = self.mock_logger

        # Act
        result = tool.run()

        # Assert
        assert len(result) == 3

    def test_raises_error_when_no_process_function(self):
        """Test that NoAIProcessParagraphError is raised when no process function provided"""
        # Arrange
        tool = ConcurrentTool(
            paragraphs=self.sample_paragraphs,
            xml_content=self.sample_xml_content,
            process_function=None,
        )
        tool.logger = self.mock_logger

        # Act & Assert
        with pytest.raises(NoAIProcessParagraphError):
            tool.run()

        self.mock_logger.error.assert_called_once()

    def test_raises_error_when_no_paragraphs(self):
        """Test that NoAIModifiedRevisionError is raised when no paragraphs provided"""
        # Arrange
        tool = ConcurrentTool(
            paragraphs=None,
            xml_content=self.sample_xml_content,
            process_function=self.mock_process_function,
        )
        tool.logger = self.mock_logger

        # Act & Assert
        with pytest.raises(NoAIModifiedRevisionError):
            tool.run()

        self.mock_logger.debug.assert_called_once()

    def test_filters_none_results(self):
        """Test that None results are filtered out"""
        tool = ConcurrentTool(
            paragraphs=self.sample_paragraphs,
            xml_content=self.sample_xml_content,
            process_function=self.mock_process_function,
        )
        tool.logger = self.mock_logger

        # Act
        result = tool.run()

        # Assert
        assert len(result) == 3  # Only para_1 and para_3 should be included

    def test_worker_count_calculation(self):
        """Test worker count calculation logic"""
        # Arrange
        tool = ConcurrentTool(name="TestConcurrentTool", parameters={})

        # Test cases: (paragraph_count, max_workers, expected_workers)
        test_cases = [
            (1, 10, 1),  # Small count gets minimum 3
            (2, 10, 2),  # Small count gets minimum 3
            (3, 10, 3),  # Exactly 3
            (5, 10, 5),  # Medium count
            (8, 10, 5),  # Medium count gets 5
            (10, 10, 5),  # Medium count gets 5
            (15, 10, 10),  # Large count gets max
            (20, 10, 10),  # Large count limited by max_workers
        ]

        for paragraph_count, max_workers, expected in test_cases:
            # Act
            result = tool._worker_count(paragraph_count, max_workers)

            # Assert
            assert (
                result == expected
            ), f"Failed for paragraph_count={paragraph_count}, max_workers={max_workers}"
