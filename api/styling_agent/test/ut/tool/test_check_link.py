from base.tool.check_link import <PERSON><PERSON><PERSON><PERSON>ool
import pytest
from unittest.mock import patch, MagicMock
import requests


@pytest.fixture(autouse=True)
def setup_env(monkeypatch):
    """Automatically set JOB_PREFIX for all tests."""
    monkeypatch.setenv("JOB_PREFIX", "unit-test")


def test_run_with_empty_parameters():
    tool = CheckLinkTool()
    tool._parameters = {}
    result = tool.run()
    print(result)
    assert result["link_revisions"] == {}, "Expected empty string for empty parameters"


@patch.object(requests, "get")
def test_get_link_valid_message_gov_site_accessible(mock_get):
    # Mock the response of requests.get to simulate a successful request
    mock_response = MagicMock()
    mock_response.status_code = 200
    mock_get.return_value = mock_response

    tool = CheckLinkTool()
    url = "https://www.example.gov.au"
    result = tool.get_link_valid_message(url)
    assert result == "Link not accessible, suggest checking with legal editor."


@patch.object(requests, "get")
def test_get_link_valid_message_gov_site_not_accessible(mock_get):
    # Mock the response of requests.get to simulate a 404 error
    mock_response = MagicMock()
    mock_response.status_code = 404
    mock_get.return_value = mock_response

    tool = CheckLinkTool()
    url = "https://www.example.gov.au"
    result = tool.get_link_valid_message(url)

    assert result == "Link not accessible, suggest checking with legal editor."


@patch.object(requests, "get")
def test_get_link_valid_message_gov_site_request_error(mock_get):
    # Mock the response of requests.get to simulate a 500 error
    mock_get.side_effect = requests.exceptions.RequestException("Connection error")

    tool = CheckLinkTool()
    url = "https://www.example.gov.au"

    result = tool.get_link_valid_message(url)
    assert result == "Link not accessible, suggest checking with legal editor."


@patch.object(requests, "get")
def test_run_with_single_url(mock_get):
    """Test run method with text containing a single URL."""
    mock_response = MagicMock()
    mock_response.status_code = 200
    mock_get.return_value = mock_response

    tool = CheckLinkTool()
    tool._parameters = {"para_id": "test_id", "text": "Check this link: https://www.test.gov.au", "link": True}
    result = tool.run()

    assert result is not None


def test_url_pattern_matching():
    """Test the URL pattern matching correctly identifies different URLs."""
    import re

    url_pattern = r'(https?://[^\s<>"]*[^\s<>"\.,!?;\)\]\}])'

    # URLs that should match
    valid_urls = [
        "https://www.example.com",
        "https://example.com",
        "https://test.gov.au/path/to/resource",
        "https://site.com?query=value",
    ]

    # Texts that should not match as URLs
    invalid_urls = [
        "www.example.com",  # Missing protocol
        "https:example.com",  # Missing //
        "text (https://example.com)",  # Should only match the URL part
        "<EMAIL>",  # Not a URL
    ]

    for url in valid_urls:
        match = re.search(url_pattern, url)
        assert match is not None, f"URL pattern failed to match valid URL: {url}"
        assert match.group(0) == url, f"URL pattern matched incorrectly for: {url}"

    for text in invalid_urls:
        if "https://" not in text:
            match = re.search(url_pattern, text)
            assert match is None, f"URL pattern incorrectly matched: {text}"
