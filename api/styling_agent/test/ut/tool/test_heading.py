import unittest
from unittest.mock import patch, MagicMock
from base.tool.heading import Heading<PERSON>heckTool
from base.util.heading import para_checker


class TestHeadingCheckTool(unittest.TestCase):
    """Test cases for HeadingCheckTool"""

    def setUp(self):
        """Set up test fixtures"""
        self.tool_class = HeadingCheckTool

    def test_heading_lowercase_first_letter_with_period(self):
        """Test case 1: Heading with lowercase first letter and period - both issues"""
        tool_data = {
            "para_id": "para_001",
            "para_content": "this is a heading.",
            "par_style_text": "<pStyle>Heading1</pStyle>",
        }
        tool = self.tool_class(**tool_data)
        result = tool.run()

        # Should fix both issues: capitalize and remove period
        self.assertEqual(result["para_id"], "para_001")
        self.assertEqual(result["modified_para_content"], "This is a heading")
        # Comment should show the final modified content with annotation
        self.assertEqual(
            result["commented_para_content"], "This is a heading[1][Capitalize Tool]"
        )

    def test_heading_lowercase_first_letter_no_period(self):
        """Test case 2: Heading with lowercase first letter only"""
        tool_data = {
            "para_id": "para_002",
            "para_content": "this is another heading",
            "par_style_text": "<pStyle>Heading2</pStyle>",
        }
        tool = self.tool_class(**tool_data)
        result = tool.run()

        # Should only capitalize first letter
        self.assertEqual(result["modified_para_content"], "This is another heading")
        # Comment should show the modified content with annotation
        self.assertEqual(
            result["commented_para_content"],
            "This is another heading[1][Capitalize Tool]",
        )

    def test_heading_uppercase_first_letter_with_period(self):
        """Test case 3: Heading with correct capitalization but period"""
        tool_data = {
            "para_id": "para_003",
            "para_content": "This is a heading.",
            "par_style_text": "<pStyle>Heading3</pStyle>",
        }
        tool = self.tool_class(**tool_data)
        result = tool.run()

        # Should only remove period
        self.assertEqual(result["modified_para_content"], "This is a heading")
        # Comment should show the modified content with annotation
        self.assertEqual(
            result["commented_para_content"], "This is a heading[1][Capitalize Tool]"
        )

    def test_heading_correct_format(self):
        """Test case 4: Heading with correct format - no changes needed"""
        tool_data = {
            "para_id": "para_004",
            "para_content": "This Is A Perfect Heading",
            "par_style_text": "<pStyle>Heading1</pStyle>",
        }
        tool = self.tool_class(**tool_data)
        result = tool.run()

        # Should remain unchanged, comment_content should be empty
        self.assertEqual(result["modified_para_content"], "This Is A Perfect Heading")
        self.assertEqual(result["commented_para_content"], "")

    def test_non_heading_style(self):
        """Test case 5: Non-heading paragraph style"""
        tool_data = {
            "para_id": "para_005",
            "para_content": "this is normal text with period.",
            "par_style_text": "<pStyle>Normal</pStyle>",
        }
        tool = self.tool_class(**tool_data)
        result = tool.run()

        # Should remain unchanged since it's not a heading
        self.assertEqual(
            result["modified_para_content"], "this is normal text with period."
        )
        self.assertEqual(result["commented_para_content"], "")

    def test_empty_para_content(self):
        """Test case 6: Empty paragraph content"""
        tool_data = {
            "para_id": "para_006",
            "para_content": "",
            "par_style_text": "<pStyle>Heading1</pStyle>",
        }
        tool = self.tool_class(**tool_data)
        result = tool.run()

        # Should handle empty content gracefully
        self.assertEqual(result["modified_para_content"], "")
        self.assertEqual(result["commented_para_content"], "")

    def test_empty_style_text(self):
        """Test case 7: Empty style text"""
        tool_data = {
            "para_id": "para_007",
            "para_content": "some content here.",
            "par_style_text": "",
        }
        tool = self.tool_class(**tool_data)
        result = tool.run()

        # Should return original content with empty comment
        self.assertEqual(result["modified_para_content"], "some content here.")
        self.assertEqual(result["commented_para_content"], "")

    def test_malformed_style_text(self):
        """Test case 8: Malformed style text"""
        tool_data = {
            "para_id": "para_008",
            "para_content": "heading content.",
            "par_style_text": "<pStyle>NotAHeading</pStyle><invalidTag>",
        }
        tool = self.tool_class(**tool_data)
        result = tool.run()

        # Should not process since it doesn't match heading pattern
        self.assertEqual(result["modified_para_content"], "heading content.")
        self.assertEqual(result["commented_para_content"], "")

    def test_chinese_period_removal(self):
        """Test case 9: Heading with Chinese period"""
        tool_data = {
            "para_id": "para_009",
            "para_content": "This is a heading。",
            "par_style_text": "<pStyle>Heading1</pStyle>",
        }
        tool = self.tool_class(**tool_data)
        result = tool.run()

        # Should remove Chinese period
        self.assertEqual(result["modified_para_content"], "This is a heading")
        self.assertEqual(
            result["commented_para_content"], "This is a heading[1][Capitalize Tool]"
        )

    def test_single_character_heading(self):
        """Test case 10: Single character heading with lowercase"""
        tool_data = {
            "para_id": "para_010",
            "para_content": "a",
            "par_style_text": "<pStyle>Heading1</pStyle>",
        }
        tool = self.tool_class(**tool_data)
        result = tool.run()

        # Should capitalize single character
        self.assertEqual(result["modified_para_content"], "A")
        self.assertEqual(result["commented_para_content"], "A[1][Capitalize Tool]")


class TestParaPropertyCheker(unittest.TestCase):
    """Test cases for ParaPropertyCheker utility"""

    def setUp(self):
        self.checker = para_checker

    def test_check_heading_both_fixes(self):
        """Test case 1: Content needs both capitalization and period removal"""
        result = self.checker.check_heading(
            "<pStyle>Heading1</pStyle>", "this is a heading."
        )

        self.assertEqual(result["para_content"], "This is a heading")
        # Should show the final modified content with annotation
        self.assertEqual(
            result["comment_content"], "This is a heading[1][Capitalize Tool]"
        )

    def test_check_heading_only_capitalization(self):
        """Test case 2: Content only needs capitalization"""
        result = self.checker.check_heading(
            "<pStyle>Heading2</pStyle>", "this is a heading"
        )

        self.assertEqual(result["para_content"], "This is a heading")
        self.assertEqual(
            result["comment_content"], "This is a heading[1][Capitalize Tool]"
        )

    def test_check_heading_only_period_removal(self):
        """Test case 3: Content only needs period removal"""
        result = self.checker.check_heading(
            "<pStyle>Heading3</pStyle>", "This is a heading."
        )

        self.assertEqual(result["para_content"], "This is a heading")
        self.assertEqual(
            result["comment_content"], "This is a heading[1][Capitalize Tool]"
        )

    def test_check_heading_no_changes(self):
        """Test case 4: Content needs no changes"""
        result = self.checker.check_heading(
            "<pStyle>Heading1</pStyle>", "This is a perfect heading"
        )

        self.assertEqual(result["para_content"], "This is a perfect heading")
        # No modification, so comment_content should be empty
        self.assertEqual(result["comment_content"], "")

    def test_check_heading_no_matches(self):
        """Test case 5: Style doesn't contain heading"""
        result = self.checker.check_heading(
            "<pStyle>Normal</pStyle>", "this is normal text."
        )

        self.assertEqual(result["para_content"], "this is normal text.")
        self.assertEqual(result["comment_content"], "")

    def test_check_heading_empty_content(self):
        """Test case 6: Empty content handling"""
        result = self.checker.check_heading("<pStyle>Heading1</pStyle>", "")

        self.assertEqual(result["para_content"], "")
        self.assertEqual(result["comment_content"], "")

    def test_check_heading_chinese_period(self):
        """Test case 7: Chinese period handling"""
        result = self.checker.check_heading(
            "<pStyle>Heading1</pStyle>", "This is a heading。"
        )

        self.assertEqual(result["para_content"], "This is a heading")
        self.assertEqual(
            result["comment_content"], "This is a heading[1][Capitalize Tool]"
        )

    def test_check_heading_case_insensitive_match(self):
        """Test case 8: Case insensitive heading detection"""
        result = self.checker.check_heading(
            "<pStyle>HEADING1</pStyle>", "this needs fixing."
        )

        self.assertEqual(result["para_content"], "This needs fixing")
        # Should detect HEADING1 as heading and apply fixes
        self.assertEqual(
            result["comment_content"], "This needs fixing[1][Capitalize Tool]"
        )

    def test_check_heading_multiple_pstyle_tags(self):
        """Test case 9: Multiple pStyle tags"""
        result = self.checker.check_heading(
            "<pStyle>Normal</pStyle><pStyle>Heading1</pStyle>",
            "this has multiple styles.",
        )

        # Should process the heading style
        self.assertEqual(result["para_content"], "This has multiple styles")
        self.assertEqual(
            result["comment_content"], "This has multiple styles[1][Capitalize Tool]"
        )

    def test_check_heading_whitespace_only_content(self):
        """Test case 10: Content with only whitespace"""
        result = self.checker.check_heading("<pStyle>Heading1</pStyle>", "   ")

        # Should handle whitespace-only content gracefully
        self.assertEqual(result["para_content"], "   ")
        # No modification since first character check fails
        self.assertEqual(result["comment_content"], "")

    def test_check_heading_already_capitalized_with_period(self):
        """Test case 11: Already capitalized but has period"""
        result = self.checker.check_heading(
            "<pStyle>Heading1</pStyle>", "Already Capitalized."
        )

        self.assertEqual(result["para_content"], "Already Capitalized")
        self.assertEqual(
            result["comment_content"], "Already Capitalized[1][Capitalize Tool]"
        )

    def test_check_heading_single_lowercase_letter_with_period(self):
        """Test case 12: Single lowercase letter with period"""
        result = self.checker.check_heading("<pStyle>Heading1</pStyle>", "a.")

        self.assertEqual(result["para_content"], "A")
        self.assertEqual(result["comment_content"], "A[1][Capitalize Tool]")


if __name__ == "__main__":
    # Run the example cases from the main function
    print("=== Running Example Cases ===")

    # Test case 1: "this is a heading."
    example_par_style = "<pStyle>Heading1</pStyle>"
    example_para_content = "this is a heading."
    result = para_checker.check_heading(example_par_style, example_para_content)
    print(f"Input: '{example_para_content}'")
    print(f"Result: {result}")
    print(
        f"Expected: {{'para_content': 'This is a heading', 'comment_content': 'This is a heading[1][Capitalize Tool]'}}"
    )
    print()

    # Test case 2: "This is another heading"
    example_par_style2 = "<pStyle>Heading2</pStyle>"
    example_para_content2 = "This is another heading"
    result2 = para_checker.check_heading(example_par_style2, example_para_content2)
    print(f"Input: '{example_para_content2}'")
    print(f"Result: {result2}")
    print(
        f"Expected: {{'para_content': 'This is another heading', 'comment_content': ''}}"
    )
    print()

    print("=== Running Unit Tests ===")

    # Create test suite
    suite = unittest.TestSuite()

    # Add HeadingCheckTool tests
    suite.addTest(unittest.TestLoader().loadTestsFromTestCase(TestHeadingCheckTool))

    # Add ParaPropertyCheker tests
    suite.addTest(unittest.TestLoader().loadTestsFromTestCase(TestParaPropertyCheker))

    # Run tests
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)

    # Print summary
    print(f"\n{'='*50}")
    print(f"Tests run: {result.testsRun}")
    print(f"Failures: {len(result.failures)}")
    print(f"Errors: {len(result.errors)}")
    print(
        f"Success rate: {((result.testsRun - len(result.failures) - len(result.errors)) / result.testsRun * 100):.1f}%"
    )
