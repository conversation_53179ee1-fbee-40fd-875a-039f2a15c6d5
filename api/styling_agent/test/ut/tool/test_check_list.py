import pytest
import json
import os
import uuid
from base.tool.check_list import Check<PERSON><PERSON>Tool, ListValidationError, ListItem


@pytest.fixture
def sample_elements():
    """加载并返回 list-sample.json 的内容"""
    sample_file_path = os.path.join(os.path.dirname(__file__), "list-sample.json")
    with open(sample_file_path, "r", encoding="utf-8") as f:
        return json.load(f)


@pytest.fixture
def check_list_tool(sample_elements):
    """初始化并返回一个 CheckListTool 实例"""
    return CheckListTool(xml_content=sample_elements)


def test_sample_file(check_list_tool):
    result = check_list_tool.run()
    assert isinstance(result, dict)
    assert "patches" in result
    assert isinstance(result["patches"], list)
    assert len(result["patches"]) == 0, "Expected no patches for the sample file"


# def test_list_item_creation():
#     """测试 ListItem 类的创建和属性初始化"""
#     element = {"id": 1, "text": "Test Item"}
#     list_item = ListItem(element=element, indent=2)
#     assert list_item.element == element
#     assert list_item.indent == 2
#     assert list_item.children == []


# def test_list_item_add_child():
#     """测试 ListItem 的 add_child 方法"""
#     parent_item = ListItem(element={"id": "parent"}, indent=0)
#     child_item = ListItem(element={"id": "child"}, indent=1)
#     parent_item.add_child(child_item)
#     assert len(parent_item.children) == 1
#     assert parent_item.children[0] == child_item


class MockElement:
    """A mock element class for testing purposes."""

    def __init__(self, element_type, text="", indent_left=0):
        self._data = {
            "ElementType": element_type,
            "Text": text,
            "Properties": {"IndentLeft": indent_left},
        }

    def get(self, key, default=None):
        # Mock the nested get for Properties -> IndentLeft
        if key == "Properties":
            return self._data.get("Properties", {})
        return self._data.get(key, default)

    def __getitem__(self, key):
        return self._data[key]

    def __repr__(self):
        return f"MockElement(element_type='{self.get('ElementType')}')"


def create_list_item(text, level, label):
    """Helper function to create a list_item dictionary for testing."""
    return {
        "PlainText": text,
        "Properties": {"ListInfo": {"Level": level, "LevelText": label}},
        "ElementId": str(uuid.uuid4()),
    }


class TestCheckListItemRules:
    """Test suite for the check_list_item_rules method in CheckListTool."""

    @pytest.fixture
    def check_list_tool(self):
        """Initializes and returns a CheckListTool instance for testing."""
        return CheckListTool(xml_content={})

    @pytest.mark.parametrize(
        "text, expected_result",
        [
            ("This is valid text.", True),
            ("", False),
            ("   ", False),
        ],
        ids=["valid_text", "empty_string", "whitespace_only"],
    )
    def test_rule_no_empty_list_items(self, check_list_tool, text, expected_result):
        """Rule 8: Test that list items cannot be empty."""
        list_item = create_list_item(text, 0, "•")
        assert check_list_tool.check_list_item_rules(list_item) == expected_result

    @pytest.mark.parametrize(
        "level, label, expected_result",
        [
            (0, "•", True),
            (1, "◦", True),
            (2, "▪", True),
            (3, "▪", False),
        ],
        ids=["level_0", "level_1", "level_2", "level_3_invalid"],
    )
    def test_rule_max_depth(self, check_list_tool, level, label, expected_result):
        """Rule 7: Test that list depth cannot exceed level 2."""
        list_item = create_list_item("Some text", level, label)
        assert check_list_tool.check_list_item_rules(list_item) == expected_result

    @pytest.mark.parametrize(
        "level, label, expected_result",
        [
            (0, "•", True),
            (0, "-", False),
            (1, "◦", True),
            (1, "•", False),
            (2, "▪", True),
            (2, "§", False),
        ],
        ids=[
            "level_0_correct",
            "level_0_incorrect",
            "level_1_correct",
            "level_1_incorrect",
            "level_2_correct",
            "level_2_incorrect",
        ],
    )
    def test_rule_labels(self, check_list_tool, level, label, expected_result):
        """Rules 2-4: Test for correct labels at each list level."""
        list_item = create_list_item("Some text", level, label)
        assert check_list_tool.check_list_item_rules(list_item) == expected_result

    @pytest.mark.parametrize(
        "text, expected_result",
        [
            ("Incomplete sentence;", False),
            ("incomplete sentence;", True),
            ("This is a complete sentence.", True),
            ("No semicolon here", True),
        ],
        ids=[
            "incomplete_uppercase_invalid",
            "incomplete_lowercase_valid",
            "complete_sentence_valid",
            "no_semicolon_valid",
        ],
    )
    def test_rule_incomplete_sentence_capitalization(self, check_list_tool, text, expected_result):
        """Rule 1: Test capitalization for incomplete sentences ending with a semicolon."""
        list_item = create_list_item(text, 0, "•")
        assert check_list_tool.check_list_item_rules(list_item) == expected_result


class TestCheckListToolRun:
    """Test suite for the CheckListTool.run method."""

    def test_run_with_sample_data(self, check_list_tool, sample_elements):
        """
        Test run with sample data to ensure it produces patches for invalid items.
        This is a regression test.
        """
        result = check_list_tool.run()
        patches = result.get("patches", [])
        # Based on list-sample.json and rules, we expect patches for invalid labels.
        # e.g., element '1B085D5B' has Level 0 but label '◦' instead of '•'
        assert len(patches) > 0
        assert any(p["target"][0]["segId"] == "1B085D5B" for p in patches)

    def test_run_with_no_list_items(self, check_list_tool):
        """
        Test run with input that contains no 'List' elements.
        """
        elements = [{"ElementType": "Paragraph", "ElementId": "p1", "PlainText": "Hello"}]
        check_list_tool.parameters["xml_content"] = {"Elements": elements}
        result = check_list_tool.run()
        patches = result.get("patches", [])
        assert len(patches) == 0

    def test_run_with_fully_compliant_list(self, check_list_tool):
        """
        Test run with a list that is fully compliant with all rules.
        """
        elements = [
            create_list_item("Item 1.", 0, "•"),
            create_list_item("Item 2.", 1, "◦"),
            create_list_item("Item 3.", 2, "▪"),
        ]
        check_list_tool.parameters["xml_content"] = {"Elements": elements}
        result = check_list_tool.run()
        patches = result.get("patches", [])
        assert len(patches) == 0


class TestBuildListHierarchy:
    """Test suite for the build_list_hierarchy function."""

    def create_mock_list_item(self, indent_left, level=0):
        """Creates a mock list item with a specific indent and a deliberately incorrect level."""
        return {
            "ElementType": "List",
            "Properties": {"ListInfo": {"IndentLeft": indent_left, "Level": level}},
        }

    def test_run_with_item_missing_element_id(self, check_list_tool):
        """
        Test that an item missing 'ElementId' is skipped and does not produce a patch.
        """
        list_item = create_list_item("Invalid item", 0, "-")  # Invalid label
        del list_item["ElementId"]  # Remove ElementId
        elements = [list_item]

        check_list_tool.parameters["xml_content"] = {"Elements": elements}
        result = check_list_tool.run()
        patches = result.get("patches", [])
        assert len(patches) == 0

    def test_run_with_empty_input(self, check_list_tool):
        """
        Test run with an empty list of elements.
        """
        check_list_tool.parameters["xml_content"] = {"Elements": []}
        result = check_list_tool.run()
        patches = result.get("patches", [])
        assert len(patches) == 0


# 辅助函数，用于创建 ListItem 对象
def create_list_item_obj(text, level, label, element_id=None):
    """Helper function to create a ListItem object for testing."""
    if element_id is None:
        element_id = str(uuid.uuid4())
    element = {
        "PlainText": text,
        "Properties": {"ListInfo": {"Level": level, "LevelText": label}},
        "ElementId": element_id,
    }
    return ListItem(element=element, indent=level)


class TestIntegration:
    @pytest.fixture
    def check_list_tool(self):
        """Initializes and returns a CheckListTool instance for testing."""
        return CheckListTool(xml_content={})

    @pytest.mark.parametrize(
        ("content", "expected_validation_result"),
        [
            # 缩写词 SUCCESS
            ("FBI is an agency.", ListValidationError.SUCCESS),
            ("Dr. Smith is here.", ListValidationError.SUCCESS),
            ("U.K.'s policy.", ListValidationError.SUCCESS),
            # 标识符 SUCCESS
            ("Supreme Court of Victoria: This is a test.", ListValidationError.SUCCESS),
            ("Chapter 1—Introduction.", ListValidationError.SUCCESS),
            # 普通句子，预期 SUCCESS
            ("This is a normal sentence.", ListValidationError.SUCCESS),
            ("another normal sentence.", ListValidationError.SUCCESS),
            # 预期 UPPERCASE_START 错误 (非缩写/标识符，且首字母大写)
            ("Incomplete sentence;", ListValidationError.UPPERCASE_START),
        ],
        ids=[
            "abbreviation_fbi_success",
            "abbreviation_dr_success",
            "abbreviation_uk_success",
            "identifier_supreme_court_success",
            "identifier_chapter_success",
            "normal_sentence_success_1",
            "normal_sentence_success_2",
            "uppercase_start_error",
        ],
    )
    def test_check_list_item_rules_with_new_features(self, check_list_tool, content, expected_validation_result):
        """
        测试 check_list_item_rules 如何处理包含有效缩写或标识符的列表项，确保它们被正确识别为 SUCCESS。
        同时测试非缩写/标识符的列表项仍然强制小写。
        """
        list_item = create_list_item_obj(content, 0, "•")
        result = check_list_tool.check_list_item_rules(list_item)
        assert result == expected_validation_result

    def test_check_sibling_consistency_skips_uppercase_for_abbreviation_and_identifier(self, check_list_tool):
        """
        测试 _check_sibling_consistency 如何在存在缩写或标识符的情况下跳过小写强制要求。
        """
        # 模拟第一个列表项以分号结尾，触发 _check_sibling_consistency 的小写强制逻辑
        elements = [
            create_list_item_obj("First item with semicolon;", 0, "•", "id1"),
            # 缩写，应该跳过小写强制
            create_list_item_obj("FBI is an agency.", 0, "•", "id2"),
            # 标识符，应该跳过小写强制
            create_list_item_obj("Supreme Court: Details.", 0, "•", "id3"),
            # 普通句子，首字母大写，应该被标记为需要小写
            create_list_item_obj("This is a normal sentence.", 0, "•", "id4"),
            # 普通句子，首字母小写，应该通过
            create_list_item_obj("this is another normal sentence.", 0, "•", "id5"),
        ]

        # 为了测试 _check_sibling_consistency，我们需要模拟 ListBlock 的结构
        # 这里直接调用 _check_sibling_consistency，并检查 ListItem 对象的 _needs_lowercase 属性
        list_block = check_list_tool.build_list_blocks({"Elements": elements})[0]
        check_list_tool._check_sibling_consistency(list_block)

        # 验证结果
        # FBI 应该没有 _needs_lowercase 属性或为 False
        assert (
            not hasattr(list_block.list_items[1], "_needs_lowercase") or not list_block.list_items[1]._needs_lowercase
        )
        # Supreme Court 应该没有 _needs_lowercase 属性或为 False
        assert (
            not hasattr(list_block.list_items[2], "_needs_lowercase") or not list_block.list_items[2]._needs_lowercase
        )
        # "This is a normal sentence." 应该被标记为 _needs_lowercase = True
        assert hasattr(list_block.list_items[3], "_needs_lowercase") and list_block.list_items[3]._needs_lowercase
        # "this is another normal sentence." 应该没有 _needs_lowercase 属性或为 False
        assert (
            not hasattr(list_block.list_items[4], "_needs_lowercase") or not list_block.list_items[4]._needs_lowercase
        )

        # 进一步测试 check_list_item_rules 在 _needs_lowercase 被设置后的行为
        # 对于 id4，应该返回 LOWERCASE_REQUIRED
        result_id4 = check_list_tool.check_list_item_rules(list_block.list_items[3])
        assert result_id4 == ListValidationError.LOWERCASE_REQUIRED

        # 对于 id2 和 id3，应该返回 SUCCESS
        result_id2 = check_list_tool.check_list_item_rules(list_block.list_items[1])
        assert result_id2 == ListValidationError.SUCCESS
        result_id3 = check_list_tool.check_list_item_rules(list_block.list_items[2])
        assert result_id3 == ListValidationError.SUCCESS

    def test_existing_rules_unaffected_by_new_features(self, check_list_tool):
        """
        确保这些新功能不会影响现有规则（例如，非缩写/标识符的列表项仍然强制小写）。
        """
        # 测试 EMPTY_ITEM
        list_item_empty = create_list_item_obj("", 0, "•")
        assert check_list_tool.check_list_item_rules(list_item_empty) == ListValidationError.EMPTY_ITEM

        # 测试 LEVEL_TOO_DEEP
        list_item_deep = create_list_item_obj("Too deep", 3, "▪")
        assert check_list_tool.check_list_item_rules(list_item_deep) == ListValidationError.LEVEL_TOO_DEEP

        # 测试 WRONG_LABEL
        list_item_wrong_label = create_list_item_obj("Wrong label", 0, "-")
        assert check_list_tool.check_list_item_rules(list_item_wrong_label) == ListValidationError.WRONG_LABEL

        # 测试 UPPERCASE_START (非缩写/标识符，且首字母大写，包含分号但未以句号结尾)
        list_item_uppercase_start = create_list_item_obj("This is a sentence;", 0, "•")
        assert check_list_tool.check_list_item_rules(list_item_uppercase_start) == ListValidationError.UPPERCASE_START

        # 确保缩写或标识符不会错误地触发这些现有规则
        list_item_abbreviation_empty = create_list_item_obj("FBI", 0, "•")
        assert check_list_tool.check_list_item_rules(list_item_abbreviation_empty) == ListValidationError.SUCCESS

        list_item_identifier_empty = create_list_item_obj("Chapter 1: Content", 0, "•")
        assert check_list_tool.check_list_item_rules(list_item_identifier_empty) == ListValidationError.SUCCESS
