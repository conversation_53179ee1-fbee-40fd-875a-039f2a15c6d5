import pytest
from base.util.author_check import AuthorCheckHelper


class TestAuthorCheckHelper:
    """Test cases for AuthorCheckHelper"""

    def setup_method(self):
        """Setup before each test method"""
        self.helper = AuthorCheckHelper()

    def test_pg_team_perfect_match(self):
        """Test PG team perfect match"""
        text = "Authored by the LexisNexis Legal Writer team."
        code, result = self.helper.validate_with_regex(text)
        assert code == 1
        assert result == text


    def test_external_team_perfect_match(self):
        """Test external team perfect match"""
        test_cases = [
            "Authored by <PERSON>. Updated by the LexisNexis Legal Writer team.",
            "Authored by <PERSON>, Partner. Updated by the LexisNexis Legal Writer team.",
            "Authored by Dr. <PERSON>. Updated by the LexisNexis Legal Writer team.",
        ]

        for text in test_cases:
            code, result = self.helper.validate_with_regex(text)
            assert code == 2
            assert result == text

    def test_external_team_needs_fix(self):
        """Test external team cases that need fixing"""
        test_cases = [
            (
                "Authored by <PERSON>. Updated by LexisNexis Legal Writer team.",
                2,
                "Authored by <PERSON>. Updated by the LexisNexis Legal Writer team.",
            ),
            (
                "Originally authored by <PERSON>, Principal. Updated by the Lexis<PERSON>exis Legal Writer team.",
                2,
                "Authored by <PERSON> <PERSON>, Principal. Updated by the LexisNexis Legal Writer team.",
            ),
            (
                "Authored by <PERSON>， and updated by the LexisNexis Legal Writer team.",
                2,
                "Authored by <PERSON>. Updated by the Lexis<PERSON>exis Legal Writer team.",
            ),
            ("<PERSON> <PERSON> authored this", 5, "<PERSON> <PERSON> authored this"),
        ]

        for original, expected_code, expected_result in test_cases:
            code, result = self.helper.validate_with_regex(original)
            assert code == expected_code, f"Failed for: {original}"
            assert result == expected_result, f"Failed for: {original}"

    def test_multiple_authors_perfect_match(self):
        """Test multiple authors perfect match"""
        test_cases = [
            "Authored by John Smith (NSW); Jane Doe (VIC). Updated by the LexisNexis Legal Writer team.",
            "Authored by Dr. A (QLD); Prof. B (WA); Ms. C (SA). Updated by the LexisNexis Legal Writer team.",
        ]

        for text in test_cases:
            code, result = self.helper.validate_with_regex(text)
            assert code == 3
            assert result == text

    def test_multiple_authors_needs_fix(self):
        """Test multiple authors cases that need fixing"""
        test_cases = [
            ("Auth by John (NSW) Jane (VIC). Update by team.", 5, "Auth by John (NSW) Jane (VIC). Update by team."),
            (
                "Authored by A (NSW) B (VIC). Updated by team",
                3,
                "Authored by A (NSW) B (VIC). Updated by the LexisNexis Legal Writer team.",
            ),
        ]

        for original, expected_code, expected_result in test_cases:
            code, result = self.helper.validate_with_regex(original)
            assert code == expected_code, f"Failed for: {original}"
            assert result == expected_result, f"Failed for: {original}"

    def test_adapted_from_perfect_match(self):
        """Test adapted from PG perfect match"""
        test_cases = [
            "Adapted from PG Contract Law/Topic: Formation/Contract Formation Guide authored by John Smith.",
            "Adapted from PG Corporate Law/Business/Corporate Governance authored by Dr. Johnson.",
        ]

        for text in test_cases:
            code, result = self.helper.validate_with_regex(text)
            assert code == 4
            assert result == text

    def test_unknown_format(self):
        """Test unknown format"""
        test_cases = [
            "This is a random text.",
            "Copyright 2023 LexisNexis.",
            "Written by someone else.",
            "",
        ]

        for text in test_cases:
            code, result = self.helper.validate_with_regex(text)
            assert code == 5
            assert result == text

    def test_detect_format_type(self):
        """Test format type detection"""
        test_cases = [
            ("Authored by team.", "external_author"),
            ("Authored by John. Updated by team.", "external_team"),
            ("Authored by John (NSW) Jane (VIC). Updated by team.", "mutiple_authors"),
            ("Adapted from PG Law authored by John.", "adapted_from"),
            ("Random text", "unknown"),
        ]

        for text, expected_type in test_cases:
            detected_type = self.helper._detect_format_type(text)
            assert detected_type == expected_type, f"Failed for: {text}"

    def test_comprehensive_validation_cases(self):
        """Test comprehensive validation cases - matches our proven test cases"""
        test_cases = [
            # Test 1: Correct PG team format
            ("Authored by the LexisNexis Legal Writer team.", 1, "Authored by the LexisNexis Legal Writer team."),
            # Test 2: Correct external team format
            (
                "Authored by John Smith. Updated by the LexisNexis Legal Writer team.",
                2,
                "Authored by John Smith. Updated by the LexisNexis Legal Writer team.",
            ),
            # Test 3: Correct multiple authors format
            (
                "Authored by John Smith (NSW); Jane Doe (VIC). Updated by the LexisNexis Legal Writer team.",
                3,
                "Authored by John Smith (NSW); Jane Doe (VIC). Updated by the LexisNexis Legal Writer team.",
            ),
            # Test 4: Correct adapted format
            (
                "Adapted from PG Contract Law/Topic: Formation/Contract Formation Guide authored by John Smith.",
                4,
                "Adapted from PG Contract Law/Topic: Formation/Contract Formation Guide authored by John Smith.",
            ),
            # Test 5: Missing "the" - should be corrected
            (
                "Authored by John Smith. Updated by LexisNexis Legal Writer team.",
                2,
                "Authored by John Smith. Updated by the LexisNexis Legal Writer team.",
            ),
            # Test 6: Wrong start - should be corrected
            (
                "Originally authored by Anna Walsh, Principal. Updated by the LexisNexis Legal Writer team.",
                2,
                "Authored by Anna Walsh, Principal. Updated by the LexisNexis Legal Writer team.",
            ),
            # Test 7: Wrong format with comma - should be corrected
            (
                "Authored by John Smith， and updated by the LexisNexis Legal Writer team.",
                2,
                "Authored by John Smith. Updated by the LexisNexis Legal Writer team.",
            ),
            (
                "Authored by the LexisNexis Legal Writer team. Updated Ben Majoe, Special Counsel, HHG Legal Group (WA).",
                8,
                "Authored by the LexisNexis Legal Writer team. Updated by Ben Majoe, Special Counsel, HHG Legal Group (WA).",
            ),
            (
                "Authored by Robert Walsh, Counsel, Notary Public, Fragomen and Anna Michalska, Director, Fragomen. Updated by Cherie Wright, former Special Counsel, Fragomen, Simon Haag, former Senior Associate, Fragomen and Lucy Nguyen, Senior Associate, Fragomen.",
                2,
                "Authored by Robert Walsh, Counsel, Notary Public, Fragomen and Anna Michalska, Director, Fragomen. Updated by Cherie Wright, Former Special Counsel; Simon Haag, Former Senior Associate and Lucy Nguyen, Senior Associate, Fragomen.",
            ),
        ]

        for i, (original, expected_code, expected_result) in enumerate(test_cases, 1):
            code, result = self.helper.validate_with_regex(original)
            assert code == expected_code, f"Test {i} - Code mismatch for: {original}"
            assert result == expected_result, f"Test {i} - Result mismatch for: {original}"

    def test_consolidate_company_names(self):
        """Test company name consolidation for same company authors"""
        test_cases = [
            # Same company, should consolidate
            (
                "Robert Walsh, Counsel, Notary Public, Fragomen and Anna Michalska, Director, Fragomen",
                "Robert Walsh, Counsel, Notary Public and Anna Michalska, Director, Fragomen",
            ),
            # Different companies, should not consolidate
            (
                "John Smith, Partner, Law Firm A and Jane Doe, Director, Law Firm B",
                "John Smith, Partner, Law Firm A and Jane Doe, Director, Law Firm B",
            ),
            # Single author, no change
            ("John Smith, Partner, Law Firm A", "John Smith, Partner, Law Firm A"),
            # No company names, no change
            ("John Smith and Jane Doe", "John Smith and Jane Doe"),
        ]

        for original, expected in test_cases:
            result = self.helper._consolidate_company_names(original)
            assert result == expected, f"Failed for: {original}"

    def test_complex_company_consolidation_case(self):
        """Test the complex case from Test 8"""
        original = "Authored by Robert Walsh, Counsel, Notary Public, Fragomen and Anna Michalska, Director, Fragomen. Updated by Cherie Wright, former Special Counsel, Fragomen, Simon Haag, former Senior Associate, Fragomen and Lucy Nguyen, Senior Associate, Fragomen."

        code, result = self.helper.validate_with_regex(original)

        # Should be identified as external_team format
        assert code == 2

        # Should fix ending (company consolidation is a future enhancement)
        expected_result = "Authored by Robert Walsh, Counsel, Notary Public, Fragomen and Anna Michalska, Director, Fragomen. Updated by Cherie Wright, Former Special Counsel; Simon Haag, Former Senior Associate and Lucy Nguyen, Senior Associate, Fragomen."
        assert result == expected_result

    def test_is_author_paragraph(self):
        """Test the is_author_paragraph method"""
        # Should return True for author paragraphs
        author_cases = [
            "Authored by the LexisNexis Legal Writer team.",
            "Authored by John Smith. Updated by the team.",
            "Authored by John (NSW); Jane (VIC). Updated by team.",
            "Adapted from PG Contract Law authored by John.",
            "Originally authored by Anna Walsh.",
        ]

        for case in author_cases:
            assert self.helper.is_author_paragraph(case), f"Should identify as author paragraph: {case}"

        # Should return False for non-author paragraphs
        non_author_cases = [
            "This is a regular paragraph.",
            "Copyright 2023 LexisNexis.",
            "Written by someone else.",
            "Random text without author attribution.",
            "",
        ]

        for case in non_author_cases:
            assert not self.helper.is_author_paragraph(case), f"Should NOT identify as author paragraph: {case}"

    def test_extract_tag_and_compare_matched(self):
        """Test tag extraction and comparison - matched case"""
        run_style = "<i><b>Authored by John Smith. Updated by team.</b></i>"
        para_content = "Authored by John Smith. Updated by team."

        match_status, indexes = self.helper.extract_tag_and_compare(run_style, para_content)
        assert match_status == "matched"
        assert indexes == []

    def test_extract_tag_and_compare_not_matched(self):
        """Test tag extraction and comparison - not matched case"""
        run_style = "<i><b>Authored by John Smith</b></i>"
        para_content = "Authored by John Smith. Updated by team."

        match_status, indexes = self.helper.extract_tag_and_compare(run_style, para_content)
        assert match_status == "not matched"
        assert len(indexes) > 0

    def test_extract_tag_and_compare_no_tags(self):
        """Test tag extraction and comparison - no tags case"""
        run_style = "Plain text without tags"
        para_content = "Plain text without tags"

        match_status, indexes = self.helper.extract_tag_and_compare(run_style, para_content)
        assert match_status == "not match"
        assert indexes == [[0, len(para_content)]]

    def test_fix_text_by_start_end_pattern(self):
        """Test start and end pattern fixing method"""
        test_cases = [
            # (original, expected_start, expected_end, expected_result)
            ("Hello world", "Hi", "earth", "Hi earth"),  # Both start and end provided
            ("Authored by John", "Authored by ", "team.", "Authored by John team."),  # Preserve middle
            ("", "Start", "End", "Start End"),  # Empty string
            ("Short", "Very long start", "Very long end", "Very long start Very long end"),  # Replace all
            ("Perfect match", "Perfect", "match", "Perfect match"),  # Already matches
        ]

        for original, start, end, expected in test_cases:
            result = self.helper._fix_text_by_start_end_pattern(original, start, end)
            assert result == expected, f"Failed for: {original}"

    def test_construct_patch_content(self):
        """Test content patch construction"""
        para_id = "test123"
        code = 1
        xml_content = {
            "Elements": [{"ElementId": "test123", "Segments": [{"SegmentId": "seg1"}, {"SegmentId": "seg2"}]}]
        }

        patches = self.helper.construct_patch_content(para_id, code, xml_content)
        assert len(patches) == 1
        assert patches[0]["op"] == "commentAdd"
        assert len(patches[0]["target"]) == 2

    def test_construct_patch_style(self):
        """Test style patch construction"""
        para_id = "test123"
        code = 1
        indexes = [(0, 5)]
        xml_content = {
            "Elements": [
                {
                    "ElementId": "test123",
                    "Segments": [
                        {"SegmentId": "seg1", "Start": 0, "End": 10},
                        {"SegmentId": "seg2", "Start": 10, "End": 20},
                    ],
                }
            ]
        }

        patches = self.helper.construct_patch_style(para_id, code, indexes, xml_content)
        assert len(patches) == 1
        assert patches[0]["op"] == "commentAdd"


class TestAuthorCheckIntegration:
    """AuthorCheckHelper integration tests"""

    def setup_method(self):
        self.helper = AuthorCheckHelper()

    def test_full_workflow_external_team(self):
        """Test external team complete workflow"""
        bug_data = {
            "para_id": "17AEA1BA",
            "para_content": "Authored by John Swinson, Partner, King & Wood Mallesons, and updated by the LexisNexis Legal Writer team.",
            "run_style_text": "<i><b>Authored by John Swinson, Partner, King & Wood Mallesons, and updated </b></i><i><b>by the LexisNexis Legal Writer team.</b></i>",
        }

        # Test content validation
        code, modified_text = self.helper.validate_with_regex(bug_data["para_content"])
        assert code == 2  # external_team
        assert modified_text != bug_data["para_content"]  # Should be fixed

        # Test tag comparison
        match_status, indexes = self.helper.extract_tag_and_compare(
            bug_data["run_style_text"], bug_data["para_content"]
        )

    def test_edge_cases(self):
        """Test edge cases"""
        edge_cases = [
            "",  # Empty string
            " ",  # Only whitespace
            "A",  # Single character
            "Authored",  # Incomplete start
            "by team.",  # Incomplete end
            "Authored by. Updated by.",  # Empty author name
            "Authored by John Doe (NSW) (VIC). Updated by team.",  # Multiple brackets
        ]

        for case in edge_cases:
            # Ensure no exceptions are thrown
            try:
                code, result = self.helper.validate_with_regex(case)
                assert isinstance(code, int)
                assert isinstance(result, str)
            except Exception as e:
                pytest.fail(f"Unexpected exception for case '{case}': {e}")


class TestAuthorCheckPerformance:
    """AuthorCheckHelper performance tests"""

    def setup_method(self):
        self.helper = AuthorCheckHelper()

    def test_performance_large_text(self):
        """Test large text processing performance"""
        import time

        # Create large text
        large_text = "Authored by " + "John Smith, " * 1000 + "Updated by the LexisNexis Legal Writer team."

        start_time = time.time()
        code, result = self.helper.validate_with_regex(large_text)
        end_time = time.time()

        # Ensure completion within reasonable time (e.g., 1 second)
        assert end_time - start_time < 1.0
        assert isinstance(code, int)
        assert isinstance(result, str)


if __name__ == "__main__":
    # Run tests
    pytest.main([__file__, "-v"])

    # Or run examples directly
    helper = AuthorCheckHelper()

    print("=== Test Examples ===")
    test_cases = [
        "Authored by the LexisNexis Legal Writer team.",
        "Authored by John Smith. Updated by LexisNexis Legal Writer team.",
        "Authored by John (NSW) and Jane (VIC). Updated by the LexisNexis Legal Writer team.",
        "Adapted from PG Consumer Protection authored by Jane Smith.",
        "Random text that doesn't match any pattern.",
    ]

    for i, text in enumerate(test_cases, 1):
        print(f"\n--- Test Case {i} ---")
        print(f"Original: {text}")
        code, result = helper.validate_with_regex(text)
        print(f"Error Code: {code}")
        print(f"Fixed: {result}")
        print(f"Needs Fix: {'Yes' if result != text else 'No'}")
