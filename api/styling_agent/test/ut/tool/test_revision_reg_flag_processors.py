"""
RevisionRegTool 标志处理器的专项测试

本文件专门测试 revision_reg.py 中新增的标志处理器功能：
- BaseFlagProcessor 抽象基类
- NavigationSectionFlagProcessor
- ReferenceFlagProcessor
- OtherResourcesFlagProcessor
- TitleFlagProcessor
"""

import pytest
from unittest.mock import MagicMock, patch
from base.tool.revision_reg import (
    BaseFlagProcessor,
    NavigationSectionFlagProcessor,
    ReferenceFlagProcessor,
    OtherResourcesFlagProcessor,
    TitleFlagProcessor,
    RevisionRegToolAnswer,
    HEADING1_STYLE,
)


class TestBaseFlagProcessor:
    """BaseFlagProcessor 抽象基类测试"""

    def test_cannot_instantiate_abstract_class(self):
        """测试不能直接实例化抽象基类"""
        with pytest.raises(TypeError, match="Can't instantiate abstract class"):
            BaseFlagProcessor()

    def test_reset_all_flags(self):
        """测试重置所有标志的功能"""
        # 创建一个具体的处理器实例用于测试
        processor = NavigationSectionFlagProcessor()

        # 创建一个包含各种标志的测试数据
        test_result: RevisionRegToolAnswer = {
            "para_id": "test_id",
            "para_content": "test content",
            "par_style_text": "<pStyle>Normal</pStyle>",
            "run_style_text": "<b>bold text</b>",
            "links": [],
            "quotation": True,
            "link": True,
            "abbr": True,
            "capital": True,
            "leg_ac": True,
            "heading": True,
            "emphasis": True,
            "grammar": True,
            "spelling": True,
        }

        # 重置标志
        result = processor.reset_all_flags(test_result)

        # 验证所有标志都被重置为 False
        assert result["quotation"] is False
        assert result["link"] is False
        assert result["abbr"] is False
        assert result["capital"] is False
        assert result["leg_ac"] is False
        assert result["heading"] is False
        assert result["emphasis"] is False
        assert result["grammar"] is False
        assert result["spelling"] is False

        # 验证其他数据不变
        assert result["para_id"] == "test_id"
        assert result["para_content"] == "test content"
        assert result["par_style_text"] == "<pStyle>Normal</pStyle>"
        assert result["run_style_text"] == "<b>bold text</b>"
        assert result["links"] == []


class TestNavigationSectionFlagProcessor:
    """NavigationSectionFlagProcessor 测试套件"""

    @pytest.fixture
    def processor(self):
        """创建处理器实例"""
        return NavigationSectionFlagProcessor()

    @pytest.fixture
    def mock_logger(self):
        """创建模拟日志器"""
        return MagicMock()

    @pytest.fixture
    def sample_xml_content_with_jump_section(self):
        """包含 jump section 的示例 XML 内容"""
        return {
            "Elements": [
                {"ElementId": "elem1", "PlainText": "Some other content"},
                {"ElementId": "jump_elem", "PlainText": "jump to section"},
                {"ElementId": "section_elem", "PlainText": "Introduction | Overview | Details | Conclusion"},
                {"ElementId": "intro_elem", "PlainText": "Introduction"},
                {"ElementId": "overview_elem", "PlainText": "Overview"},
                {"ElementId": "details_elem", "PlainText": "Details"},
                {"ElementId": "conclusion_elem", "PlainText": "Conclusion"},
            ]
        }

    @pytest.fixture
    def sample_result_map(self):
        """示例结果映射"""
        return {
            "jump_elem": {
                "para_id": "jump_elem",
                "para_content": "jump to section",
                "quotation": True,
                "link": False,
                "heading": False,
            },
            "intro_elem": {
                "para_id": "intro_elem",
                "para_content": "Introduction",
                "quotation": False,
                "link": True,
                "heading": False,
            },
            "overview_elem": {
                "para_id": "overview_elem",
                "para_content": "Overview",
                "quotation": True,
                "link": True,
                "heading": False,
            },
            "details_elem": {
                "para_id": "details_elem",
                "para_content": "Details",
                "quotation": False,
                "link": False,
                "heading": True,
            },
            "conclusion_elem": {
                "para_id": "conclusion_elem",
                "para_content": "Conclusion",
                "quotation": True,
                "link": True,
                "heading": True,
            },
        }

    def test_find_jump_section_index_found(self, processor, sample_xml_content_with_jump_section):
        """测试找到 jump section 索引"""
        elements = sample_xml_content_with_jump_section["Elements"]
        index = processor._find_jump_section_index(elements)
        assert index == 1

    def test_find_jump_section_index_not_found(self, processor):
        """测试未找到 jump section"""
        elements = [{"PlainText": "Some content"}, {"PlainText": "Other content"}]
        index = processor._find_jump_section_index(elements)
        assert index is None

    def test_find_jump_section_index_case_insensitive(self, processor):
        """测试大小写不敏感的查找"""
        elements = [
            {"PlainText": "JUMP TO SECTION"},
            {"PlainText": "Jump To Section"},
            {"PlainText": "jump TO section"},
        ]
        # 应该找到第一个匹配的
        index = processor._find_jump_section_index(elements)
        assert index == 0

    def test_parse_heading_names(self, processor):
        """测试解析标题名称"""
        section_text = "Introduction | Overview | Details | Conclusion"
        heading_names = processor._parse_heading_names(section_text)
        expected = {"Introduction", "Overview", "Details", "Conclusion"}
        assert heading_names == expected

    def test_parse_heading_names_with_spaces(self, processor):
        """测试解析包含空格的标题名称"""
        section_text = " Introduction |  Overview  | Details| Conclusion "
        heading_names = processor._parse_heading_names(section_text)
        expected = {"Introduction", "Overview", "Details", "Conclusion"}
        assert heading_names == expected

    def test_parse_heading_names_empty_items(self, processor):
        """测试解析包含空项的标题"""
        section_text = "Introduction || Overview | | Details"
        heading_names = processor._parse_heading_names(section_text)
        expected = {"Introduction", "Overview", "Details"}
        assert heading_names == expected

    def test_find_heading_elements(self, processor, sample_xml_content_with_jump_section, sample_result_map):
        """测试查找标题元素"""
        elements = sample_xml_content_with_jump_section["Elements"]
        heading_names = {"Introduction", "Overview", "Details"}

        heading_elements = processor._find_heading_elements(elements, heading_names, sample_result_map)

        # 应该找到3个元素
        assert len(heading_elements) == 3

        # 验证找到的元素
        found_ids = {elem["para_id"] for elem in heading_elements}
        expected_ids = {"intro_elem", "overview_elem", "details_elem"}
        assert found_ids == expected_ids

    def test_find_heading_elements_partial_match(
        self, processor, sample_xml_content_with_jump_section, sample_result_map
    ):
        """测试部分匹配的标题元素"""
        elements = sample_xml_content_with_jump_section["Elements"]
        heading_names = {"Introduction", "NonExistent", "Overview"}

        heading_elements = processor._find_heading_elements(elements, heading_names, sample_result_map)

        # 应该只找到2个存在的元素
        assert len(heading_elements) == 2
        found_ids = {elem["para_id"] for elem in heading_elements}
        expected_ids = {"intro_elem", "overview_elem"}
        assert found_ids == expected_ids

    def test_find_heading_elements_missing_from_result_map(self, processor, mock_logger):
        """测试元素在XML中但不在结果映射中的情况"""
        processor.logger = mock_logger

        elements = [{"ElementId": "missing_elem", "PlainText": "Introduction"}]
        heading_names = {"Introduction"}
        result_map = {}  # 空的结果映射

        heading_elements = processor._find_heading_elements(elements, heading_names, result_map)

        # 应该返回空列表
        assert heading_elements == []

        # 应该记录警告
        mock_logger.warning.assert_called_once_with("Element ID missing_elem not found in result map")

    def test_apply_heading_flags_to_elements(self, processor):
        """测试为标题元素应用标志设置"""
        heading_elements = [
            {"para_id": "elem1", "quotation": True, "link": True, "heading": False},
            {"para_id": "elem2", "quotation": False, "link": False, "heading": True},
        ]

        overrides = processor._apply_heading_flags_to_elements(heading_elements)

        # 验证返回正确数量的覆盖
        assert len(overrides) == 2

        # 验证第一个元素
        elem1_override = overrides["elem1"]
        assert elem1_override["heading"] is True
        assert elem1_override["par_style_text"] == HEADING1_STYLE
        assert elem1_override["quotation"] is False  # 应该被重置
        assert elem1_override["link"] is False  # 应该被重置

        # 验证第二个元素
        elem2_override = overrides["elem2"]
        assert elem2_override["heading"] is True
        assert elem2_override["par_style_text"] == HEADING1_STYLE
        assert elem2_override["quotation"] is False  # 应该被重置
        assert elem2_override["link"] is False  # 应该被重置

    def test_apply_flags_complete_workflow(self, processor, sample_xml_content_with_jump_section, sample_result_map):
        """测试完整的标志应用工作流程"""
        overrides = processor.apply_flags(sample_xml_content_with_jump_section, sample_result_map)

        # 应该包含 jump section 元素和标题元素的覆盖
        assert "jump_elem" in overrides
        assert "intro_elem" in overrides
        assert "overview_elem" in overrides
        assert "details_elem" in overrides
        assert "conclusion_elem" in overrides

        # 验证 jump section 元素
        jump_override = overrides["jump_elem"]
        assert jump_override["heading"] is True
        assert jump_override["par_style_text"] == HEADING1_STYLE

        # 验证标题元素都被正确设置
        for elem_id in ["intro_elem", "overview_elem", "details_elem", "conclusion_elem"]:
            override = overrides[elem_id]
            assert override["heading"] is True
            assert override["par_style_text"] == HEADING1_STYLE
            # 验证其他标志被重置
            assert override["quotation"] is False
            assert override["link"] is False

    def test_apply_flags_no_jump_section(self, processor):
        """测试没有 jump section 的情况"""
        xml_content = {
            "Elements": [
                {"ElementId": "elem1", "PlainText": "Some content"},
                {"ElementId": "elem2", "PlainText": "Other content"},
            ]
        }
        result_map = {}

        overrides = processor.apply_flags(xml_content, result_map)

        # 应该返回空字典
        assert overrides == {}

    def test_apply_flags_jump_element_not_in_result_map(self, processor):
        """测试 jump 元素不在结果映射中的情况"""
        xml_content = {
            "Elements": [
                {"ElementId": "jump_elem", "PlainText": "jump to section"},
                {"ElementId": "section_elem", "PlainText": "Introduction"},
            ]
        }
        result_map = {}  # jump_elem 不在结果映射中

        overrides = processor.apply_flags(xml_content, result_map)

        # 应该返回空字典，因为 jump_elem 不在 result_map 中
        assert overrides == {}


class TestReferenceFlagProcessor:
    """ReferenceFlagProcessor 测试套件"""

    @pytest.fixture
    def processor(self):
        """创建处理器实例"""
        return ReferenceFlagProcessor()

    @pytest.fixture
    def mock_logger(self):
        """创建模拟日志器"""
        return MagicMock()

    def test_apply_flags_with_references(self, processor):
        """测试包含引用的情况"""
        xml_content = {
            "Elements": [
                {"ElementId": "ref1", "PlainText": "References: Some legal reference"},
                {"ElementId": "ref2", "PlainText": "References: Another reference"},
                {"ElementId": "normal", "PlainText": "Normal paragraph"},
            ]
        }

        result_map = {
            "ref1": {"para_id": "ref1", "quotation": True, "link": False, "abbr": True},
            "ref2": {"para_id": "ref2", "quotation": False, "link": True, "abbr": False},
            "normal": {"para_id": "normal", "quotation": True, "link": True, "abbr": True},
        }

        overrides = processor.apply_flags(xml_content, result_map)

        # 应该只处理引用元素
        assert len(overrides) == 2
        assert "ref1" in overrides
        assert "ref2" in overrides
        assert "normal" not in overrides

        # 验证引用元素的标志设置
        ref1_override = overrides["ref1"]
        assert ref1_override["link"] is True
        assert ref1_override["quotation"] is False  # 应该被重置
        assert ref1_override["abbr"] is False  # 应该被重置

        ref2_override = overrides["ref2"]
        assert ref2_override["link"] is True
        assert ref2_override["quotation"] is False  # 应该被重置
        assert ref2_override["abbr"] is False  # 应该被重置

    def test_apply_flags_no_references(self, processor):
        """测试没有引用的情况"""
        xml_content = {
            "Elements": [
                {"ElementId": "elem1", "PlainText": "Normal content"},
                {"ElementId": "elem2", "PlainText": "Other content"},
            ]
        }
        result_map = {"elem1": {"para_id": "elem1"}, "elem2": {"para_id": "elem2"}}

        overrides = processor.apply_flags(xml_content, result_map)

        assert overrides == {}

    def test_apply_flags_reference_not_in_result_map(self, processor, mock_logger):
        """测试引用元素不在结果映射中的情况"""
        processor.logger = mock_logger

        xml_content = {"Elements": [{"ElementId": "ref1", "PlainText": "References: Some reference"}]}
        result_map = {}  # 空的结果映射

        overrides = processor.apply_flags(xml_content, result_map)

        assert overrides == {}
        mock_logger.warning.assert_called_once_with("Element ID ref1 not found in result map")

    def test_apply_flags_partial_references_match(self, processor):
        """测试部分包含 'References:' 的文本"""
        xml_content = {
            "Elements": [
                {"ElementId": "ref1", "PlainText": "References: Legal case"},
                {"ElementId": "not_ref", "PlainText": "This references something"},  # 不是以 References: 开头
                {"ElementId": "ref2", "PlainText": "References: Another case"},
            ]
        }

        result_map = {
            "ref1": {"para_id": "ref1", "link": False},
            "not_ref": {"para_id": "not_ref", "link": False},
            "ref2": {"para_id": "ref2", "link": False},
        }

        overrides = processor.apply_flags(xml_content, result_map)

        # 只有以 "References:" 开头的才会被处理
        assert len(overrides) == 2
        assert "ref1" in overrides
        assert "ref2" in overrides
        assert "not_ref" not in overrides

        assert overrides["ref1"]["link"] is True
        assert overrides["ref2"]["link"] is True


class TestOtherResourcesFlagProcessor:
    """OtherResourcesFlagProcessor 测试套件"""

    @pytest.fixture
    def processor(self):
        """创建处理器实例"""
        return OtherResourcesFlagProcessor()

    @pytest.fixture
    def mock_logger(self):
        """创建模拟日志器"""
        return MagicMock()

    def test_apply_flags_with_other_resources(self, processor):
        """测试包含 Other Resources 的情况"""
        xml_content = {
            "Elements": [
                {"ElementId": "elem1", "PlainText": "Some content"},
                {"ElementId": "other_res", "PlainText": "Other Resources"},
                {"ElementId": "resource1", "PlainText": "Resource 1"},
                {"ElementId": "resource2", "PlainText": "Resource 2"},
                {"ElementId": "resource3", "PlainText": "Resource 3"},
            ]
        }

        result_map = {
            "elem1": {"para_id": "elem1", "link": True},
            "other_res": {"para_id": "other_res", "link": True, "quotation": True},
            "resource1": {"para_id": "resource1", "link": False, "abbr": True},
            "resource2": {"para_id": "resource2", "link": True, "quotation": True},
            "resource3": {"para_id": "resource3", "link": False, "emphasis": True},
        }

        overrides = processor.apply_flags(xml_content, result_map)

        # 应该处理 Other Resources 标题和后续元素
        assert len(overrides) == 4  # other_res + 3 resource elements
        assert "other_res" in overrides
        assert "resource1" in overrides
        assert "resource2" in overrides
        assert "resource3" in overrides
        assert "elem1" not in overrides

        # 验证 Other Resources 标题元素被重置
        other_res_override = overrides["other_res"]
        assert other_res_override["link"] is False  # 所有标志应该被重置
        assert other_res_override["quotation"] is False

        # 验证后续资源元素设置了 link 标志
        for res_id in ["resource1", "resource2", "resource3"]:
            res_override = overrides[res_id]
            assert res_override["link"] is True
            # 其他标志应该被重置
            assert res_override.get("quotation", True) is False  # 默认或重置为 False
            assert res_override.get("abbr", True) is False
            assert res_override.get("emphasis", True) is False

    def test_apply_flags_no_other_resources(self, processor):
        """测试没有 Other Resources 的情况"""
        xml_content = {
            "Elements": [
                {"ElementId": "elem1", "PlainText": "Some content"},
                {"ElementId": "elem2", "PlainText": "Other content"},
            ]
        }
        result_map = {"elem1": {"para_id": "elem1"}, "elem2": {"para_id": "elem2"}}

        overrides = processor.apply_flags(xml_content, result_map)

        assert overrides == {}

    def test_apply_flags_other_resources_at_end(self, processor):
        """测试 Other Resources 在文档末尾的情况"""
        xml_content = {
            "Elements": [
                {"ElementId": "elem1", "PlainText": "Some content"},
                {"ElementId": "other_res", "PlainText": "Other Resources"},
            ]
        }

        result_map = {"elem1": {"para_id": "elem1"}, "other_res": {"para_id": "other_res", "quotation": True}}

        overrides = processor.apply_flags(xml_content, result_map)

        # 应该只处理 Other Resources 标题
        assert len(overrides) == 1
        assert "other_res" in overrides

        # 验证标志被重置
        other_res_override = overrides["other_res"]
        assert other_res_override["quotation"] is False

    def test_apply_flags_element_not_in_result_map(self, processor, mock_logger):
        """测试元素不在结果映射中的情况"""
        processor.logger = mock_logger

        xml_content = {
            "Elements": [
                {"ElementId": "other_res", "PlainText": "Other Resources"},
                {"ElementId": "missing_elem", "PlainText": "Missing resource"},
            ]
        }

        result_map = {"other_res": {"para_id": "other_res"}}

        overrides = processor.apply_flags(xml_content, result_map)

        # 应该只处理存在于 result_map 中的元素
        assert len(overrides) == 1
        assert "other_res" in overrides
        assert "missing_elem" not in overrides

        # 应该记录警告
        mock_logger.warning.assert_called_once_with("Element ID missing_elem not found in result map")


class TestTitleFlagProcessor:
    """TitleFlagProcessor 测试套件"""

    @pytest.fixture
    def processor(self):
        """创建处理器实例"""
        return TitleFlagProcessor()

    @pytest.fixture
    def mock_logger(self):
        """创建模拟日志器"""
        return MagicMock()

    def test_apply_flags_with_heading1_style(self, processor):
        """测试包含 Heading1 样式的元素"""
        xml_content = {
            "Elements": [
                {"ElementId": "title_elem", "Properties": {"Style": "Heading1"}},
                {"ElementId": "normal_elem", "Properties": {"Style": "Normal"}},
                {"ElementId": "other_heading", "Properties": {"Style": "Heading2"}},
            ]
        }

        result_map = {
            "title_elem": {"para_id": "title_elem", "quotation": True, "link": True, "heading": False},
            "normal_elem": {"para_id": "normal_elem", "quotation": False, "link": False, "heading": False},
            "other_heading": {"para_id": "other_heading", "quotation": True, "link": False, "heading": False},
        }

        overrides = processor.apply_flags(xml_content, result_map)

        # 应该只处理第一个 Heading1 元素
        assert len(overrides) == 1
        assert "title_elem" in overrides

        # 验证标志设置
        title_override = overrides["title_elem"]
        assert title_override["heading"] is True
        assert title_override["par_style_text"] == HEADING1_STYLE
        assert title_override["quotation"] is False  # 应该被重置
        assert title_override["link"] is False  # 应该被重置

    def test_apply_flags_no_heading1_style(self, processor):
        """测试没有 Heading1 样式的情况"""
        xml_content = {
            "Elements": [
                {"ElementId": "elem1", "Properties": {"Style": "Normal"}},
                {"ElementId": "elem2", "Properties": {"Style": "Heading2"}},
                {"ElementId": "elem3", "Properties": {"Style": "Heading3"}},
            ]
        }

        result_map = {"elem1": {"para_id": "elem1"}, "elem2": {"para_id": "elem2"}, "elem3": {"para_id": "elem3"}}

        overrides = processor.apply_flags(xml_content, result_map)

        assert overrides == {}

    def test_apply_flags_heading1_not_in_result_map(self, processor, mock_logger):
        """测试 Heading1 元素不在结果映射中的情况"""
        processor.logger = mock_logger

        xml_content = {"Elements": [{"ElementId": "title_elem", "Properties": {"Style": "Heading1"}}]}

        result_map = {}

        overrides = processor.apply_flags(xml_content, result_map)

        assert overrides == {}
        mock_logger.warning.assert_called_once_with("Element ID title_elem not found in result map")

    def test_apply_flags_multiple_heading1_only_first(self, processor):
        """测试多个 Heading1 元素只处理第一个"""
        xml_content = {
            "Elements": [
                {"ElementId": "title1", "Properties": {"Style": "Heading1"}},
                {"ElementId": "title2", "Properties": {"Style": "Heading1"}},
                {"ElementId": "title3", "Properties": {"Style": "Heading1"}},
            ]
        }

        result_map = {
            "title1": {"para_id": "title1", "heading": False},
            "title2": {"para_id": "title2", "heading": False},
            "title3": {"para_id": "title3", "heading": False},
        }

        overrides = processor.apply_flags(xml_content, result_map)

        # 应该只处理第一个
        assert len(overrides) == 1
        assert "title1" in overrides
        assert "title2" not in overrides
        assert "title3" not in overrides

        title_override = overrides["title1"]
        assert title_override["heading"] is True
        assert title_override["par_style_text"] == HEADING1_STYLE

    def test_apply_flags_missing_properties(self, processor):
        """测试缺少 Properties 的元素"""
        xml_content = {
            "Elements": [
                {"ElementId": "elem1"},  # 没有 Properties
                {"ElementId": "elem2", "Properties": {}},  # 空的 Properties
                {"ElementId": "elem3", "Properties": {"Style": "Heading1"}},
            ]
        }

        result_map = {
            "elem1": {"para_id": "elem1"},
            "elem2": {"para_id": "elem2"},
            "elem3": {"para_id": "elem3", "heading": False},
        }

        overrides = processor.apply_flags(xml_content, result_map)

        # 应该只处理有正确样式的元素
        assert len(overrides) == 1
        assert "elem3" in overrides

        elem3_override = overrides["elem3"]
        assert elem3_override["heading"] is True
        assert elem3_override["par_style_text"] == HEADING1_STYLE


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
