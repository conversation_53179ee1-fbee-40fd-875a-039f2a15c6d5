import json

from unittest.mock import patch, MagicMock
from base.planner_handler import lambda_handler, async_planner_handler
from base.util.constants import DEFAULT_ERROR_MSG

@patch("base.planner_handler.try_to_get_upload_file")
@patch("base.planner_handler.generate_processing_script")
@patch("base.planner_handler.s3_client")
@patch("base.planner_handler.lambda_client")
def test_lambda_handler_valid_path(
    mock_lambda_client,
    mock_s3_client,
    mock_generate_processing_script,
    mock_try_to_get_upload_file, 
):
    event = {"rawPath": "/"}
    context = MagicMock()
    context.aws_request_id = "test-request-id"

    mock_try_to_get_upload_file.return_value = {"name": "1.docx", "bytes": b"test content"}
    mock_generate_processing_script.return_value = ""
    mock_s3_client.put_object.return_value = True
    mock_lambda_client.invoke.return_value = {
        "statusCode": 200, 
        "body": json.dumps({"code": "0000", "message": "Success", "data": {"presigned_url": "http://example.com"}
        })
    }

    response = lambda_handler(event, context)

    assert response == {"statusCode": 200, "body": '{"downloadUrl": "http://example.com"}', "headers": {'Content-Type': 'application/json'}}

def test_lambda_handler_invalid_path():
    event = {"rawPath": "/invalid"}
    context = MagicMock()
    context.aws_request_id = "test-request-id"

    response = lambda_handler(event, context)

    assert response == {"statusCode": 400, "body": '{"message": "Invalid path"}', "headers": {'Content-Type': 'application/json'}}


@patch("base.planner_handler.try_to_get_upload_file")
@patch("base.planner_handler.generate_processing_script")
@patch("base.planner_handler.s3_client")
def test_lambda_handler_planner_inner_error(    
    mock_s3_client,
    mock_generate_processing_script,
    mock_try_to_get_upload_file, 
):
    event = {"rawPath": "/"}
    context = MagicMock()
    context.aws_request_id = "test-request-id"

    mock_try_to_get_upload_file.return_value = {"name": "1.docx", "bytes": b"test content"}
    mock_generate_processing_script.return_value = ""
    mock_s3_client.put_object.return_value = False

    response = lambda_handler(event, context)

    assert response == {"statusCode": 500, "body": '{"message": "Generic error encountered while processing, please contact admin and try again later."}', "headers": {'Content-Type': 'application/json'}}


@patch("base.planner_handler.try_to_get_upload_file")
@patch("base.planner_handler.generate_processing_script")
@patch("base.planner_handler.s3_client")
def test_lambda_handler_unexpected_error(
    mock_s3_client,
    mock_generate_processing_script,
    mock_try_to_get_upload_file, 
):
    event = {"rawPath": "/"}
    context = MagicMock()
    context.aws_request_id = "test-request-id"

    mock_try_to_get_upload_file.return_value = {"name": "1.docx", "bytes": b"test content"}
    mock_generate_processing_script.return_value = None
    mock_s3_client.put_object.return_value = True

    response = lambda_handler(event, context)

    assert response == {"statusCode": 500, "body": '{"message": "Generic error encountered while processing, please contact admin and try again later."}', "headers": {'Content-Type': 'application/json'}}


@patch("base.planner_handler.try_to_get_email")
@patch("base.planner_handler.base_handler")
def test_async_planner_handler_valid_email(mock_base_handler, mock_try_to_get_email):
    event = {
        "rawPath": "/v2",
        "queryStringParameters": {"email": "<EMAIL>"}
    }
    context = MagicMock()
    context.aws_request_id = "test-request-id"

    mock_try_to_get_email.return_value = "<EMAIL>"
    mock_base_handler.return_value = None

    response = async_planner_handler(event, context)

    assert response == {"statusCode": 200, "body": '{"message": "The processed document will be sent to your email shortly. Please check your inbox."}', "headers": {'Content-Type': 'application/json'}}
    mock_base_handler.assert_called_once_with(event, "Event", "<EMAIL>")


@patch("base.planner_handler.try_to_get_email")
def test_async_planner_handler_invalid_email(mock_try_to_get_email):
    event = {
        "rawPath": "/v2",
        "queryStringParameters": {"email": "invalid-email"}
    }
    context = MagicMock()
    context.aws_request_id = "test-request-id"

    mock_try_to_get_email.return_value = "invalid-email"

    response = lambda_handler(event, context)

    assert response == {"statusCode": 400, "body": '{"message": "Invalid email format."}', "headers": {'Content-Type': 'application/json'}}


@patch("base.planner_handler.try_to_get_email")
@patch("base.planner_handler.base_handler")
def test_async_planner_handler_no_email(mock_base_handler, mock_try_to_get_email):
    event = {
        "rawPath": "/v2",
        "queryStringParameters": {}
    }
    context = MagicMock()
    context.aws_request_id = "test-request-id"

    mock_try_to_get_email.return_value = None
    mock_base_handler.return_value = None

    response = lambda_handler(event, context)

    assert response == {"statusCode": 400, "body": '{"message": "Please provide email address."}', "headers": {'Content-Type': 'application/json'}}


@patch("base.planner_handler.try_to_get_email")
@patch("base.planner_handler.base_handler")
def test_executor_error_response(mock_base_handler, mock_try_to_get_email):
    event = {
        "rawPath": "/",
        "queryStringParameters": {}
    }
    context = MagicMock()
    context.aws_request_id = "test-request-id"

    mock_try_to_get_email.return_value = "<EMAIL>"
    mock_base_handler.return_value = {"statusCode": 400, "body": '{"code": "C002", "message": "No revision found for the given document."}', "headers": {'Content-Type': 'application/json'}}

    response = lambda_handler(event, context)

    assert response == {"statusCode": 400, "body": '{"message": "No revision found for the given document."}', "headers": {'Content-Type': 'application/json'}}

    mock_try_to_get_email.return_value = "<EMAIL>"
    mock_base_handler.return_value = {"statusCode": 400, "body": '{"code": "C002", "message": ""}', "headers": {'Content-Type': 'application/json'}}

    response = lambda_handler(event, context)

    assert response == {"statusCode": 400, "body": '{"message": ' + '"' + DEFAULT_ERROR_MSG + '"' + '}', "headers": {'Content-Type': 'application/json'}}