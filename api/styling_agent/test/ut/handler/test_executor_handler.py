import pytest
from unittest.mock import patch, MagicMock

from base.executor_handler import lambda_handler
from base.error.convert import ConvertRevisionEmptyError
from base.schema.executor import ExecutorRequest, ExecutorResponse
from base.util.email_template import EMAIL_SUCCESS, EMAIL_ERROR

@pytest.fixture
def mock_context():
    return MagicMock(aws_request_id="test-request-id")

@pytest.fixture
def mock_event():
    return {
        "script": {
            "bucket": "test-bucket",
            "key": "test-key",
            "base": "test-base",
            "email": "<EMAIL>",
            "request_id": "test-request-id"
        }
    }

@patch("base.executor_handler.try_to_get_request_payload")
@patch("base.executor_handler.executor_handler")
@patch("base.executor_handler.get_document_attachment")
@patch("base.executor_handler.ses_client")
def test_lambda_handler_success(
    mock_ses_client,
    mock_get_document_attachment,
    mock_executor_handler,
    mock_try_to_get_request_payload,
    mock_event,
    mock_context
):
    mock_try_to_get_request_payload.return_value = ExecutorRequest(
        script_bucket="test-bucket",
        script_key="test-key",
        base="test-base",
        email="<EMAIL>"
    )
    mock_executor_handler.return_value = ExecutorResponse[dict](data={"bucket": "test-bucket", "key": "test-key"})
    mock_get_document_attachment.return_value = {"test.txt": b"test content"}
    mock_ses_client.invoke.return_value = True

    response = lambda_handler(mock_event, mock_context)

    mock_try_to_get_request_payload.assert_called_once_with(mock_event)
    mock_executor_handler.assert_called_once()
    mock_get_document_attachment.assert_called_once()
    assert response["statusCode"] == 200

@patch("base.executor_handler.try_to_get_request_payload")
def test_lambda_handler_invalid_payload(
    mock_try_to_get_request_payload,
    mock_event,
    mock_context
):
    mock_try_to_get_request_payload.return_value = None

    response = lambda_handler(mock_event, mock_context)

    mock_try_to_get_request_payload.assert_called_once_with(mock_event)
    assert response["statusCode"] == 400
    assert response["body"] == '{"code": "E002", "message": "Invalid payload.", "data": null}'

@patch("base.executor_handler.try_to_get_request_payload")
@patch("base.executor_handler.executor_handler")
def test_lambda_handler_convert_error(
    mock_executor_handler,
    mock_try_to_get_request_payload,
    mock_event,
    mock_context
):
    mock_try_to_get_request_payload.return_value = ExecutorRequest(
        script_bucket="test-bucket",
        script_key="test-key",
        base="test-base"
    )
    mock_executor_handler.side_effect = ConvertRevisionEmptyError()

    response = lambda_handler(mock_event, mock_context)

    mock_try_to_get_request_payload.assert_called_once_with(mock_event)
    mock_executor_handler.assert_called_once()
    assert response["statusCode"] == 400
    assert response["body"] == '{"code": "C002", "message": "No revision found for the given document.", "data": null}'

@patch("base.executor_handler.try_to_get_request_payload")
@patch("base.executor_handler.executor_handler")
def test_lambda_handler_unexpected_error(
    mock_executor_handler,
    mock_try_to_get_request_payload,
    mock_event,
    mock_context
):
    mock_try_to_get_request_payload.return_value = ExecutorRequest(
        script_bucket="test-bucket",
        script_key="test-key",
        base="test-base",
    )
    mock_executor_handler.side_effect = Exception("Unexpected error")

    response = lambda_handler(mock_event, mock_context)

    mock_try_to_get_request_payload.assert_called_once_with(mock_event)
    mock_executor_handler.assert_called_once()
    assert response["statusCode"] == 500
    assert response["body"] == '{"code": "E001", "message": "Internal server error.", "data": null}'