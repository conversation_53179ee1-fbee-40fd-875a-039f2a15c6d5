import json
import pytest
import re
from datetime import datetime

from base.process.output_transform import (
    remove_markers,
    apply_deletions,
    merge_two_texts_consensus,
    unified_baseline,
    get_marker_offset,
    map_offsets,
    merge_markers,
    generate_revision_list,
    merge_tools_outputs,
)


def test_remove_markers():
    text = "This is a {test|add|marker} example."
    assert remove_markers(text) == "This is a  example."

    text = "Multiple {tag1|add|markers} in {tag2|delete|one} text."
    assert remove_markers(text) == "Multiple  in  text."

    text = "Nested {outer|add|{inner|delete|tag}} example."
    assert remove_markers(text) == "Nested } example."

    text = "No markers in this text."
    assert remove_markers(text) == text

    assert remove_markers("") == ""


def test_apply_deletions():
    text = "Keep this {tool|delete|remove this} continue here."
    assert apply_deletions(text) == "Keep this  continue here."

    text = "Delete match {tool|delete|content}content here."
    assert apply_deletions(text) == "Delete match  here."

    text = "This is {tool|add|added} text."
    assert apply_deletions(text) == "This is  text."

    text = "Mix {tool1|delete|remove}remove and {tool2|add|add} operations."
    assert apply_deletions(text) == "Mix  and  operations."

    text = "No operations here."
    assert apply_deletions(text) == text

    assert apply_deletions("") == ""


def test_merge_two_texts_consensus():
    text1 = "Identical text."
    text2 = "Identical text."
    assert merge_two_texts_consensus(text1, text2) == "Identical text."

    text1 = "This is version one."
    text2 = "This is version two."
    result = merge_two_texts_consensus(text1, text2)
    assert result == "This is version o."

    text1 = "This is a longer segment."
    text2 = "This is short."
    result = merge_two_texts_consensus(text1, text2)
    assert result == "This is short."

    text1 = "Text with some differences here and there."
    text2 = "Text having a few variations here or there."
    result = merge_two_texts_consensus(text1, text2)
    assert len(result) <= max(len(text1), len(text2))


def test_unified_baseline():
    texts = ["This is {tool|delete|some} text.", "This is {tool|add|different} text."]
    baseline = unified_baseline(texts)
    assert baseline == "This is  text."

    texts = ["Keep {tool1|delete|remove}remove this part.", "Keep  this part."]
    baseline = unified_baseline(texts)
    assert baseline == "Keep  this part."

    texts = [
        "Text with {t1|delete|first} deletion.",
        "Text with {t2|delete|second} deletion.",
        "Text with {t3|delete|third} deletion.",
    ]
    baseline = unified_baseline(texts)
    assert baseline == "Text with  deletion."


def test_get_marker_offset():
    text = "Prefix {tool|action|content} suffix"
    match = re.search(r"\{[^}]+\}", text)
    assert get_marker_offset(text, match) == len("Prefix ")

    text = "First {t1|a1|c1} second {t2|a2|c2} third"
    match = re.search(r"\{[^}]+\}", text)  # 匹配第一个标记
    assert get_marker_offset(text, match) == len("First ")


def test_map_offsets():
    original = "Same text"
    unified = "Same text"
    offset_map = map_offsets(original, unified)
    assert offset_map[0] == 0  # 'S' maps to 'S'
    assert offset_map[4] == 4  # ' ' maps to ' '
    assert offset_map[8] == 8  # 't' maps to 't'

    original = "Original text"
    unified = "Modified text"
    offset_map = map_offsets(original, unified)
    assert offset_map


def test_merge_markers():
    texts = ["This {tool1|add|added} text.", "This {tool2|delete|deleted} text."]
    baseline, markers = merge_markers(texts)
    assert baseline == "This  text."
    assert len(markers) == 2
    assert markers[0]["tool"] == "tool1"
    assert markers[0]["action"] == "add"
    assert markers[1]["tool"] == "tool2"
    assert markers[1]["action"] == "delete"


def test_generate_revision_list():
    baseline = "This is text."
    markers = [
        {"id": 0, "tool": "test", "action": "add", "content": "sample", "offset": 5},
        {"id": 1, "tool": "test", "action": "delete", "content": "is", "offset": 5},
    ]
    rev_date = "2025-05-09 12:00:00"

    revisions = generate_revision_list(baseline, markers, rev_date)
    assert len(revisions) >= 3

    assert revisions[0]["Key"] == "Revision"
    assert revisions[0]["Type"] == "Insert"
    assert revisions[0]["Text"][0] == "This "
    assert revisions[0]["Author"] is None


def test_merge_tools_outputs():
    text_quote = (
        "Test text with {quote_check|delete|'}{quote_check|add|\"}"
        "quoted content{quote_check|delete|'}{quote_check|add|\"}"
    )
    text_legis = "Test text with {legis_check|add|(Ref)} 'quoted content'"
    text_abbr = "Test text with {abbr_check|add|abbr} 'quoted content'"

    tool_texts = [text_quote, text_legis, text_abbr]
    rev_date = "2025-05-09 12:00:00"

    revisions = merge_tools_outputs(tool_texts, rev_date)

    assert isinstance(revisions, list)
    assert len(revisions) > 0

    authors = [r["Author"] for r in revisions if r["Author"] is not None]
    assert any("AI:quote_check" in author for author in authors)
    assert any("AI:legis_check" in author for author in authors)
    assert any("AI:abbr_check" in author for author in authors)


def test_complex_scenario():
    text_quote = (
        "On 20th February 2025, the Scams Prevention Framework Act 2025 received royal assent and came into force a day later. "
        "The Act introduces a new SPF into Part IVF of the Competition and Consumer Act (Cth) which will apply to social media companies, "
        "banks, and telecommunication providers. These service providers will be required to take {quote_check|delete|'}{quote_check|add|\"}"
        "reasonable steps{quote_check|delete|'}{quote_check|add|\"} to prevent, detect, report, respond, and disrupt scams while using their services."
    )

    text_legis = (
        "On 20th February 2025, the Scams Prevention Framework Act 2025 {legis_check|add|(Cth)} received royal assent and came into force a day later. "
        "The Act introduces a new SPF into Part IVF of the Competition and Consumer Act {legis_check|add|2010} (Cth) which will apply to social media companies, "
        "banks, and telecommunication providers. These service providers will be required to take 'reasonable steps' to prevent, detect, report, respond, and disrupt scams while using their services."
    )

    text_abbr = (
        "On 20th February 2025, the Scams Prevention Framework Act 2025 {abbr_check|add|(SPF Act)} received royal assent and came into force a day later. "
        "The Act introduces a new SPF into P{abbr_check|delete|ar}t IVF of the Competition and Consumer Act (Cth) {abbr_check|add|(CC Act)} which will apply to social media companies, "
        "banks, and telecommunication providers. These service providers will be required to take 'reasonable steps' to prevent, detect, report, respond, and disrupt scams while using their services."
    )

    tool_texts = [text_quote, text_legis, text_abbr]

    revisions = merge_tools_outputs(tool_texts)

    print(json.dumps(revisions, ensure_ascii=False, indent=4))

    assert isinstance(revisions, list)
    assert len(revisions) > 0

    revision_texts = []
    for rev in revisions:
        if rev["Author"] is not None:
            revision_texts.extend(rev["Text"])

    assert any('"' in text for text in revision_texts)
    assert any("2010" in text for text in revision_texts)
    assert any("SPF Act" in text for text in revision_texts)
    assert any("ar" in text for text in revision_texts)
