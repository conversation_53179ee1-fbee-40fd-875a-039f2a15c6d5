import pytest
from lxml import etree
import time

# Import modules to test
from base.process.input_transform import (
    extract_paragraphs_xml,
    extract_paragraph_data,
    convert_json_to_xml,
    paragraph_helper,
    skip_paragraphs,
    _extract_links_from_content,
)
from base.model.document import (
    DocumentInfo,
    Annotations,
    BlockElement,
    ElementProperties,
    SegmentProperties,
    Segment,
    NativeRevisions,
    NativeRevision,
    Hyperlink,
    Comment,
)
from typing import Dict, Any


@pytest.fixture
def sample_data():
    """Sample data fixture for testing"""
    return DocumentInfo(
        Version="1.0",
        DocumentId="TEST_DOC",
        Elements=[
            BlockElement(
                Key="",
                ElementId="TEST001",
                ElementType="Paragraph",
                PlainText="This is a test paragraph with bold and italic text.",
                Properties=ElementProperties(Style="Heading1"),
                Segments=[
                    Segment(
                        SegmentId="SEG001",
                        SegmentText="This is a ",
                        Start=0,
                        End=9,
                        Properties=SegmentProperties(Bold=False, Italic=False),
                        FieldId="",
                        HyperlinkId="",
                        ContentRevisionId="",
                        PropRevisions=[],
                        CommentIds=[],
                    ),
                    Segment(
                        SegmentId="SEG002",
                        SegmentText="test",
                        Start=10,
                        End=14,
                        Properties=SegmentProperties(Bold=True, Italic=False),
                        FieldId="",
                        HyperlinkId="LINK001",
                        ContentRevisionId="",
                        PropRevisions=[],
                        CommentIds=[],
                    ),
                    Segment(
                        SegmentId="SEG003",
                        SegmentText=" paragraph with ",
                        Start=15,
                        End=30,
                        Properties=SegmentProperties(Bold=False, Italic=False),
                        FieldId="",
                        HyperlinkId="",
                        ContentRevisionId="",
                        PropRevisions=[],
                        CommentIds=[],
                    ),
                    Segment(
                        SegmentId="SEG004",
                        SegmentText="bold and italic",
                        Start=31,
                        End=46,
                        Properties=SegmentProperties(Bold=True, Italic=True),
                        FieldId="",
                        HyperlinkId="",
                        ContentRevisionId="",
                        PropRevisions=[],
                        CommentIds=["COMMENT001"],
                    ),
                    Segment(
                        SegmentId="SEG005",
                        SegmentText=" text.",
                        Start=47,
                        End=53,
                        Properties=SegmentProperties(Bold=False, Italic=False),
                        FieldId="",
                        HyperlinkId="",
                        ContentRevisionId="",
                        PropRevisions=[],
                        CommentIds=[],
                    ),
                ],
            ),
            BlockElement(
                Key="",
                ElementId="TEST002",
                ElementType="Paragraph",
                PlainText="Simple paragraph without formatting.",
                Properties=ElementProperties(Style="Normal"),
                Segments=[
                    Segment(
                        SegmentId="SEG006",
                        SegmentText="Simple paragraph without formatting.",
                        Start=0,
                        End=35,
                        Properties=SegmentProperties(Bold=False, Italic=False),
                        FieldId="",
                        HyperlinkId="",
                        ContentRevisionId="",
                        PropRevisions=[],
                        CommentIds=[],
                    )
                ],
            ),
        ],
        Annotations=Annotations(
            Comments={
                "COMMENT001": Comment(
                    CommentId="COMMENT001",
                    Author="Test Author",
                    Date="2023-01-01",
                    Text="https://comment-link.com",
                    Targets=[],
                )
            },
            Hyperlinks={"LINK001": Hyperlink(HyperlinkId="LINK001", Uri="https://example.com/test", Targets=[])},
        ),
        NativeRevisions={},
    )


@pytest.fixture
def empty_data():
    """Empty data fixture for testing"""
    return DocumentInfo(
        Version="1.0",
        DocumentId="EMPTY_DOC",
        Elements=[],
        Annotations=Annotations(Comments={}, Hyperlinks={}),
        NativeRevisions={},
    )


class TestInputTransform:
    """Test cases for input_transform module methods"""

    def test_extract_paragraphs_dicts_basic(self, sample_data):
        """Test basic paragraph extraction functionality"""
        result_xmls, result_order = extract_paragraphs_xml(sample_data)

        # Verify return types
        assert isinstance(result_xmls, list)
        assert isinstance(result_order, list)
        assert len(result_xmls) > 0
        assert len(result_xmls) == 1

        # Verify XML format is correct for first chunk
        try:
            root = etree.fromstring(result_xmls[0])
            assert root.tag == "Xml"
        except etree.XMLSyntaxError:
            pytest.fail("Generated XML is not valid")

    def test_extract_paragraphs_dicts_paragraph_count(self, sample_data):
        """Test correct number of paragraphs extracted"""
        result_xmls, result_order = extract_paragraphs_xml(sample_data)
        root = etree.fromstring(result_xmls[0])  # Get first chunk

        # Should have 2 paragraphs (Table elements are ignored)
        paragraphs = root.findall("Paragraph")
        assert len(paragraphs) == 2

    def test_extract_paragraphs_dicts_paragraph_attributes(self, sample_data):
        """Test paragraph attributes are correctly set"""
        result_xmls, result_order = extract_paragraphs_xml(sample_data)
        root = etree.fromstring(result_xmls[0])  # Get first chunk

        # Check first paragraph
        para1 = root.find(".//Paragraph[@paraId='TEST001']")
        assert para1 is not None
        assert para1.get("Style") == "Heading1"
        assert para1.text == "This is a test paragraph with bold and italic text."

    def test_extract_paragraphs_dicts_empty_data(self, empty_data):
        """Test handling of empty data"""
        result_xmls, result_order = extract_paragraphs_xml(empty_data)

        # Should return empty list
        assert len(result_xmls) == 0
        assert len(result_order) == 0

    def test_extract_paragraphs_dicts_skip_empty_text(self):
        """Test skipping paragraphs with empty text"""
        data_no_text = DocumentInfo(
            Version="1.0",
            DocumentId="EMPTY_DOC",
            Elements=[
                BlockElement(
                    Key="",
                    ElementId="EMPTY001",
                    ElementType="Paragraph",
                    PlainText="",  # Empty text
                    Properties=ElementProperties(Style="Normal"),
                    Segments=[],
                )
            ],
            Annotations=Annotations(Comments={}, Hyperlinks={}),
            NativeRevisions={},
        )

        result_xmls, result_order = extract_paragraphs_xml(data_no_text)

        # Empty text paragraphs should be skipped, resulting in empty list
        assert len(result_xmls) == 0
        assert len(result_order) == 0

    def test_extract_paragraph_data_content(self, sample_data):
        """Test extracting paragraph content using new unified method"""
        xml_result_list, _ = extract_paragraphs_xml(sample_data)
        xml_result = xml_result_list[0]  # Get first chunk

        # Convert DocumentInfo to dict for extract_paragraph_data
        sample_data_dict = sample_data.model_dump()

        # Test existing paragraph
        result = extract_paragraph_data("TEST001", xml_result, sample_data_dict)
        assert result["content"] == "This is a test paragraph with bold and italic text."

        # Test non-existing paragraph
        result = extract_paragraph_data("NONEXISTENT", xml_result, sample_data_dict)
        assert result["content"] == ""
        assert result["links"] == []
        assert result["par_style_text"] == ""
        assert result["run_style_text"] == ""

    def test_extract_paragraph_data_links(self, sample_data):
        """Test extracting paragraph links using new unified method"""
        xml_result_list, _ = extract_paragraphs_xml(sample_data)
        xml_result = xml_result_list[0]  # Get first chunk

        # Convert DocumentInfo to dict for extract_paragraph_data
        sample_data_dict = sample_data.model_dump()

        result = extract_paragraph_data("TEST001", xml_result, sample_data_dict)

        links = result["links"]
        # Should have 2 links: 1 hyperlink + 1 comment URL
        assert len(links) == 2

        # Check hyperlinks
        hyperlinks = [item for item in links if item["Type"] == "Hyperlink"]
        assert len(hyperlinks) == 1
        assert hyperlinks[0]["Uri"] == "https://example.com/test"
        assert hyperlinks[0]["Targets"] == []

        # Check comment URLs
        comment_urls = [item for item in links if item["Type"] == "Comment"]
        assert len(comment_urls) == 1
        assert comment_urls[0]["Uri"] == "https://comment-link.com"
        assert comment_urls[0]["Targets"] == ["COMMENT001"]

    def test_extract_paragraph_data_links_nonexistent(self, sample_data):
        """Test extracting links from non-existing paragraph"""
        # Convert DocumentInfo to dict for extract_paragraph_data
        sample_data_dict = sample_data.model_dump()

        result = extract_paragraph_data("NONEXISTENT", "", sample_data_dict)
        assert result["links"] == []

    def test_extract_paragraph_data_styles(self, sample_data):
        """Test extracting paragraph styles using new unified method"""
        xml_result_list, _ = extract_paragraphs_xml(sample_data)
        xml_result = xml_result_list[0]  # Get first chunk

        # Convert DocumentInfo to dict for extract_paragraph_data
        sample_data_dict = sample_data.model_dump()

        result = extract_paragraph_data("TEST001", xml_result, sample_data_dict)

        # Check paragraph style
        assert result["par_style_text"] == "<pStyle>Heading1</pStyle>"

        # Check run styles structure
        assert isinstance(result["run_style_text"], str)

    def test_extract_paragraph_data_complete(self, sample_data):
        """Test complete paragraph data extraction"""
        xml_result_list, _ = extract_paragraphs_xml(sample_data)
        xml_result = xml_result_list[0]  # Get first chunk

        # Convert DocumentInfo to dict for extract_paragraph_data
        sample_data_dict = sample_data.model_dump()

        result = extract_paragraph_data("TEST001", xml_result, sample_data_dict)

        # Verify all fields are present
        expected_keys = {"content", "links", "par_style_text", "run_style_text"}
        assert set(result.keys()) == expected_keys

        # Verify content
        assert result["content"] == "This is a test paragraph with bold and italic text."

        # Verify links
        assert len(result["links"]) == 2

        # Verify styles
        assert result["par_style_text"] == "<pStyle>Heading1</pStyle>"
        assert isinstance(result["run_style_text"], str)

    def test_convert_json_to_xml(self):
        """Test JSON to XML conversion"""
        from base.process.input_transform import Paragraph

        test_data = [
            Paragraph(
                ParaId="TEST_CONV",
                Text="Test conversion",
                Style=ElementProperties(Style="Heading1"),
                Links=[
                    {
                        "Id": "LINK_CONV",
                        "Uri": "https://test.com",
                        "Targets": ["target1"],
                    }
                ],
                Runs=[{"Text": "Test", "style": {"Bold": True, "Italic": False}}],
            )
        ]

        result = convert_json_to_xml(test_data)
        root = etree.fromstring(result)

        # Verify basic structure
        assert root.tag == "Xml"
        paragraph = root.find(".//Paragraph[@paraId='TEST_CONV']")
        assert paragraph is not None
        assert paragraph.get("Style") == "Heading1"

        # Verify links
        link = root.find(".//Link[@LinkId='LINK_CONV']")
        assert link is not None
        assert link.get("Uri") == "https://test.com"

        # Verify run styles
        run = root.find(".//Run[@Bold='true']")
        assert run is not None
        assert run.text == "Test"


class TestExtractParagraphDataAdvanced:
    """Advanced test cases for the new unified extract_paragraph_data method"""

    def test_extract_paragraph_data_with_run_styles(self):
        """Test paragraph data extraction with complex run styles"""
        # Create test XML with run styles
        xml = """<Xml>
            <Paragraph paraId="STYLE_TEST" Style="Heading1">Test Content<Runs>
                    <Run Bold="true" Italic="false">Bold text</Run>
                    <Run Bold="false" Italic="true">Italic text</Run>
                    <Run Bold="true" Italic="true">Bold and Italic</Run>
                </Runs>
            </Paragraph>
        </Xml>"""

        # Test with empty xml_content to focus on XML parsing
        result = extract_paragraph_data("STYLE_TEST", xml, {})

        # Verify paragraph style
        assert result["par_style_text"] == "<pStyle>Heading1</pStyle>"

        # Verify run styles
        expected_run = ""  # this is empty, because xml_content is empty
        assert result["run_style_text"] == expected_run

        # Verify content
        assert result["content"] == "Test Content"

        # Verify links (should be empty since no xml_content provided)
        assert result["links"] == []

    def test_extract_paragraph_data_normal_style_skip(self):
        """Test that Normal style is skipped in paragraph style extraction"""
        xml = """<Xml>
            <Paragraph paraId="NORMAL_TEST" Style="Normal">Normal paragraph<Runs />
            </Paragraph>
        </Xml>"""

        result = extract_paragraph_data("NORMAL_TEST", xml, {})

        # Normal style should be skipped
        assert result["par_style_text"] == ""
        assert result["content"] == "Normal paragraph"
        assert result["run_style_text"] == ""

    def test_extract_paragraph_data_invalid_xml(self):
        """Test handling of invalid XML"""
        invalid_xml = "<Xml><Paragraph paraId='TEST' unclosed tag"

        result = extract_paragraph_data("TEST", invalid_xml, {})

        # Should return empty values for invalid XML
        assert result["content"] == ""
        assert result["par_style_text"] == ""
        assert result["run_style_text"] == ""
        assert result["links"] == []

    def test_extract_paragraph_data_empty_inputs(self):
        """Test handling of empty inputs"""
        # Test empty para_id
        result = extract_paragraph_data("", "<Xml></Xml>", {})
        expected_empty = {"content": "", "links": [], "par_style_text": "", "run_style_text": ""}
        assert result == expected_empty


class TestInputTransformIntegration:
    """Integration test cases with new unified method"""

    def test_full_workflow_with_unified_method(self):
        """Test complete workflow using new unified extract_paragraph_data method"""
        input_data = DocumentInfo(
            Version="1.0",
            DocumentId="WORKFLOW_DOC",
            Elements=[
                BlockElement(
                    Key="",
                    ElementId="WORKFLOW001",
                    ElementType="Paragraph",
                    PlainText="Test workflow paragraph.",
                    Properties=ElementProperties(Style="Heading2"),
                    Segments=[
                        Segment(
                            SegmentId="WF_SEG001",
                            SegmentText="Test workflow",
                            Start=0,
                            End=12,
                            Properties=SegmentProperties(Bold=True, Italic=False),
                            FieldId="",
                            HyperlinkId="WF_LINK001",
                            ContentRevisionId="",
                            PropRevisions=[],
                            CommentIds=[],
                        ),
                        Segment(
                            SegmentId="WF_SEG002",
                            SegmentText=" paragraph.",
                            Start=13,
                            End=23,
                            Properties=SegmentProperties(Bold=False, Italic=False),
                            FieldId="",
                            HyperlinkId="",
                            ContentRevisionId="",
                            PropRevisions=[],
                            CommentIds=[],
                        ),
                    ],
                )
            ],
            Annotations=Annotations(
                Comments={},
                Hyperlinks={
                    "WF_LINK001": Hyperlink(HyperlinkId="WF_LINK001", Uri="https://workflow.example.com", Targets=[])
                },
            ),
            NativeRevisions={},
        )

        # Step 1: Convert to XML
        xml_result_list, _ = extract_paragraphs_xml(input_data)
        assert isinstance(xml_result_list, list)
        assert len(xml_result_list) > 0
        xml_result = xml_result_list[0]  # Get first chunk

        # Step 2: Extract all paragraph data using new unified method
        input_data_dict = input_data.model_dump()
        para_data = extract_paragraph_data("WORKFLOW001", xml_result, input_data_dict)

        # Verify all data is extracted correctly in one call
        assert para_data["content"] == "Test workflow paragraph."
        assert len(para_data["links"]) == 1
        assert para_data["links"][0]["Uri"] == "https://workflow.example.com"
        assert para_data["par_style_text"] == "<pStyle>Heading2</pStyle>"
        assert isinstance(para_data["run_style_text"], str)

        # Step 3: Verify XML structure
        root = etree.fromstring(xml_result)
        paragraph = root.find(".//Paragraph[@paraId='WORKFLOW001']")
        assert paragraph is not None
        assert paragraph.get("Style") == "Heading2"

    def test_performance_comparison(self):
        """Test performance improvement with unified method"""
        # Large input data for performance testing
        elements = []
        for i in range(10):  # Create 10 test paragraphs
            elements.append(
                BlockElement(
                    Key="",
                    ElementId=f"PERF{i:03d}",
                    ElementType="Paragraph",
                    PlainText=f"Performance test paragraph {i}.",
                    Properties=ElementProperties(Style="Normal"),
                    Segments=[
                        Segment(
                            SegmentId=f"PERF_SEG{i:03d}",
                            SegmentText=f"Performance test paragraph {i}.",
                            Start=0,
                            End=len(f"Performance test paragraph {i}."),
                            Properties=SegmentProperties(Bold=False, Italic=False),
                            FieldId="",
                            HyperlinkId="",
                            ContentRevisionId="",
                            PropRevisions=[],
                            CommentIds=[],
                        )
                    ],
                )
            )

        input_data = DocumentInfo(
            Version="1.0",
            DocumentId="PERF_DOC",
            Elements=elements,
            Annotations=Annotations(Comments={}, Hyperlinks={}),
            NativeRevisions={},
        )

        xml_result_list, _ = extract_paragraphs_xml(input_data)
        xml_result = xml_result_list[0]  # Get first chunk

        # Convert DocumentInfo to dict for extract_paragraph_data
        input_data_dict = input_data.model_dump()

        # Test unified method performance
        start_time = time.time()
        for i in range(10):
            para_data = extract_paragraph_data(f"PERF{i:03d}", xml_result, input_data_dict)
            # Verify data is extracted correctly
            assert isinstance(para_data["content"], str)
            assert isinstance(para_data["links"], list)
            assert isinstance(para_data["par_style_text"], str)
            assert isinstance(para_data["run_style_text"], str)

        unified_time = time.time() - start_time

        # The unified method should complete successfully
        # (Performance comparison would need the old separate methods to be meaningful)
        assert unified_time < 1.0  # Should complete within 1 second
