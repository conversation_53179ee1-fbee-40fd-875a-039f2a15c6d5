# tests/eval/test_tool_examples_live.py
import json
from pathlib import Path
import pytest

from base.example.core import Too<PERSON><PERSON><PERSON><PERSON>
from test.utils import _collect_all_evaluations

RESULT_DIR = Path(__file__).parent / "artifacts"
RESULT_DIR.mkdir(exist_ok=True)

@pytest.mark.evaluation
@pytest.mark.parametrize(("tool_cls", "example"), _collect_all_evaluations())
def test_examples_evaluation(tool_cls, example: ToolExample, request):
    outcome = example.run_ok(tool_cls)

    # out_file = RESULT_DIR / f"{request.node.name}.json"
    # out_file.write_text(json.dumps(outcome, ensure_ascii=False, indent=2), "utf-8")

    # if "score" in outcome:
    #     assert outcome["score"] >= 0.5, "LLM quality below threshold"
