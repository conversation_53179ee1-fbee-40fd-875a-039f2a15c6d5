import os
import json
from pathlib import Path
from unittest.mock import patch

import pytest

def pytest_addoption(parser):
    parser.addoption(
        "--evaluation",
        action="store_true",
        help="Run evaluation tests that hit real LLM endpoints",
    )

def pytest_configure(config):
    config.addinivalue_line(
        "markers",
        "evaluation: mark tests that run real LLM calls for evaluation (skipped unless --evaluation)",
    )

def pytest_collection_modifyitems(config, items):
    if config.getoption("--evaluation"):
        return
    skip_eval = pytest.mark.skip(reason="need --evaluation to run")
    for item in items:
        if "evaluation" in item.keywords:
            item.add_marker(skip_eval)


def load_mock_json_data(filename):
    with open(Path(os.path.realpath(__file__)).parent / "file" /  filename, "r", errors="ignore") as f:
        _mock_data = json.load(f)
    return _mock_data


@pytest.fixture(scope="module")
def revision_test_case():
    return load_mock_json_data("revision_test.json")


@pytest.fixture
def mock_boto3_client():
    with patch("boto3.client") as mock_client:
        yield mock_client