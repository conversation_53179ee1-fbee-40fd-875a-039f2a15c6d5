from __future__ import annotations

import importlib
import inspect
import pkgutil
import re
from pathlib import Path

import pytest
import yaml
from base import tool as tool_pkg
from base.example.core import ToolExample
from base.tool.base import LLMTool


def camel_case_split(identifier: str):
    return re.findall(r"[A-Z](?:[a-z]+|[A-Z]*(?=[A-Z]|$))", identifier)


# ---------- 数据收集 ----------


def _iter_tool_classes():
    """Yield every concrete LLMTool subclass that defines .example."""
    for _pkg, module_name, _is_pkg in pkgutil.iter_modules(tool_pkg.__path__):
        mod = importlib.import_module(f"{tool_pkg.__name__}.{module_name}")
        for _name, cls in inspect.getmembers(mod, inspect.isclass):
            if issubclass(cls, LLMTool) and cls is not LLMTool and hasattr(cls, "example"):
                yield cls


def find_yaml_path(tool_cls):
    base = Path(__file__).parent / "cases"
    name = tool_cls.__name__

    parts = camel_case_split(name)
    snake = "_".join(part.lower() for part in parts) if parts else ""
    first = parts[0].lower() if parts else ""

    # 构造所有候选路径
    candidates = [
        base / f"{name}.yml",
        base / f"{name}.yaml",
        base / f"{name.lower()}.yaml",
        base / f"{name.lower()}.yml",
        base / f"{snake}.yaml",
        base / f"{snake}.yml",
        base / f"{first}.yaml",
        base / f"{first}.yml",
    ]

    existing = [p for p in candidates if p.exists()]

    if not existing:
        return None
    if len(existing) > 1:
        raise RuntimeError(f"找到多个 YAML 文件匹配 {tool_cls.__name__}：{[p.name for p in existing]}")
    # 恰好一个
    return existing[0]


def _iter_yaml_cases(tool_cls):
    """
    If tests/cases/ToolName.yml exists, yield ToolExample instances
    built from each YAML case.
    """
    yaml_path = find_yaml_path(tool_cls)
    if not yaml_path:
        return  # no cases file found

    raw_cases = yaml.safe_load(yaml_path.read_text(encoding="utf-8"))
    for case in raw_cases:
        data = {
            "para_id":      case["para_id"],
            "para_content": case["para_content"],
            "abbr":        case.get("abbr", tool_cls.__name__=="AbbrTool"),
            "leg_ac":      case.get("leg_ac", tool_cls.__name__=="LegislationCheckTool"),
            "quotation":   case.get("quotation", tool_cls.__name__=="QuotationTool"),
            "link":        case.get("link", tool_cls.__name__=="CheckLinkTool"),            
        }
        yield ToolExample(
            template=tool_cls.example.template,
            input_data=data,
            expected=case["expected"],
        )


def _collect_all_examples():
    """Return a list[(tool_cls, example, case_id)] suitable for parametrize."""
    params = []
    for tool_cls in _iter_tool_classes():
        # ① 默认示例
        if tool_cls.example.mock_predict:
            params.append(
                pytest.param(
                    tool_cls,
                    tool_cls.example,
                    id=f"{tool_cls.__name__}::default",
                )
            )
        # ② YAML-驱动示例
        if tool_cls.example.mock_predict:
            for idx, example in enumerate(_iter_yaml_cases(tool_cls), start=1):
                params.append(
                    pytest.param(
                        tool_cls,
                        example,
                        id=f"{tool_cls.__name__}::yaml[{idx}]",
                    )
                )
    return params


def _collect_all_evaluations():
    """Return a list[(tool_cls, example, case_id)] suitable for parametrize."""
    params = []
    for tool_cls in _iter_tool_classes():
        # ① 默认示例
        params.append(
            pytest.param(
                tool_cls,
                tool_cls.example,
                id=f"{tool_cls.__name__}::default",
            )
        )
        # ② YAML-驱动示例
        for idx, example in enumerate(_iter_yaml_cases(tool_cls), start=1):
            params.append(
                pytest.param(
                    tool_cls,
                    example,
                    id=f"{tool_cls.__name__}::yaml[{idx}]",
                )
            )
    return params
