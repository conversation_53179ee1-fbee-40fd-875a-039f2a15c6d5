openapi: 3.0.1
info:
  title: PG Styling Agent
  version: "1.0"
servers:
  - url: https://shyastptdykou4rdo5dbkq76340olwqu.lambda-url.us-east-2.on.aws/
    description: Production
paths:
  /:
    post:
      summary: Upload a file
      parameters:
        - name: mode
          in: query
          description: The mode of processing.
          required: true
          schema:
            type: string
            enum: ["full", "revision"]
            default: "full"
            example: "full"
      requestBody:
        required: true
        content:
          multipart/form-data:
            schema:
              type: object
              properties:
                file:
                  type: string
                  format: binary
      responses:
        "200":
          description: The download link will be returned in the response.
          content:
            application/json:
              schema:
                type: object
                properties:
                  downloadUrl:
                    type: string
                    format: uri
                    description: The download link will be returned in the response.
  /v2:
    post:
      summary: The processed document will be sent to your email shortly.
      tags:
        - V2
      parameters:
        - name: email
          in: query
          description: The email address.
          required: true
          schema:
            type: string
            format: email
        - name: mode
          in: query
          description: The mode of processing.
          required: true
          schema:
            type: string
            enum: ["full", "revision"]
            default: "full"
            example: "full"
      requestBody:
        required: true
        content:
          multipart/form-data:
            schema:
              type: object
              properties:
                file:
                  type: string
                  format: binary
      responses:
        "200":
          description: The processed document will be sent to your email shortly.
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
                    description: The processed document will be sent to your email shortly.
                    example: The processed document will be sent to your email shortly.
