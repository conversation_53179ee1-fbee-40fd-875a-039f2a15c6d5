import requests


class CaaSRepoClient:

    def query_meta(self, url: str, body: dict) -> dict:
        """Query the CaaS Repo server with the given parameters."""
        try:
            response = requests.post(url, json=body)
            response.raise_for_status()
            return response.json()
        except requests.exceptions.RequestException as err:
            raise Exception(f"Request error: {err}")
        except Exception as err:
            raise Exception(f"Unknown Error: {err}")

caas_repo_client = CaaSRepoClient()
__all__ = ["caas_repo_client"]
