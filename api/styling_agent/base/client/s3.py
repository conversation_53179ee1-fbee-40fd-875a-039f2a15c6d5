import boto3
from loguru import logger

from config_manager.manager import AWS_REGION_NAME


class S3Client:
    def __init__(self, region_name: str = None):
        region_name = region_name or AWS_REGION_NAME
        self._s3 = boto3.client("s3", region_name=region_name)

    def put_object(self, bucket_name: str, file_name: str, file_bytes: bytes) -> bool:
        try:
            self._s3.put_object(
                Bucket=bucket_name,
                Key=file_name,
                Body=file_bytes,
            )
            logger.info(f"Upload file: {file_name} success.")
            return True
        except Exception as e:
            logger.error(f"Upload file failed: {e}")
        return False

    def generate_presigned_url(self, bucket: str, key: str, expires_in: int = 3600, action: str = "get_object") -> str:
        try:
            presigned_url = self._s3.generate_presigned_url(
                action,
                Params={"Bucket": bucket, "Key": key},
                ExpiresIn=expires_in,
            )
            return presigned_url
        except Exception as e:
            logger.error(f"Error generating presigned URL: {e}")
            return ""

    def download_object(self, bucket: str, key: str, target_path: str) -> bool:
        try:
            self._s3.download_file(bucket, key, target_path)
            return True
        except Exception as e:
            logger.error(f"Error downloading object: {e}")
        return False 

    def get_object(self, bucket: str, key: str) -> bytes:
        try:
            response = self._s3.get_object(Bucket=bucket, Key=key)
            return response["Body"].read()
        except Exception as e:
            logger.error(f"Error getting object: {e}")
        return b""

    def head_object(self, bucket: str, key: str) -> bool:
        try:
            self._s3.head_object(Bucket=bucket, Key=key)
            return True
        except self._s3.exceptions.ClientError as e:
            logger.error(f"Object does not exist: {e}")
        return False

s3_client = S3Client()
__all__ = ["s3_client"]
