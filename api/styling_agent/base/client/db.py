import pymysql
from loguru import logger

from base.config.settings import config
from base.error.client import DatabaseError


class DBClient:
    def __init__(self, host, database, user, password, port):
        self.host = host
        self.database = database
        self.user = user
        self.password = password
        self.port = port
        self.conn = None

    def connect(self):
        """Establish a connection to the MySQL database."""
        try:
            self.conn = pymysql.connect(
                host=self.host,
                database=self.database,
                user=self.user,
                password=self.password,
                port=self.port,
            )
        except pymysql.MySQLError as e:
            logger.error(f"Error connecting to MySQL: {e}")
            raise DatabaseError(f"Error connecting to MySQL: {e}")

    def close(self):
        if self.conn:
            self.conn.close()
            self.conn = None
            logger.debug("Database connection closed")

    def query(self, query, params):
        """Execute a query and return the results"""
        logger.debug(f"DB Executing query: {query} with params: {params}")
        self.connect()
        try:
            with self.conn.cursor() as cursor:
                cursor.execute(query, params)
                return cursor.fetchall()
        except pymysql.MySQLError as e:
            logger.error(f"Error executing query: {e}")
            raise DatabaseError(f"Error executing query: {e}")
        finally:
            self.close()


db_client = DBClient(
    host=config.DB_HOST,
    database=config.DB_DATABASE,
    user=config.DB_USER,
    password=config.DB_PASSWORD,
    port=config.DB_PORT,
)
