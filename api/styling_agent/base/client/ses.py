import re
import traceback
import unicodedata
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
from email.mime.application import MIMEApplication
from typing import List, Optional, Dict, Any

import boto3
from loguru import logger

from base.config.settings import config


class SESClient:
    def __init__(
        self, 
        sender: str,
    ):
        self._sender = sender
        self._client = boto3.client('ses', region_name=config.SES_REGION_NAME)

    def send_email(
        self, 
        recipients: List[str], 
        subject: str, 
        content: str, 
        html: bool = False,
        attachments: Optional[dict[str, bytes]] = None,
    ) -> Dict[str, Any]:
        try:
            msg = MIMEMultipart()
            msg['From'] = self._sender
            msg['To'] = ', '.join(recipients)
            msg['Subject'] = subject

            body = MIMEMultipart('alternative')

            # add body
            content_type = 'html' if html else 'plain'
            body.attach(MIMEText(content, content_type, 'utf-8'))
            msg.attach(body)

            if attachments:
                for filename, content in attachments.items():
                    if not content or len(content) == 0:
                        logger.error(f"Attachment content is empty: {filename}")
                        continue

                    try:
                        cleaned_filename = self._get_attachment_name(filename)
                        part = MIMEApplication(content, Name=cleaned_filename)
                        part['Content-Disposition'] = f'attachment; filename="{cleaned_filename}"'
                        msg.attach(part)
                    except Exception:
                        tb = traceback.format_exc(-1)
                        logger.error(f"Add attachment: {filename}, ex: {tb}")

            message_params = {
                'Source': self._sender,
                'Destinations': recipients,
                'RawMessage': {
                    'Data': msg.as_string()
                }
            }

            response = self._client.send_raw_email(
                **message_params
            )
            logger.info(f"Email sent successfully: {response['MessageId']}")
            return True
        except Exception:
            tb = traceback.format_exc(-1)
            logger.error(f"Send email failed: {tb}")
        return False

    def _get_suffix(self, filename: str) -> str:
        suffix = "." + filename.rsplit('.')[-1] if '.' in filename else ''
        return suffix

    def _get_attachment_name(self, name: str, max_length=255, replace_with=" ", allowed_chars='-_.() ') -> str:
        file_suffix = self._get_suffix(name)
        normalized = unicodedata.normalize('NFKD', name)
        ascii_only = normalized.encode('ascii', 'ignore').decode()
        pattern = f'[^a-zA-Z0-9{re.escape(allowed_chars)}]'
        # replace non-ASCII characters and unwanted characters
        cleaned = re.sub(pattern, " ", ascii_only)
        cleaned = cleaned.strip(f'{replace_with} ')
        # limit length
        if len(cleaned) > max_length:
            cleaned = cleaned[:max_length - len(file_suffix) - 1] + file_suffix
        # make sure it is not empty
        if not cleaned or file_suffix == cleaned:
            cleaned = 'unnamed_file' + file_suffix
        return cleaned

ses_client = SESClient(config.SES_SENDER)

__all__ = ["ses_client"]
