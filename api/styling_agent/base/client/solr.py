import requests
from loguru import logger

from base.config.settings import config
from base.error.client import SolrClientError

class SolrClient:
    def __init__(self, url):
        self.url = url

    def _build_url(self, endpoint: str) -> str:
        """Build the full URL for the Solr endpoint."""
        return f"{self.url}/{endpoint}"

    def query(self, params: dict) -> dict:
        """Query the Solr server with the given parameters."""
        try:
            logger.debug(f"Querying Solr with params: {params}")
            response = requests.get(self._build_url("select"), params=params, timeout=config.SOLR_TIMEOUT)
            response.raise_for_status()
            return response.json()
        except requests.exceptions.RequestException as err:
            logger.error(f"Request error: {err}")
            raise SolrClientError(f"Request error: {err}")
        except Exception as err:
            logger.error(f"Unknown Error: {err}")
            raise SolrClientError(f"Unknown Error: {err}")


solr_client = SolrClient(url=f"{config.SOLR_URL}/{config.SOLR_CORE}")

