import json
import time

import boto3
from botocore.config import Config
from loguru import logger

from config_manager.manager import AWS_REGION_NAME
from base.config.settings import config


class LambdaClient:
    def __init__(self, region_name: str = None):
        region_name = region_name or AWS_REGION_NAME
        self._lambda_client = boto3.client(
            "lambda",
            region_name=region_name,
            config=Config(
                retries={"max_attempts": config.LAMBDA_MAX_RETRIES},
                read_timeout=config.READ_TIMEOUT,
                connect_timeout=config.CONNECT_TIMEOUT,
                tcp_keepalive=True,
            ),
        )

    def invoke(self, function_name: str, payload: str, invocation_type: str = "RequestResponse"):
        try:
            t0 = time.time()
            response = self._lambda_client.invoke(
                FunctionName=function_name,
                InvocationType=invocation_type,
                Payload=payload
            )
            if invocation_type == "Event":
                return response

            logger.debug(f"[invoke returned in {time.time()-t0:.1f}s]")

            body_bytes = response["Payload"].read()
            logger.debug(f"[read {len(body_bytes)} bytes "
                        f"in {time.time()-t0:.1f}s]")

            body = json.loads(body_bytes)
            logger.debug(f"[parsed body] {body}")
            return body
        except json.JSONDecodeError as e:
            logger.error(f"Error decoding JSON response: {e}")
        except Exception as e:
            logger.error(f"Error invoking Lambda function: {e}")
        return None

lambda_client = LambdaClient()
__all__ = ["lambda_client"]
