import importlib.util
import os
import time
import traceback

from base.client.s3 import s3_client
from base.client.ses import ses_client
from base.error import AgentError
from base.error.apply_revision import (
    ApplyRevisionInternalError,
    ApplyRevisonResponseError,
)
from base.error.convert import ConvertWithEmptyReturnError
from base.error.executor import ExecutorInnerError, ExecutorPayloadError
from base.schema.executor import ExecutorRequest, ExecutorResponse
from base.context_manager import get_context
from base.util.email_template import EMAIL_ERROR, EMAIL_SUCCESS
from base.util.lambda_util import get_package_version, get_response
from common.util.logger import register_logger
from common.util.request_id_gen import request_id_context
from loguru import logger

register_logger()


def get_error_respnse(e: AgentError):
    response = ExecutorResponse[None](code=e.code, message=e.message)
    return get_response(e.status_code, response.to_dict())


def try_to_get_email(event):
    try:
        return event.get("email")
    except KeyError:
        return None


def get_document_attachment(bucket: str, key: str, **kwargs):
    """Get AI document response."""
    content = s3_client.get_object(bucket, key)
    filename = key.rsplit("/")[-1]
    return {filename: content}


def lambda_handler(event, context):
    start_time = time.monotonic()
    try_to_set_request_id(event, context)
    email = try_to_get_email(event)
    request_payload = try_to_get_request_payload(event)

    email_template = EMAIL_ERROR
    attachment_data: dict[str, bytes] = {}
    response = get_error_respnse(ExecutorInnerError())
    if not request_payload:
        logger.error(f"Invalid request payload: {request_payload}")
        email_template["content"].format(error_message="Invalid request payload")
        response = get_error_respnse(ExecutorPayloadError())
    else:
        upload_filenmae = request_payload_parse(request_payload)
        try:
            handler_result = executor_handler(request_payload)
            email_template = EMAIL_SUCCESS
            response = get_response(200, handler_result.to_dict())
            attachment_data = get_document_attachment(**handler_result.data)
        except AgentError as e:
            error_msg = str(e)
            email_template["content"] = email_template["content"].format(
                error_message=gen_error_message(error_msg, upload_filenmae)
            )
            logger.error(f"Executor error: {error_msg}")
            response = get_error_respnse(e)
        except Exception as e:
            tb = traceback.format_exc(-1)
            logger.error(f"Executor handler unexpected error: {tb}")
            email_template["content"] = email_template["content"].format(
                error_message=gen_error_message(ExecutorInnerError.message, upload_filenmae)
            )

    if email:
        ses_client.send_email(recipients=[email], attachments=attachment_data, **email_template)

    end_time = time.monotonic()
    logger.info(f"Executor execution total time: {end_time - start_time:.2f} seconds")
    return response


def executor_handler(request_payload: ExecutorRequest):
    get_context()
    os.environ["JOB_PREFIX"] = request_payload.base
    logger.info(f"Agent package version: {get_package_version()}")
    logger.info(f"Executor request payload: {request_payload.to_dict()}")

    # Download agent script
    script_path = "/tmp/agent_script.py"
    download_status = s3_client.download_object(request_payload.script_bucket, request_payload.script_key, script_path)
    if not download_status:
        raise Exception("Failed to download script from S3.")

    logger.info(f"Executing script")
    spec = importlib.util.spec_from_file_location("generated", script_path)
    if spec is None or spec.loader is None:
        raise ImportError(f"Cannot load spec for {script_path}")
    mod = importlib.util.module_from_spec(spec)
    spec.loader.exec_module(mod)  # == runpy.run_path

    if not hasattr(mod, "main"):
        raise AttributeError("Generated script error: missing main fuction.")

    script_response = mod.main()
    if "updated_s3_key" in script_response:
        # check if the word update tool returns empty string
        response_obj = script_response["updated_s3_key"]
        if not isinstance(response_obj, dict):
            raise ConvertWithEmptyReturnError()
        data_obj = response_obj.get("Data", {})
        if not data_obj or not isinstance(data_obj, dict) or "Key" not in data_obj:
            raise ExecutorInnerError()
        key = data_obj["Key"]
        if key.endswith("original.docx"):
            raise ExecutorInnerError()
        # check filename
        updated_s3_key = script_response["updated_s3_key"]
    else:
        updated_s3_key = script_response

    status_code = updated_s3_key["Code"]
    if is_success_status_code(status_code):
        bucket = updated_s3_key["Data"]["Bucket"]
        key = updated_s3_key["Data"]["Key"]
        if key.endswith("original.docx"):
            raise ExecutorInnerError()
        if bucket and key:
            presigned_url = s3_client.generate_presigned_url(bucket, key=key)
            presigned_s3_info = {
                "bucket": bucket,
                "key": key,
                "presigned_url": presigned_url,
            }
            executor_response = ExecutorResponse[dict](data=presigned_s3_info)
            return executor_response
        raise ApplyRevisonResponseError()
    raise ApplyRevisionInternalError()


def is_success_status_code(status_code: int) -> bool:
    """Check if the status code indicates success."""
    return 200 <= status_code < 300


def try_to_set_request_id(event, context):
    try:
        request_id = event["request_id"]
    except KeyError:
        request_id = context.aws_request_id if hasattr(context, "aws_request_id") else "local-run"
    request_id_context.set(request_id)


def try_to_get_request_payload(event):
    try:
        return ExecutorRequest(
            script_bucket=event["script_bucket"],
            script_key=event["script_key"],
            base=event["base"],
        )
    except KeyError as e:
        logger.error(f"Missing key in event: {e}")
    except TypeError as e:
        logger.error(f"Invalid type in event: {e}")
    return None


def request_payload_parse(request_payload: ExecutorRequest) -> str:
    """Parse the request payload to get the upload filename."""
    if not request_payload or not isinstance(request_payload, ExecutorRequest):
        return ""
    script_key = request_payload.script_key
    if not script_key:
        return ""
    filename = script_key.split("/")[2]
    return filename


def gen_error_message(error_msg: str, filename: str) -> str:
    """Generate a formatted error message."""
    if filename != "":
        return f"{error_msg} when processing {filename}"
    return error_msg
