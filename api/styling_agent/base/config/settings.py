from loguru import logger
from pydantic import BaseModel, ConfigDict, <PERSON>, <PERSON>son

from config_manager.manager import (
    ENV,
    SECRET_NAME,
    BaseConfig,
    Env,
)


class _AppSetting(BaseModel):
    PROJECT_NAME: str = "Styling Agent"
    PROJECT_VERSION: str = "0.0.1"
    DESCRIPTION: str = "Styling Agent"


class _LLMSetting(BaseModel):
    LLM_MODEL: str = "" # env required, DEFAULT model, if tool/agent model not found in LLM_TOOL_MODEL_MAP, use default model
    LLM_MODEL_TEMPERATURE: float = 0.0
    LLM_MODEL_MAX_TOKENS: int = 4096
    LLM_MODEL_TOP_P: float = 1.0
    LLM_TENANT: str = Field(secret_manager=True, secret_name=SECRET_NAME, default="")
    ASSET_ID: str = ""
    LLM_MODEL_TIMEOUT: int = 360
    LLM_STREAM: bool = False


# current support model
# OpenAI_gpt-4o-2024-11-20_ContentPlatform_US_5633_nonprod
# OpenAI_gpt-4o-mini_ContentPlatform_US_5633_nonprod
# OpenAI_o1-mini_ContentPlatform_US_5633_nonprod
# OpenAI_o1_ContentPlatform_US_5633_nonprod
# OpenAI_o3-mini_ContentPlatform_US_5633_nonprod
class _ModelSetting(BaseModel):
    AGENT_MODEL: str = "" # env required
    TOOL_MODEL_MAP: Json = Field(default_factory=dict) # env required


class _SolrSetting(BaseModel):
    SOLR_URL: str = "" # env required
    SOLR_CORE: str = "" # env required
    SOLR_TIMEOUT: int = 30
    SOLR_RETRIEVAL_NUM: int = 100

class _DBSetting(BaseModel):
    DB_HOST: str = "" # env required
    DB_PORT: int = 3306
    DB_DATABASE: str = "" # env  required
    DB_USER: str = "" # env required
    DB_PASSWORD: str = Field(secret_manager=True, secret_name=SECRET_NAME, default="")


class _S3Setting(BaseModel):
    S3_BUCKET: str = f"5633-pg-{ENV.env}-styling-agent"


class _LambdaSetting(BaseModel):
    EXECUTOR_NAME: str           = f"5633-pg-{ENV.env}-executor"
    WORD_CONVERTER_NAME: str     = f"5633-pg-{ENV.env}-wordtool-ConvertHandler"
    WORD_APPLY_NAME: str         = f"5633-pg-{ENV.env}-wordtool-ApplyRevisionsHandler"
    LAMBDA_MAX_RETRIES: int = 3
    READ_TIMEOUT: int = 900
    CONNECT_TIMEOUT: int = 900


class _SESSetting(BaseModel):
    SES_SENDER: str = "<EMAIL>"
    SES_REGION_NAME: str = "us-east-1"


class _CaasRepoSetting(BaseModel):
    CAAS_REPO_URL: str = ""  # env required
    META_ENDPOINT: str = "" # env required


class Config(
    BaseConfig, _AppSetting, _CaasRepoSetting, _DBSetting, _LLMSetting, _SolrSetting, _ModelSetting, _S3Setting, _SESSetting, _LambdaSetting
):
    """
    This is the config for the content manager
    """

    ENV: Env = ENV
    SECRET_NAME: str = SECRET_NAME

    model_config = ConfigDict(arbitrary_types_allowed=True)


try:
    import base.config.settings_local as settings_local

    logger.info("local config find, use local config")
    config = Config.load(**{k: v for k, v in settings_local.__dict__.items() if not k.startswith("__")})


except ImportError:
    logger.warning("local config not found, use default config")
    config = Config.load()
