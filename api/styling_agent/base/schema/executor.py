from dataclasses import dataclass, asdict
from typing import TypeVar, Generic

T = TypeVar("T")

@dataclass
class ExecutorRequest:
    script_bucket: str
    script_key: str
    base: str
    request_id: str | None = None
    email: str | None = None

    def to_dict(self):
        return asdict(self)


@dataclass
class ExecutorResponse(Generic[T]):
    code: str = "0000"
    message: str = "success"
    data: T | None = None

    def to_dict(self):
        return asdict(self)
