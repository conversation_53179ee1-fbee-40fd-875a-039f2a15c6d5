import boto3
import base64


def lambda_handler(event, context):
    s3 = boto3.client("s3")
    bucket = "your-bucket-name"
    # 兼容 API Gateway 的多种触发方式
    key = event.get("queryStringParameters", {}).get("key") or event.get(
        "pathParameters", {}
    ).get("key")
    if not key:
        return {"statusCode": 400, "body": "Missing key"}
    obj = s3.get_object(Bucket=bucket, Key=key)
    content = obj["Body"].read()
    return {
        "statusCode": 200,
        "headers": {"Content-Type": obj["ContentType"]},
        "body": base64.b64encode(content).decode(),
        "isBase64Encoded": True,
    }
