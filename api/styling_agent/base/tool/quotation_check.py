import re

from base.example.quotation_check import QUOTATION_EXAMPLE
from base.model import ParaState
from base.prompt.tooling_prompt import prompt_quote_check
from base.util.convert import convert_yaml_to_json_if_possible

from .base import LLMTool


class QuotationCheckTool(LLMTool):
    """Context is: # Integrate - base.tool\n
    QuotationCheckTool has integrated base.tool for identify, check and modify quotations. There is one aspect of QuotationCheckTool understand xml with user's document:\n
    - Use QuotationCheckTool run for execute.\n

    Requires(context):
        These parameters are obtained from `self.context_manager` and do not need to be passed explicitly:
        - para_state (ParaState): The paragraph state of the document, retrieved from `self.context_manager`.
          - para_content (str): The content of the paragraph.

    Provides(context):
        - para_state (ParaState): The updated paragraph state after running the styling tool.
          - modified_para_content (str): The modified content of the paragraph.

    Args:
        The following parameters must be explicitly passed.
        a dictionary with the following keys:
        - para_id (str): The ID of the paragraph.

    Returns:
      a dictionary with the following keys:
        - para_id (str): The ID of the paragraph.
        - modified_para_content (str): The content of the paragraph updated with the quotation check.
    """

    example = QUOTATION_EXAMPLE
    provides = {"para_state"}

    def run(self) -> ParaState | None:
        para_state = self.context_manager.get_para_state(self.parameters["para_id"])
        if not para_state:
            self.logger.error(f"{self.name}: No paragraph state found for para_id {self.parameters['para_id']}")
            return None
        try:
            result_content = self.llm_predict(para_state.para_content)
        except Exception as e:
            self.logger.error(f"{self.name}: error in quotation check: {e}")
            return para_state
        pattern = r"<modified_text>(.*?)</modified_text>"
        matched_result = re.findall(pattern, result_content, re.DOTALL)
        if not matched_result:
            self.logger.debug(f"{self.name}: error no modified patterns found")
            return para_state
        para_state.modified_para_content = matched_result[0].strip()
        return para_state

    def prompt_template(self, text: str):
        return prompt_quote_check.format(text)

    def post_process(self, text: str):
        return convert_yaml_to_json_if_possible(text)
