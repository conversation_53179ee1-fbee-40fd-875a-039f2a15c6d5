import re
import uuid
from collections import deque
from dataclasses import dataclass, field
from enum import Enum
from typing import Any, Dict, List

from .base import Tool


class ListValidationError(Enum):
    """列表项验证错误类型"""

    SUCCESS = 0
    EMPTY_ITEM = 1  # 空列表项
    LEVEL_TOO_DEEP = 2  # 列表层级过深
    WRONG_LABEL = 3  # 标签格式错误
    UPPERCASE_START = 4  # 不完整句子首字母大写
    LOWERCASE_REQUIRED = 5  # 需要首字母小写（分号列表格式）


@dataclass
class ListItem:
    """表示一个列表项，包含原始元素和层级信息"""

    element: Dict[str, Any]
    indent: int
    level: int = 0
    children: List["ListItem"] = field(default_factory=list)

    def add_child(self, child: "ListItem"):
        """添加子列表项"""
        self.children.append(child)

    def to_dict(self) -> Dict[str, Any]:
        """将 ListItem 对象转换为字典，并更新 level。"""
        properties = self.element.setdefault("Properties", {})
        list_info = properties.setdefault("ListInfo", {})
        list_info["Level"] = self.level
        return self.element

    def to_flat_list(self) -> List[Dict[str, Any]]:
        """将树形结构转换为平铺列表，保持树形顺序"""
        result = [self.to_dict()]
        for child in self.children:
            result.extend(child.to_flat_list())
        return result


@dataclass
class ListBlock:
    """表示一个连续的列表块"""

    list_items: List[ListItem] = field(default_factory=list)

    def add(self, item: ListItem):
        self.list_items.append(item)


class CheckListTool(Tool):
    """Validates the list structure of an entire document based on a set of rules.

    IMPORTANT: This tool is designed to be called once per document to check all lists for formatting consistency,
     so do not place it inside functions that process individual paragraphs.

    Requires(context):
        These parameters are obtained from `self.context_manager` and do not need to be passed explicitly:
        - xml_content (str): The XML content of the document, retrieved from `self.context_manager`.

    Provides(context):
        - para_state (ParaState): The updated paragraph state after running the styling tool.
          - patchs (list): The tool will append the patch operations to the patches attribute of the para_state.

    Returns:
        A list of results for each paragraph that was checked.
        Each result includes:
            - para_id (str): The ID of the paragraph.
            - modified_para_content (str): The content after applying the check.
            - commented_para_content (str): The content with comments added, if applicable.
            - run_style_text (str): The styled text for the paragraph.
            - patches (list): A list of patch operations, if any.
        Note: The returned checklist_result should be merged into the all_para_result list that is passed to the PatchTool.
    """

    example = """```python
        from base.tool import CheckListTool

        # Run the CheckListTool
        list_check_tool = CheckListTool(xml_content=xml_content)
        checklist_result = list_check_tool.run()
        checklist_result": [
            {{
                "para_id": "para_id_1",
                "modified_para_content": "Updated content for paragraph 1",
                "commented_para_content": "Updated paragraph 1 content",
                "run_style_text": "Updated <b>paragraph 1</b> content",
                "patches": []  # Optional, list of patch operations
            }},
        ]

        # Merge checklist results into all_para_result
        all_para_result.extend(checklist_result)
        ```"""

    def run(self):
        """运行列表检查工具，构建 ListBlock 的层级结构"""
        xml_content = self.context_manager.xml_content
        elements = xml_content.get("Elements", [])

        list_blocks = build_list_blocks(elements)

        para_result = []

        for list_block in list_blocks:
            if not list_block.list_items:
                continue

            # 新增：检查同级列表项的格式一致性
            self._check_sibling_consistency(list_block)

            queue = deque(list_block.list_items)  # 初始化队列

            while queue:
                list_item = queue.popleft()  # 出队

                # 检查列表项是否符合预定义的规则
                validation_result = self.check_list_item_rules(list_item)

                if validation_result == ListValidationError.UPPERCASE_START:
                    lowercase_fix = self._format_lowercase_start(list_item)
                    comment_add = self._make_comment(list_item, "AI Suggestion: List formatting update.")
                    lowercase_fix["patches"].extend(comment_add["patches"])
                    para_result.append(lowercase_fix)
                elif validation_result == ListValidationError.LOWERCASE_REQUIRED:
                    lowercase_fix = self._format_lowercase_start(list_item)
                    comment_add = self._make_comment(list_item, "AI Suggestion: List formatting update.")
                    lowercase_fix["patches"].extend(comment_add["patches"])
                    para_result.append(lowercase_fix)
                elif validation_result in [
                    ListValidationError.EMPTY_ITEM,
                    ListValidationError.LEVEL_TOO_DEEP,
                    ListValidationError.WRONG_LABEL,
                ]:
                    # 处理其他错误类型：EMPTY_ITEM/LEVEL_TOO_DEEP/WRONG_LABEL
                    segments = list_item.element.get("Segments", [])
                    if segments:
                        result = self._make_comment(list_item, "AI Suggestion: List formatting update.")
                        para_result.append(result)
                    else:
                        self.logger.warning(
                            f"No segments found for list item with ElementId: {list_item.element.get('ElementId')}"
                        )

                # 将子节点入队
                for child in list_item.children:
                    queue.append(child)

        for r in para_result:
            para_id = r["para_id"]
            para_state = self.context_manager.get_para_state(para_id)
            if not para_state:
                self.logger.error(f"{self.name}: No paragraph state found for para_id {para_id}.")
                continue
            para_state.patches.extend(r["patches"])

        return para_result

    def _check_sibling_consistency(self, list_block: ListBlock):
        """检查同级列表项的格式一致性

        新增功能：检测第一个列表项以分号结尾时，强制同级其他列表项首字母小写
        """
        if not list_block.list_items:
            return

        # 使用字典按层级和父节点分组存储同级节点
        level_groups = {}

        def collect_siblings(item: ListItem, parent_key: str = "root"):
            """递归收集同级节点"""
            key = f"{parent_key}_level_{item.level}"
            if key not in level_groups:
                level_groups[key] = []
            level_groups[key].append(item)

            # 处理子节点
            for child in item.children:
                collect_siblings(child, f"{parent_key}_child_{id(item)}")

        # 收集所有同级节点
        for root_item in list_block.list_items:
            collect_siblings(root_item)

        # has_semicolon = any(";" in item.element.get("PlainText", "").strip() for item in list_block.list_items)

        # 检查每个同级组
        for siblings in level_groups.values():
            if len(siblings) <= 1:
                continue
            # 检查第一个列表项是否以分号结尾
            first_item = siblings[0]
            content = first_item.element.get("PlainText", "").strip()
            # FIXME: 这里检查到第一个句子不存在 ';' 就返回了，那如果后面的句子有呢？
            if ";" not in content:
                continue
            # 标记需要强制小写
            for item in siblings[1:]:
                item_content = item.element.get("PlainText", "").strip()
                if self.starts_with_abbreviation(item_content):
                    continue
                if self.starts_with_identifier(item_content):
                    continue

                if item_content and item_content[0].isupper():
                    # 标记为需要小写处理
                    item._needs_lowercase = True

    def _format_lowercase_start(self, list_item: ListItem) -> Dict[str, Any]:
        para_content = list_item.element.get("PlainText", "").strip()
        if para_content:
            modified_para_content = para_content[0].lower() + para_content[1:] if para_content else para_content
        else:
            modified_para_content = para_content

        return {
            "para_id": list_item.element.get("ElementId", ""),
            "para_content": para_content,
            "modified_para_content": modified_para_content,
            "commented_para_content": modified_para_content + "[1][Check List Tool]",
            "run_style_text": "",
            "patches": [],
        }

    def _make_comment(self, list_item: ListItem, comment_text: str) -> Dict[str, Any]:
        segments = list_item.element.get("Segments", [])
        target = [
            {
                "segId": seg["SegmentId"],
                "range": seg.get("Range", {"start": 0, "end": len(seg.get("SegmentText", ""))}),
            }
            for seg in segments
        ]

        patch = {
            "op": "commentAdd",
            "id": str(uuid.uuid4()),
            "target": target,
            "comment": {"text": comment_text},
        }
        return {
            "para_id": list_item.element.get("ElementId", ""),
            "modified_para_content": list_item.element.get("PlainText", ""),
            "commented_para_content": list_item.element.get("PlainText", ""),
            "run_style_text": "",
            "patches": [patch],
        }

    def check_list_item_rules(self, list_item: ListItem) -> ListValidationError:
        """
        检查列表项是否符合预定义的规则

        Args:
            list_item: 列表项元素

        Returns:
            ListValidationError: 返回验证结果枚举值
        """
        element = list_item.element
        content: str = element.get("PlainText", "").strip()
        properties = element.get("Properties", {})
        list_info = properties.get("ListInfo", {})
        label = list_info.get("LevelText", "")

        # note: 不去 element 的 Properties 中获取 Level，是因为可能是错误的
        #  应该使用由 list_item.level 计算出来的 level
        level = list_item.level

        # 规则8：不允许存在空的列表项
        if not content:
            self.logger.info("Empty list item")
            return ListValidationError.EMPTY_ITEM

        # 规则7：列表层级深度不能超过 level 2
        if level > 2:
            self.logger.info(f"List level too deep: {level}")
            return ListValidationError.LEVEL_TOO_DEEP

        # 规则2-4：检查标签格式
        expected_labels = {0: "•", 1: "◦", 2: "▪"}
        expected_label = expected_labels.get(level)
        if expected_label and label != expected_label:
            self.logger.info(f"Wrong label for level {level}: expected '{expected_label}', got '{label}'")
            return ListValidationError.WRONG_LABEL

        # 检查是否需要强制小写（由 _check_sibling_consistency 设置）
        if hasattr(list_item, "_needs_lowercase") and list_item._needs_lowercase:
            first_char = content.strip()[0] if content.strip() else ""
            if first_char.isupper():
                self.logger.info("Sibling consistency: uppercase letter should be lowercase")
                return ListValidationError.LOWERCASE_REQUIRED

        # 规则: 如果首个单词是缩写
        if self.starts_with_abbreviation(content):
            return ListValidationError.SUCCESS

        # 以 ':' 分割，前一部分是 identifier
        if self.starts_with_identifier(content):
            # 如果 identifier 是合法的标识符，则不需要检查首字母
            return ListValidationError.SUCCESS

        # 规则1：检查不完整句子的首字母
        if ";" in content and not content.endswith("."):
            first_char = content.strip()[0] if content.strip() else ""
            if first_char.isupper():
                self.logger.info("Incomplete sentence should not start with uppercase letter")
                return ListValidationError.UPPERCASE_START

        return ListValidationError.SUCCESS

    def starts_with_abbreviation(self, content: str) -> bool:
        """Check if the first word in the sentence is an abbreviation

        Supports multiple abbreviation formats, including possessive forms with 's
        """
        words = content.split()
        if words:
            first_word = words[0]
        else:
            return False

        # 移除句点和's后缀进行检测
        clean_word = first_word.rstrip(".'s")

        # 缩写检测正则模式
        patterns = [
            # 全大写缩写：FBI, NASA, HTML, CEO's, Dr.'s
            r"^[A-Z]{2,6}(\.)?(?:\'s)?$",
            # 首字母大写+句点：Dr., Mr., Prof.'s
            r"^[A-Z][a-z]*\.(?:\'s)?$",
            # 混合大小写：PhD, CEO, iPhone's
            r"^[A-Z]+[a-z]*[A-Z].*(?:\'s)?$",
            # 带句点的混合：U.S.A., U.K.'s
            r"^[A-Z](?:\.[A-Z])+\.?(?:\'s)?$",
            # 数字+字母组合：3G, 5G's, B2B
            r"^[0-9]*[A-Z][0-9A-Z]*(?:\'s)?$",
        ]

        return any(re.match(pattern, clean_word) for pattern in patterns)

    def starts_with_identifier(self, content: str) -> bool:
        """Check if the content has an identifier

        e.g. "Supreme Court of Victoria: xxxxxx"
        """
        identifier = None
        splites = content.split(":", 1)
        if len(splites) == 2:
            identifier = splites[0].strip()

        splites = content.split("—", 1)
        if len(splites) == 2:
            identifier = splites[0].strip()

        if identifier is None:
            return False

        words = identifier.split(" ")
        if words[0].islower():
            return False
        return True


def build_list_blocks(elements: List[Dict[str, Any]]) -> List[ListBlock]:
    """将连续的列表项组合成 ListBlock。

    ```
    - a
    - b
      - b.1
      - b.2
    - c

    转换为

    ListBlock{
        ListItem{
            element: {"ElementType": "Paragraph", "PlainText": "a"},
            indent: 0,
            level: 0
        },
        ListItem{
            element: {"ElementType": "Paragraph", "PlainText": "b"},
            indent: 0,
            level: 0,
            children: [
                ListItem{
                    element: {"ElementType": "Paragraph", "PlainText": "b.1"},
                    indent: 1,
                    level: 1
                },
                ListItem{
                    element: {"ElementType": "Paragraph", "PlainText": "b.2"},
                    indent: 1,
                    level: 1
                }
            ]
        },
        ListItem{
            element: {"ElementType": "Paragraph", "PlainText": "c"},
            indent: 0,
            level: 0
        }
    }
    """
    if not elements:
        return []

    # 第一步：识别连续的列表块
    list_blocks = []
    current_block = None

    for element in elements:
        element_type = element.get("ElementType")
        # 获取缩进信息
        properties = element.get("Properties", {})
        indent_left = properties.get("IndentLeft", 0)
        if element_type == "List":
            list_info = properties.get("ListInfo", {})
            indent_left = indent_left or list_info.get("Indent", 0)

            # 创建ListItem
            list_item = ListItem(element=element, indent=indent_left)

            # 如果是新的列表块
            if current_block is None:
                current_block = ListBlock()
                list_blocks.append(current_block)

            current_block.add(list_item)
        else:
            # 非列表元素，需要检查其 indentLeft 是否和当前列表块的最后一个元素相同
            is_last_on_top_level = current_block is not None and current_block.list_items and indent_left == 0
            if is_last_on_top_level:
                current_block = None

    # 第二步：为每个列表块构建层级结构
    for list_block in list_blocks:
        if list_block.list_items:
            _build_hierarchy_for_block(list_block)

    return list_blocks


def _build_hierarchy_for_block(list_block: ListBlock):
    """为单个列表块构建层级结构"""
    if not list_block.list_items:
        return

    root_items = []
    parent_stack = []

    for item in list_block.list_items:
        while parent_stack and item.indent <= parent_stack[-1].indent:
            parent_stack.pop()

        if not parent_stack:
            item.level = 0
            root_items.append(item)
        else:
            parent = parent_stack[-1]
            item.level = parent.level + 1
            parent.add_child(item)

        parent_stack.append(item)

    list_block.list_items = root_items
