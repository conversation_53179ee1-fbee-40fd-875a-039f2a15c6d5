import inspect
import textwrap
from abc import ABC, abstractmethod
from typing import Any, Class<PERSON><PERSON>, Iterator

from config_manager.manager import ENV
from llm_proxy import create_simple_proxy
from llm_proxy.schema import PredictResponse
from loguru import logger

from base.config.settings import config
from base.context_manager import KEYS, get_context
from base.example.core import Too<PERSON><PERSON>xample
from base.util.common import method_log, wrap_run


class BaseTool(ABC):
    """
    Abstract base class for all tools that work with ContextManager.

    This class provides a common interface for tools that use ContextManager
    to handle input and output data. All concrete tool implementations must inherit
    from this class and implement the run method.

    The context is automatically retrieved using the global context accessor,
    so there's no need to pass a ContextManager instance explicitly.
    """

    requires: ClassVar[set[KEYS]] = set()  # Optional list for input dependencies
    provides: ClassVar[set[KEYS]] = set()  # Optional list for output dependencies

    @property
    def context_manager(self):
        """
        Get the current ContextManager instance.

        This property provides backward compatibility for existing code
        that accesses self.context_manager directly.

        Returns:
            The current ContextManager instance from the global context.
        """
        return get_context()

    @abstractmethod
    def run(self) -> None:
        """
        Abstract method that must be implemented by all subclasses.

        This method should not take any parameters and should not return any value.
        All inputs should be retrieved through the context manager,
        and all outputs should be stored through the context manager.

        Example:
            def run(self) -> None:
                # Get the context manager
                context = get_context()

                # Get input data from context
                input_data = context.get('input_key')

                # Process the data
                result = self.process_data(input_data)

                # Store output data in context
                context.set('output_key', result)

                # Alternatively, you can use the context_manager property
                # which provides backward compatibility:
                # input_data = self.context_manager.get('input_key')
                # self.context_manager.set('output_key', result)
        """
        pass


class Tool(BaseTool):
    name: ClassVar[str]
    description: ClassVar[str]
    example: ClassVar[ToolExample | str]

    def __init_subclass__(cls, **kwargs):
        super().__init_subclass__(**kwargs)

        # 1) name fallback
        cls.name = cls.__name__

        # 2) description validation & filling (skip abstract classes)
        if not inspect.isabstract(cls):
            if not ENV.is_local:
                cls.run = wrap_run(cls)

            if getattr(cls, "description", None):
                return

            raw = textwrap.dedent(cls.__doc__ or "").strip()
            if not raw:
                raise TypeError(
                    f"{cls.__name__} must provide a class-level docstring for description"
                )
            cls.description = raw

    def __init__(self, **kwargs):
        self._parameters = kwargs
        self.logger = logger.bind(**{"name": self.name})

    @abstractmethod
    def run(self) -> Any: ...

    @property
    def parameters(self):
        return self._parameters


class LLMTool(Tool):
    tenant = config.LLM_TENANT
    model = config.LLM_MODEL  # default model
    temperature = config.LLM_MODEL_TEMPERATURE
    max_tokens = config.LLM_MODEL_MAX_TOKENS
    timeout = config.LLM_MODEL_TIMEOUT
    tracing_info = {"asset_id": config.ASSET_ID}
    stream = config.LLM_STREAM

    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        # load model from TOOL_MODEL_MAP
        self.model = config.TOOL_MODEL_MAP.get(self.name) or self.model
        self.logger.info(f"LLMTool {self.name}: set predict model {self.model}")

    @abstractmethod
    def prompt_template(self, text: str) -> str: ...

    @abstractmethod
    def post_process(self, text: str) -> str: ...

    @method_log
    def llm_predict(
        self, text: str, llm_model: str | None = None
    ) -> str | PredictResponse | Iterator:
        final_model = llm_model or self.model
        self.logger.info(f"{self.name}: llm predict with model: {final_model}")
        prompt = self.prompt_template(text)
        llm = create_simple_proxy(
            prompt=prompt,
            model=final_model,
            temperature=self.temperature,
            tenant=self.tenant,
            timeout=self.timeout,
            max_tokens=self.max_tokens,
        )
        output = llm.predict(stream=self.stream, tracing_info=self.tracing_info)
        self._last_llm_output = output
        return output
