import traceback
from collections import defaultdict
from typing import Dict, TypedDict

from base.util.convert import build_paragraph_level_spans
from base.util.label_adding import compare_texts_enhanced_v2, diff_sentinels

from .base import Tool


class ToolResult(TypedDict):
    tool_name: str
    input: str
    output: str


class MergeParagraphResultToolParameters(TypedDict):
    para_id: str
    original_text: str
    output_text: str
    xml_content: dict
    link_revisions: dict
    tool_results: dict[str, ToolResult]


class Change(TypedDict):
    RevId: str
    EditType: str
    Ops: list[dict]
    Link: list[dict]


class MergeParagraphResultToolResultReturn(TypedDict):
    ParaId: str
    Text: str
    ChangeText: list[Change]


class MergeParagraphResultTool(Tool):
    """Context is: # Integrate - base.tool\n
    MergeParagraphResultTool is used to consolidate the outputs from a chain of independent check tools
    (e.g., QuotationCheckTool, LegislationCheckTool, <PERSON>bbr<PERSON>ool, LinkCheckTool) that are executed sequentially.

    The tool compares the final output text against the original text and generates unified revision data
    based on the tool execution chain records.

    When using MergeParagraphResultTool, ensure tool_results correctly captures each tool's actual input and output:
    *   Each tool's input should be the content it actually receives (not the original paragraph content)
    *   Each tool's output should be the content it actually returns (not the final modified content)
    *   Form a proper tool execution chain: original_text -> tool1_input -> tool1_output -> tool2_input -> tool2_output -> final_output

    Args:
        A dictionary contain the following keys:
            - para_id (str): The ID of the paragraph.
            - original_text (str): The original paragraph text before any checks.
            - output_text (str): The final text after all tools have been executed in chain.
            - xml_content (dict): Output dict from the DataLoadTool.
            - link_revisions (dict, optional): A dictionary containing the results of the link check.
            - tool_results (dict, optional): A dictionary containing the results of each tool execution.
                Each tool result contains:
                - tool_name (str): The name of the tool.
                - input (str): The input text to the tool.
                - output (str): The output text from the tool.

    Returns:
        a dictionary contains:
            - ParaId (str): The ID of the paragraph.
            - Text (str): The original paragraph text.
            - ChangeText (List[str]): The final merged content after applying all tool outputs.
    """

    example = """```python
    from base.tool import MergeParagraphResultTool

    # Method 1: Chain-based tool execution with complete traceability
    data = {{
        'para_id': 'p1',
        'original_text': "This is the original paragraph.",
        'output_text': "This is the [final|modified] paragraph with all changes.",
        'xml_content': "This is the output dict of the DataLoadTool.",
        'tool_results': {{
            'quotation': {{
                "tool_name": "quotation",
                "input": "This is the original paragraph.",
                "output": "This is the [quote_check|delete|the] original paragraph.",
            }},
            'legislation': {{
                "tool_name": "legislation",
                "input": "This is the [quote_check|delete|the] original paragraph.",
                "output": "This is the [quote_check|delete|the] original paragraph. [legis_check|add|Act 2025]",
            }},
            'abbreviation': {{
                "tool_name": "abbreviation",
                "input": "This is the [quote_check|delete|the] original paragraph. [legis_check|add|Act 2025]",
                "output": "This is the [final|modified] paragraph with all changes.",
            }},
        }},
        'link_revisions': {{}}
    }}

    # Method 2: Using legacy parameter names (backward compatibility)
    data = {{
        'para_id': 'p1',
        'original_text': "This is the original paragraph.",
        'xml_content': "This is the output dict of the DataLoadTool.",
        'quote_check_text': "This is the [quote_check|delete|the] original paragraph.",
        'legis_check_text': "This is the original paragraph. [legis_check|add|Act 2025]",
        'abbr_check_text': "This is the original [abbr_check|delete|orig] paragraph.",
        'link_revisions': {{}}
    }}

    merger_tool = MergeParagraphResultTool(**data)
    result = merger_tool.run()
    print(result)
    ```
    """

    parameters: MergeParagraphResultToolParameters

    def run(self):
        # 1. Retrieve core parameters
        para_id = self.parameters.get("para_id")
        original_text = self.parameters.get("original_text", "")
        output_text = self.parameters.get("output_text", "")
        xml_content = self.parameters.get("xml_content", {})
        link_revisions = self.parameters.get("link_revisions", {})
        tool_results = self.parameters.get("tool_results", {})

        if not para_id:
            self.logger.warning(f"{self.name}: 'para_id' is missing. Defaulting to 'unknown_para_id'.")
            para_id = "unknown_para_id"

        response: MergeParagraphResultToolResultReturn = {
            "ParaId": str(para_id),  # Ensure ParaId is a string
            "Text": original_text,
            "ChangeText": [],
        }

        if not original_text:
            self.logger.error(f"{self.name}: 'original_text' is required.")
            return response

        # 2. Handle legacy parameters if new format is not provided
        if not output_text and not tool_results:
            self.logger.info(f"{self.name}: No output_text or tool_results provided, checking for legacy parameters")
            legacy_outputs = self._collect_legacy_tool_outputs()
            if legacy_outputs:
                # For backward compatibility, merge legacy outputs
                output_text = self._merge_legacy_outputs(original_text, legacy_outputs)
                # Create tool_results from legacy outputs for traceability
                tool_results = self._convert_legacy_to_tool_results(original_text, legacy_outputs)
            else:
                self.logger.info(f"{self.name}: para_id={para_id} no tool outputs found.")
                return response

        if not output_text:
            self.logger.warning(f"{self.name}: 'output_text' is missing, using original_text as fallback.")
            output_text = original_text

        # 3. Process the final output against original text
        self.logger.info(f"{self.name}: processing final output for para_id={para_id}")
        self.logger.info(f"{self.name}: tool chain executed {len(tool_results)} tools")

        paragraph_rev_maps = build_paragraph_level_spans(xml_content)
        rev_map = paragraph_rev_maps.get(str(para_id), {})
        self.logger.info(f"{self.name}: para_id={para_id}, rev_map={rev_map}")
        spans = rev_map.get("spans", [])
        self.logger.info(f"{self.name}: spans={spans}")

        if not spans:
            self.logger.info(f"{self.name}: para_id={para_id} no spans found.")

        # 4. Build suggestion lookup from tool execution chain
        suggestion_lookup = self._build_suggestion_lookup_from_chain(original_text, output_text, tool_results, spans)

        if not suggestion_lookup:
            self.logger.info(f"{self.name}: para_id={para_id} no changes detected between original and final text.")
            return response

        # 5. Generate final merged data
        self.logger.info(f"{self.name}: generating final revision data...")

        try:
            # Compare original_text with final output_text
            merged_data = compare_texts_enhanced_v2(
                original_text,
                output_text,
                "styling_agent",
                xml_content,
                para_id,
                suggestion_lookup,
            )

            # 6. Add link information to patches
            for patch in merged_data:
                rev_id = patch.get("RevId")
                if not rev_id:
                    continue

                link_patches = link_revisions.get(rev_id, [])
                if link_patches:
                    patch["Link"] = link_patches

            # 7. Add standalone link patches
            existing_rev_ids = {patch.get("RevId") for patch in merged_data if patch.get("RevId")}
            for rev_id, link_patches in link_revisions.items():
                if rev_id not in existing_rev_ids:
                    merged_data.append({"RevId": rev_id, "EditType": None, "Ops": [], "Link": link_patches})

            self.logger.info(
                f"{self.name}: Processing complete for para_id={para_id}, generated {len(merged_data)} patches."
            )
            response["ChangeText"] = merged_data

        except Exception as e:
            tb = traceback.format_exc()
            self.logger.error(f"[ERROR] ParaId={para_id}: {tb}")

        return response

    def _build_suggestion_lookup_from_chain(
        self, original_text: str, output_text: str, tool_results: Dict[str, ToolResult], spans
    ) -> defaultdict:
        """Build suggestion lookup from the tool execution chain.

        This analyzes each tool's contribution to the final changes.
        """
        suggestion_lookup = defaultdict(list)

        if not tool_results:
            self.logger.warning("No tool_results provided, suggestion lookup will be empty")
            return suggestion_lookup

        # Analyze the complete transformation from original to final
        final_diffs = diff_sentinels(original_text, output_text, spans, "final_chain")
        self.logger.info(f"Found {len(final_diffs)} differences to analyze")

        # Track statistics for debugging
        direct_matches = 0
        step_by_step_matches = 0
        fuzzy_matches = 0
        pattern_matches = 0
        unknown_count = 0

        # For each change, try to determine which tool(s) contributed
        for rev_id, seg, op, tool_type in final_diffs:
            # Find which tools in the chain could have contributed to this change
            contributing_tools = self._find_contributing_tools(seg, op, tool_results)

            if contributing_tools:
                suggestion_lookup[(rev_id, seg, op)].extend(contributing_tools)
                # Log which strategy worked (for debugging)
                self.logger.debug(
                    f"Found contributing tools {contributing_tools} for segment '{seg}' operation '{op}'"
                )
            else:
                # Last resort: use unknown_tool with detailed logging
                self.logger.warning(
                    f"Could not trace tool origin for segment '{seg}' (operation: {op}, rev_id: {rev_id}). "
                    f"Available tools: {list(tool_results.keys())}. Adding as unknown_tool."
                )
                suggestion_lookup[(rev_id, seg, op)].append("unknown_tool")
                unknown_count += 1

        # Log summary statistics
        total_changes = len(final_diffs)
        self.logger.info(
            f"Tool traceability summary: Total changes: {total_changes}, "
            f"Successfully traced: {total_changes - unknown_count}."
        )

        if unknown_count > 0:
            self.logger.warning(
                "High number of unknown tool origins detected. Consider:\n"
                "1. Checking tool_results format and completeness\n"
                "2. Verifying tool execution chain is properly recorded\n"
                "3. Adding more pattern rules in _infer_from_patterns method"
            )

        return suggestion_lookup

    def _find_contributing_tools(self, segment: str, operation: str, tool_results: Dict[str, ToolResult]) -> list[str]:
        """Find which tools in the execution chain contributed to a specific change.

        Uses multiple strategies to improve traceability:
        1. Direct string matching (original method)
        2. Fuzzy matching for similar content
        3. Position-based analysis
        4. Step-by-step diff analysis
        """
        contributing_tools = []

        # Strategy 1: Direct string matching (existing logic)
        for tool_name, tool_result in tool_results.items():
            tool_input = tool_result["input"]
            tool_output = tool_result["output"]

            # Check if this tool made changes involving the segment
            if operation == "del":
                # For deletions, check if the segment was removed by this tool
                if segment in tool_input and segment not in tool_output:
                    contributing_tools.append(self._normalize_tool_name(tool_name))
            elif operation == "ins":
                # For insertions, check if the segment was added by this tool
                if segment not in tool_input and segment in tool_output:
                    contributing_tools.append(self._normalize_tool_name(tool_name))

        # Strategy 2: If no direct match found, use step-by-step analysis
        if not contributing_tools:
            contributing_tools = self._analyze_step_by_step_changes(segment, operation, tool_results)

        # Strategy 3: If still no match, use fuzzy matching
        if not contributing_tools:
            contributing_tools = self._fuzzy_match_tools(segment, operation, tool_results)

        # Strategy 4: If still no match, try to infer from tool type patterns
        if not contributing_tools:
            contributing_tools = self._infer_from_patterns(segment, operation, tool_results)

        return contributing_tools

    def _analyze_step_by_step_changes(
        self, segment: str, operation: str, tool_results: Dict[str, ToolResult]
    ) -> list[str]:
        """Analyze tool chain step by step to find which tool introduced the change."""
        from base.util.label_adding import diff_sentinels

        # Create a chain of texts: original -> tool1_output -> tool2_output -> ... -> final
        tool_chain = []
        for tool_name, tool_result in tool_results.items():
            tool_chain.append((tool_name, tool_result["input"], tool_result["output"]))

        # Analyze each step
        for i, (tool_name, tool_input, tool_output) in enumerate(tool_chain):
            # Check if this specific tool introduced the segment
            try:
                step_diffs = diff_sentinels(tool_input, tool_output, [], f"step_{i}")
                for _, diff_seg, diff_op, _ in step_diffs:
                    # Check for exact match or substring match
                    if diff_op == operation and (diff_seg == segment or segment in diff_seg or diff_seg in segment):
                        return [self._normalize_tool_name(tool_name)]
            except Exception as e:
                self.logger.warning(f"Error analyzing step {i} for tool {tool_name}: {e}")
                continue

        return []

    def _fuzzy_match_tools(self, segment: str, operation: str, tool_results: Dict[str, ToolResult]) -> list[str]:
        """Use fuzzy matching to find tools that made similar changes."""
        import difflib

        best_matches = []
        threshold = 0.6  # Similarity threshold

        for tool_name, tool_result in tool_results.items():
            tool_input = tool_result["input"]
            tool_output = tool_result["output"]

            if operation == "del":
                # Find segments in input that were removed
                words_input = set(tool_input.split())
                words_output = set(tool_output.split())
                removed_words = words_input - words_output

                # Check similarity with the deleted segment
                for word in removed_words:
                    similarity = difflib.SequenceMatcher(None, word, segment).ratio()
                    if similarity > threshold:
                        best_matches.append((self._normalize_tool_name(tool_name), similarity))

            elif operation == "ins":
                # Find segments in output that were added
                words_input = set(tool_input.split())
                words_output = set(tool_output.split())
                added_words = words_output - words_input

                # Check similarity with the inserted segment
                for word in added_words:
                    similarity = difflib.SequenceMatcher(None, word, segment).ratio()
                    if similarity > threshold:
                        best_matches.append((self._normalize_tool_name(tool_name), similarity))

        # Return the best match(es)
        if best_matches:
            best_matches.sort(key=lambda x: x[1], reverse=True)
            return [best_matches[0][0]]  # Return the tool with highest similarity

        return []

    def _infer_from_patterns(self, segment: str, operation: str, tool_results: Dict[str, ToolResult]) -> list[str]:
        """Infer tool contribution based on known patterns and segment characteristics."""
        # Pattern-based inference rules
        patterns = {
            "quotation": ['"', "'", """, """, "«", "»"],
            "legislation": ["Act", "Section", "subsection", "paragraph", "clause"],
            "abbr": ["(", ")", "abbreviation", "acronym"],
            "link": ["http", "www", "link", "url", ".com", ".org", ".gov"],
        }

        # Check if segment matches any tool patterns
        segment_lower = segment.lower()
        for tool_type, keywords in patterns.items():
            if any(keyword.lower() in segment_lower for keyword in keywords):
                # Check if this tool type exists in the tool_results
                for tool_name in tool_results.keys():
                    normalized_name = self._normalize_tool_name(tool_name)
                    if normalized_name == tool_type or tool_type in normalized_name:
                        self.logger.info(
                            f"Inferred tool '{tool_name}' for segment '{segment}' based on pattern matching"
                        )
                        return [normalized_name]

        # If no pattern match, try to find the most likely tool based on execution order
        if tool_results:
            # Default to the first tool that actually made changes
            for tool_name, tool_result in tool_results.items():
                if tool_result["input"] != tool_result["output"]:
                    self.logger.info(f"Defaulting to first active tool '{tool_name}' for segment '{segment}'")
                    return [self._normalize_tool_name(tool_name)]

        return []

    def _collect_legacy_tool_outputs(self) -> Dict[str, str]:
        """
        Collect tool outputs using legacy parameter format for backward compatibility.
        """
        tool_outputs = {}

        # Define reserved parameter names that should not be treated as tool outputs
        reserved_params = {"para_id", "original_text", "output_text", "xml_content", "link_revisions", "tool_results"}

        # Strategy 1: Any parameter that is not reserved and contains valid string content
        for param_name, param_value in self.parameters.items():
            # Skip reserved parameters
            if param_name in reserved_params:
                continue

            # Only process string parameters with content
            if isinstance(param_value, str) and param_value.strip():
                tool_outputs[param_name] = param_value
                self.logger.info(f"{self.name}: Found legacy tool output for '{param_name}'")

        # Strategy 2: Legacy parameter mapping for backward compatibility
        legacy_mappings = {
            "quote_check_text": "quotation",
            "legis_check_text": "legislation",
            "abbr_check_text": "abbreviation",
        }

        for legacy_param, standard_name in legacy_mappings.items():
            if legacy_param in self.parameters and standard_name not in tool_outputs:
                param_value = self.parameters[legacy_param]
                if isinstance(param_value, str) and param_value.strip():
                    # Remove the legacy parameter name and add the standardized name
                    if legacy_param in tool_outputs:
                        del tool_outputs[legacy_param]
                    tool_outputs[standard_name] = param_value
                    self.logger.info(
                        f"{self.name}: Mapped legacy parameter '{legacy_param}' to tool '{standard_name}'"
                    )

        return tool_outputs

    def _merge_legacy_outputs(self, original_text: str, legacy_outputs: Dict[str, str]) -> str:
        """
        Merge legacy tool outputs to create a final output text.
        This simulates chain execution for backward compatibility.
        """
        from base.util.label_adding import merge_rv_sections_skip_conflict

        if not legacy_outputs:
            return original_text

        # Convert outputs to list and merge them
        tool_texts = list(legacy_outputs.values())
        return merge_rv_sections_skip_conflict(original_text, *tool_texts)

    def _convert_legacy_to_tool_results(
        self, original_text: str, legacy_outputs: Dict[str, str]
    ) -> Dict[str, ToolResult]:
        """
        Convert legacy tool outputs to tool_results format for traceability.
        """
        tool_results = {}
        current_input = original_text

        for tool_name, tool_output in legacy_outputs.items():
            tool_results[tool_name] = {"tool_name": tool_name, "input": current_input, "output": tool_output}
            current_input = tool_output  # Chain the outputs

        return tool_results

    def _normalize_tool_name(self, tool_name: str) -> str:
        """
        Normalize tool names for consistent processing.
        Maps various tool name formats to standardized names used by diff_sentinels.

        Args:
            tool_name: Original tool name

        Returns:
            Normalized tool name for diff processing
        """
        # Mapping of common tool name variations to standardized names
        name_mappings = {
            "quote": "quotation",
            "quotation": "quotation",
            "legis": "legislation",
            "legislation": "legislation",
            "abbr": "abbr",
            "abbreviation": "abbr",
            "link": "link",
            "check_link": "link",
        }

        # Try exact match first
        if tool_name in name_mappings:
            return name_mappings[tool_name]

        # Try without common prefixes/suffixes
        cleaned_name = tool_name.lower()
        for prefix in ["check_", "tool_"]:
            if cleaned_name.startswith(prefix):
                cleaned_name = cleaned_name[len(prefix) :]
                break

        for suffix in ["_check", "_tool"]:
            if cleaned_name.endswith(suffix):
                cleaned_name = cleaned_name[: -len(suffix)]
                break

        # Try mapping again with cleaned name
        if cleaned_name in name_mappings:
            return name_mappings[cleaned_name]

        # Default: return original name in lowercase
        return tool_name.lower()

    def _get_revision_by_id(self, para_id: str, rev_id: str, xml_content: list[dict]):
        # Placeholder for actual implementation to retrieve revision by ID
        # This should be replaced with the actual logic to fetch the revision data
        para_id = str(para_id)  # Ensure para_id is a string
        for item in xml_content:
            if item["ParaId"] != para_id:
                continue
            for r in item["Runs"]:
                if r.get("Id") == rev_id:
                    return r
        return None
