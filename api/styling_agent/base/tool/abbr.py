import re

from base.example.abbr import ABBR_EXAMPLE
from base.model import ParaState
from base.prompt.tooling_prompt import prompt_abbr_check
from base.util.convert import convert_yaml_to_json_if_possible

from .base import LLMTool


class AbbrTool(LLMTool):
    """Context is: # Integrate - base.tool\n
    AbbrTool has integrated base.tool for identify, check and modify abbreviation style in doc. There is one aspect of AbbrTool understand xml with user's document:\n
    - Use AbbrTool run for execute.\n

    Requires(context):
        These parameters are obtained from `self.context_manager` and do not need to be passed explicitly:
        - para_state (ParaState): The paragraph state of the document, retrieved from `self.context_manager`.
          - para_content (str): The content of the paragraph.

    Provides(context):
        - para_state (ParaState): The updated paragraph state after running the styling tool.
          - modified_para_content (str): The modified content of the paragraph.

    Args:
        The following parameters must be explicitly passed.
        a dictionary with the following keys:
        - para_id (str): The ID of the paragraph.

    Returns:
      a dictionary with the following keys:
        - para_state (ParaState): A ParaState object containing the results of the abbreviation check.
    """

    write_keys = {"para_state"}
    example = ABBR_EXAMPLE

    def run(self) -> ParaState | None:
        para_id = self.parameters.get("para_id", "")
        para_state = self.context_manager.get_para_state(para_id)
        if not para_state:
            self.logger.error(f"{self.name}: No paragraph state found for para_id {para_id}.")
            return None
        revision_paragraph = para_state.para_content
        try:
            result_content = self.llm_predict(revision_paragraph)
        except Exception as e:
            self.logger.error(f"{self.name}: error in abbreviation check: {e}")
            return para_state
        pattern = r"<modified_text>(.*?)</modified_text>"
        matched_result = re.findall(pattern, result_content, re.DOTALL)
        if not matched_result:
            self.logger.debug(f"{self.name}: error no modified patterns found")
            return para_state
        modified_snippet = matched_result[0].strip()
        para_state.modified_para_content = modified_snippet
        return para_state

    def prompt_template(self, text):
        return prompt_abbr_check.format(text)

    def post_process(self, text):
        return convert_yaml_to_json_if_possible(text)
