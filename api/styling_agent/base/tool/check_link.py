import socket
import uuid
from typing import cast
from urllib.parse import urlparse

import dns.resolver
import requests

from base.model import ParaState
from base.model.document import DocumentInfo, Segment
from base.util.constants import BLOCKED_DOMAINS, CNAME_PATTERNS, WHITE_LIST_SUFFIXES

from .base import Tool


class CheckLinkTool(Tool):
    """Context is: # Integrate - base.tool\n
    CheckLinkTool has integrated base.tool for link validation and connectivity checks for concurrent paragraphs.\n
    - Use CheckLinkTool to validate URLs.\n

    Requires(context):
        These parameters are obtained from `self.context_manager` and do not need to be passed explicitly:
        - links (list): The links to be checked, retrieved from `self.context_manager`.
        - xml_content (dict): The XML content of the document, also retrieved from `self.context_manager`.

    Provides(context):
        - para_state (ParaState): The updated paragraph state after running the styling tool.
          - patches (list): The tool will append the patch operations to the patches attribute of the para_state.

    Args:
        The following parameters must be explicitly passed.
        a dictionary with the following keys:
        - para_id (str): The ID of the paragraph.

    Returns:
      a dictionary with the following keys:
        - para_state (ParaState): A ParaState object containing the results of the link check.
    """

    example = """```python\n
        from base.tool import CheckLinkTool
        # prepare your data in dictionary format as we previously mentioned
        if data.get('link'):
            rlt = CheckLinkTool(**data)
            result = rlt.run()\n
        ```"""

    def run(self) -> ParaState | None:
        para_state = self.context_manager.get_para_state(self.parameters["para_id"])
        document_info = self.context_manager.document_info
        if not para_state:
            self.logger.error(
                f"{self.name}: No paragraph state found for para_id {self.parameters['para_id']}"
            )
            return
        if not document_info:
            self.logger.error(
                f"{self.name}: No document info found for para_id {self.parameters['para_id']}"
            )
            return

        all_ops = []
        for data in para_state.links:
            targets = cast(list[str], data.get("Targets", []))
            uri = cast(str, data.get("Uri", ""))
            t = data.get("Type")
            segments = self.get_segments(para_state.para_id, document_info)
            if not segments:
                self.logger.warning(
                    f"No segments found for para_id {para_state.para_id}"
                )
                continue
            segments_map = {seg.segment_id: seg for seg in segments}

            try:
                comment = self.get_link_valid_message(uri)
                # today_utc = datetime.now(timezone.utc).strftime("%Y-%m-%dT%H:%M:%SZ")
                if t == "Hyperlink":
                    patch_target = []
                    for target_id in targets:
                        start = segments_map[target_id].start
                        end = segments_map[target_id].end
                        patch_target.append(
                            {
                                "segId": target_id,
                                "range": {"start": 0, "end": end - start + 1},
                            }
                        )
                    patch = {
                        "id": str(uuid.uuid4()),
                        "op": "commentAdd",
                        "target": patch_target,
                        "comment": {"text": f"AI suggestion: {comment}"},
                    }
                    all_ops.append(patch)
                elif t == "Comment":
                    patch = {
                        "id": str(uuid.uuid4()),
                        "op": "commentReply",
                        "comment": {
                            "parentCommentId": targets[0],
                            "text": f"AI suggestion: {comment}",
                        },
                    }
                    all_ops.append(patch)
                else:
                    self.logger.warning(f"Unknown link type {t} for {uri}, skipping.")
                    continue
            except Exception as e:
                self.logger.error(f"Error checking link {uri}: {e}")

        para_state.patches.extend(all_ops)
        return para_state

    def get_segments(self, para_id: str, document_info: DocumentInfo) -> list[Segment]:
        for element in document_info.iter_paragraph():
            if element.element_id != para_id:
                continue
            return element.segments
        return []

    def get_link_valid_message(self, url: str) -> str:
        """
        Check a URL and return exactly one of four messages:
          1) Blacklisted domain → blocked
          2) Trusted gov suffix but HTTP non-200 → not accessible
          3) Non-trusted suffix but HTTP 200       → not trusted
          4) Both trusted and accessible           → OK
        """
        parsed = urlparse(url)
        domain = parsed.netloc.lower()
        hostname = parsed.hostname.lower() if parsed.hostname else ""

        # 1) Blacklist check first
        if any(
            domain == blocked or domain.endswith("." + blocked)
            for blocked in BLOCKED_DOMAINS
        ):
            return "Link provided is from blocked list, please be aware."

        # 2) Whitelist check
        is_trusted = any(domain.endswith(suf) for suf in WHITE_LIST_SUFFIXES)

        # 3) Connectivity check: consider only 2xx successes
        suspect_dns = self._check_dns(hostname)  # (1) if the domain is a CDN/WAF
        tcp_ok = self._tcp_probe(hostname, 443, 3)  # (2) if the TCP connection is OK

        is_anti_scraping = False
        try:
            resp = requests.head(url, timeout=30, allow_redirects=True)
            if 200 <= resp.status_code < 300:
                is_accessible = True
            else:
                # fallback to GET if HEAD fails or gets weird status
                resp = requests.get(url, timeout=30, allow_redirects=True)
                is_accessible = 200 <= resp.status_code < 300
        except requests.exceptions.Timeout:
            is_accessible = False
            is_anti_scraping = suspect_dns and tcp_ok
        except requests.exceptions.RequestException:
            is_accessible = False

        # 4) Return the correct scenario message
        if is_anti_scraping:
            return "Link is blocked by anti-scraping, suggest manual check."
        if is_trusted and not is_accessible:
            return "Link not accessible, suggest checking with legal editor."
        if is_accessible and not is_trusted:
            return "Link not trusted, suggest checking with legal editor."
        if not is_trusted and not is_accessible:
            return "Link is not trusted and not accessible, suggest checking with legal editor."
        # trusted & accessible
        return "Link is trusted and accessible."

    @staticmethod
    def _tcp_probe(host: str, port: int, timeout: float) -> bool:
        try:
            with socket.create_connection((host, port), timeout):
                return True
        except Exception:
            return False

    @staticmethod
    def _check_dns(host: str) -> bool:
        """check if the domain is a CDN/WAF"""
        try:
            answers = dns.resolver.resolve(host, "CNAME", lifetime=4)
            return any(
                CNAME_PATTERNS.search(r.target.to_text().rstrip(".")) for r in answers
            )
        except Exception:
            return False
