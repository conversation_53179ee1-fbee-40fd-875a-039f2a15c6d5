import re
from typing import TypedDict, cast

from base.example.prompt_combine import PROMPT_COMBINE_EXAMPLE
from base.model import ParaState
from base.prompt.dynamics_tooling_prompt import (
    abbreviation_rule,
    capital_rule,
    content_input,
    emphasis_rule,
    grammar_rule,
    instruction,
    legislation_rule,
    modified_text_output_general_phase_2,
    modified_text_output_legislation_phase_2,
    number_rule,
    quote_rule,
    spelling_rule,
    prompt_list_check,
)
from base.tool.heading import HeadingCheckTool
from base.util.convert import convert_yaml_to_json_if_possible
from base.util.legislation import replace_legis_info

from .base import LLMTool

MODIFIED_RE = re.compile(r"<modified_text>(.*?)</modified_text>", re.S | re.I)
COMMENTED_RE = re.compile(r"<commented_text>(.*?)</commented_text>", re.S | re.I)
LEGIS_RE = re.compile(r"<legis>(.*?)</legis>", re.S | re.I)


class StylingToolReturn(TypedDict):
    para_id: str
    modified_para_content: str
    commented_para_content: str
    run_style_text: str
    patches: list[dict]


class StylingTool(LLMTool):
    """Context is: # Integrate - base.tool\n
    StylingTool has integrated base.tool for combine relevant prompts corresponding to paragraph contents based
    on the context from the upstream, and run llm prompted by the combined prompt. There is one aspect
    of PromptCombineTool understand upstream RevisionRegTool outcome and combine corresponding prompts:\n
    - Use PromptCombineTool run for execute.\n

    Requires(context):
        These parameters are obtained from `self.context_manager` and do not need to be passed explicitly:
        - para_state (ParaState): The paragraph state of the document, retrieved from `self.context_manager`.
          - para_content (str): The content of the paragraph.
          - run_style_text (str): The run style text of the paragraph.

    Provides(context):
        - para_state (ParaState): The updated paragraph state after running the styling tool.
          - modified_para_content (str): The modified content of the paragraph.
          - commented_para_content (str): The content with comments added, if applicable.
          - run_style_text (str): The run style text of the paragraph.

    Args:
        The following parameters must be explicitly passed.
        a dictionary with the following keys:
        - para_id (str): The ID of the paragraph.

    Returns:
        para_state (ParaState): The updated paragraph state after running the styling tool.
        The para_state contains the following attributes:
        - para_id (str): The ID of the paragraph.
        - para_content (str): The content of the paragraph.
        - modified_para_content (str): The modified content of the paragraph.
        - commented_para_content (str): The content of the paragraph with comments.
        - run_style_text (str): The run style text of the paragraph.
        - par_style_text (str): The paragraph style text.
        - patches (list): A list of patches applied to the paragraph.
    The tool will return the updated para_state with the modified content, commented content, and run style text.
    If no modifications are made, the original content will be returned.
    """

    example = PROMPT_COMBINE_EXAMPLE

    def run(self) -> ParaState | None:
        para_state = self.context_manager.get_para_state(self.parameters["para_id"])
        if not para_state:
            self.logger.error(f"{self.name}: No paragraph state found for para_id {self.parameters['para_id']}")
            return None
        revision_paragraph = para_state.run_style_text
        run_style_text = para_state.run_style_text
        para_state.modified_para_content = para_state.para_content

        default_result = {
            "para_id": para_state.para_id,
            "modified_para_content": para_state.modified_para_content,
            "commented_para_content": para_state.commented_para_content,
            "run_style_text": para_state.run_style_text,
            "patches": [],
        }

        prompt_parts = [instruction]
        rule_num = 1
        prompt_output_guide = modified_text_output_general_phase_2

        is_heading_enabled = para_state.is_heading_enabled()
        is_quotation_enabled = para_state.is_quotation_enabled()
        is_abbr_enabled = para_state.is_abbr_enabled()
        is_capital_enabled = para_state.is_capital_enabled()
        is_grammar_enabled = para_state.is_grammar_enabled()
        is_spelling_enabled = para_state.is_spelling_enabled()
        is_emphasis_enabled = para_state.is_emphasis_enabled()
        is_leg_ac_enabled = para_state.is_leg_ac_enabled()
        is_link_enabled = para_state.is_link_enabled()
        is_num_enabled = para_state.is_num_enabled()
        is_list = para_state.is_list()

        # Define rule configuration for better maintainability
        rule_flags = {
            "heading": is_heading_enabled,
            "quotation": is_quotation_enabled,
            "abbr": is_abbr_enabled,
            "capital": is_capital_enabled,
            "grammar": is_grammar_enabled,
            "spelling": is_spelling_enabled,
            "emphasis": is_emphasis_enabled,
            "leg_ac": is_leg_ac_enabled,
            "link": is_link_enabled,
            "num": is_num_enabled,
        }

        # Check if only link processing is enabled (link enabled, all others disabled)
        non_link_rules = {k: v for k, v in rule_flags.items() if k != "link"}
        is_only_link_processing = rule_flags["link"] and not any(non_link_rules.values())

        # Check if all rules are disabled
        all_rules_disabled = len(para_state.enabled_tools) == 0

        # Early return for edge cases
        if is_only_link_processing or all_rules_disabled:
            return para_state

        if is_heading_enabled:
            para_style_check_tool = HeadingCheckTool(para_id=para_state.para_id)
            para_style_check_tool.run()
            return para_state

        if is_quotation_enabled:
            prompt_parts.append(quote_rule.format(num=rule_num))
            rule_num += 1
        if is_abbr_enabled:
            prompt_parts.append(abbreviation_rule.format(num=rule_num))
            rule_num += 1
        if is_capital_enabled:
            prompt_parts.append(capital_rule.format(num=rule_num))
            rule_num += 1
        if is_grammar_enabled:
            prompt_parts.append(grammar_rule.format(num=rule_num))
            rule_num += 1
        if is_spelling_enabled:
            prompt_parts.append(spelling_rule.format(num=rule_num))
            rule_num += 1
        if is_emphasis_enabled:
            prompt_parts.append(emphasis_rule.format(num=rule_num))
            rule_num += 1
        if is_num_enabled:
            prompt_parts.append(number_rule.format(num=rule_num))
            rule_num += 1
        if is_leg_ac_enabled:
            prompt_parts.append(legislation_rule.format(num=rule_num))
            rule_num += 1
            prompt_output_guide = modified_text_output_legislation_phase_2

        prompt_parts.extend([content_input, prompt_output_guide])

        if is_list:
            prompt_parts.append(prompt_list_check)

        self.combined_prompt = "\n".join(prompt_parts)

        try:
            result_content = cast(str, self.llm_predict(revision_paragraph))
        except Exception as e:
            self.logger.error(f"{self.name}: error in prompt combination: {e}")
            return para_state

        # Extract modified text based on the type of check
        if is_leg_ac_enabled:
            mod_res = self.extract_last_modified_with_legis(
                result_content,
                self.parameters["para_id"],
                replace_legis_info,
                default_result=default_result,
            )
            com_res = self.extract_last_commented_with_legis(
                result_content,
                self.parameters["para_id"],
                replace_legis_info,
                default_result=default_result,
            )

        else:
            mod_res = self.extract_last_modified_only(
                para_id=self.parameters["para_id"],
                result_text=result_content,
                default_result=default_result,
            )
            com_res = self.extract_last_commented_only(
                para_id=self.parameters["para_id"],
                result_text=result_content,
                default_result=default_result,
            )

        mod_res["modified_para_content"] = (
            mod_res.get("modified_para_content", "")
            .replace("for example,", "eg")
            .replace("for example", "eg")
            .replace("e.g.,", "eg")
            .replace("e.g.", "eg")
            .replace("that is,", "ie")
            .replace("i.e.,", "ie")
            .replace("i.e.", "ie")
            .replace(" eg,", " eg")
        )
        com_res["commented_para_content"] = (
            com_res.get("commented_para_content", "")
            .replace("for example,", "eg[Abbreviation Check Tool]")
            .replace("for example", "eg[Abbreviation Check Tool]")
            .replace("e.g.,", "eg[Abbreviation Check Tool]")
            .replace("e.g.", "eg[Abbreviation Check Tool]")
            .replace("that is,", "ie[Abbreviation Check Tool]")
            .replace("i.e.,", "ie[Abbreviation Check Tool]")
            .replace("i.e.", "ie[Abbreviation Check Tool]")
            .replace(" eg,", " eg[Abbreviation Check Tool]")
        )

        para_state.modified_para_content = mod_res.get("modified_para_content", "")
        para_state.commented_para_content = com_res.get("commented_para_content", "")
        para_state.run_style_text = run_style_text

        return para_state

    def prompt_template(self, text: str):
        return self.combined_prompt.format(text)

    def post_process(self, text: str):
        return convert_yaml_to_json_if_possible(text)

    def extract_last_modified_with_legis(
        self,
        result_text: str,
        para_id: str,
        replace_legis,
        default_result: StylingToolReturn | None = None,
    ):
        """
        1. 找到最后一段 <modified_text>
        2. 先向后找最近的 <legis>
        - 若找到 → 用它
        - 若没找到 → 向前找最近的 <legis>
        3. 把 <legis> 内容拆开后交给 replace_legis
        """
        # lst modified_text
        mods = list(MODIFIED_RE.finditer(result_text))
        if not mods:
            self.logger.debug("No <modified_text> found; skip replacement.")
            return default_result or {"para_id": para_id, "modified_para_content": ""}

        last_mod = mods[-1]
        mod_clean = self._strip_nested_modified(last_mod.group(1))
        after_idx = last_mod.end()
        before_idx = last_mod.start()

        # find the last <legis> after the last modified_text
        m = LEGIS_RE.search(result_text, after_idx)
        if not m:
            # find the previous modified_text, but only to the previous one (or the beginning of the text)
            prev_boundary = mods[-2].end() if len(mods) >= 2 else 0
            segment = result_text[prev_boundary:before_idx]
            m = LEGIS_RE.search(segment)

        if not m:
            self.logger.debug("No <legis> found near final <modified_text>; skip replacement.")

        legis_raw = m.group(1).strip() if m else ""
        legis_raw = self._strip_nested_legis(legis_raw)
        legis_list = [x.strip() for x in legis_raw.split("|") if x.strip()]

        # replace & return
        mod_clean = replace_legis(mod_clean, legis_list)
        return {"para_id": para_id, "modified_para_content": mod_clean}

    @staticmethod
    def _strip_nested_modified(snippet: str) -> str:
        while True:
            pos = snippet.lower().find("<modified_text>")
            if pos == -1:
                break

            snippet = snippet[pos + len("<modified_text>") :]
        return snippet.strip()

    @staticmethod
    def _strip_nested_commented(snippet: str) -> str:
        while True:
            pos = snippet.lower().find("<commented_text>")
            if pos == -1:
                break

            snippet = snippet[pos + len("<commented_text>") :]
        return snippet.strip()

    @staticmethod
    def _strip_nested_legis(snippet: str) -> str:
        while True:
            pos = snippet.lower().find("<legis>")
            if pos == -1:
                break

            snippet = snippet[pos + len("<legis>") :]
        return snippet.strip()

    def extract_last_modified_only(
        self,
        para_id: str,
        result_text: str,
        default_result: StylingToolReturn | None = None,
    ) -> dict:
        """
        从 AI 输出中提取最后一个 <modified_text>

        Args:
            result_text: AI 返回的完整字符串
            para_id    : 当前段落 ID
            default_result: 找不到标签时返回的兜底结果

        Returns:
            {"para_id": para_id, "modified_para_content": snippet | "" | default_result}
        """
        matches = list(MODIFIED_RE.finditer(result_text))
        if not matches:
            self.logger.debug("No <modified_text> found; return default.")
            return default_result or {"para_id": para_id, "modified_para_content": ""}

        last_snippet = self._strip_nested_modified(matches[-1].group(1))
        return {"para_id": para_id, "modified_para_content": last_snippet}

    def extract_last_commented_only(
        self,
        para_id: str,
        result_text: str,
        default_result: StylingToolReturn | None = None,
    ) -> dict:
        """
        从 AI 输出中提取最后一个 <comment>

        Args:
            result_text: AI 返回的完整字符串
            para_id    : 当前段落 ID
            default_result: 找不到标签时返回的兜底结果
        Returns:
            {"para_id": para_id, "commented_para_content": snippet | "" | default_result}
        """
        matches = list(COMMENTED_RE.finditer(result_text))
        if not matches:
            self.logger.debug("No <comment> found; return default.")
            return default_result or {"para_id": para_id, "commented_para_content": ""}

        last_snippet = self._strip_nested_commented(matches[-1].group(1))
        return {"para_id": para_id, "commented_para_content": last_snippet}

    def extract_last_commented_with_legis(
        self,
        result_text: str,
        para_id: str,
        replace_legis,
        default_result: StylingToolReturn | None = None,
    ):
        """
        1. 找到最后一段 <comment>
        2. 先向后找最近的 <legis>
        - 若找到 → 用它
        - 若没找到 → 向前找最近的 <legis>
        3. 把 <legis> 内容拆开后交给 replace_legis
        """
        # lst commented_text
        comments = list(COMMENTED_RE.finditer(result_text))
        if not comments:
            self.logger.debug("No <comment> found; skip replacement.")
            return default_result or {"para_id": para_id, "commented_para_content": ""}

        last_comment = comments[-1]
        comment_clean = self._strip_nested_commented(last_comment.group(1))
        after_idx = last_comment.end()
        before_idx = last_comment.start()

        # find the last <legis> after the last comment
        m = LEGIS_RE.search(result_text, after_idx)
        if not m:
            # find the previous comment, but only to the previous one (or the beginning of the text)
            prev_boundary = comments[-2].end() if len(comments) >= 2 else 0
            segment = result_text[prev_boundary:before_idx]
            m = LEGIS_RE.search(segment)

        if not m:
            self.logger.debug("No <legis> found near final <comment>; skip replacement.")

        legis_raw = m.group(1).strip() if m else ""
        legis_raw = self._strip_nested_legis(legis_raw)
        legis_list = [x.strip() for x in legis_raw.split("|") if x.strip()]

        # replace & return
        comment_clean = replace_legis(comment_clean, legis_list)
        return {"para_id": para_id, "commented_para_content": comment_clean}
