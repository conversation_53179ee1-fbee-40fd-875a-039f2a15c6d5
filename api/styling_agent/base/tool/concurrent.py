import concurrent.futures

from base.error import NoAIProcessParagraphError
from base.example.concurrent import CONCURRENT_TOOL_EXAMPLE

from .base import Tool


class ConcurrentTool(Tool):
    """Context is: # Integrate - base.tool\n
    ConcurrentTool is designed to process multiple paragraphs concurrently,
    improving performance when handling large documents with I/O-intensive operations.\n
    It executes different checking tools concurrently based on the paragraph analysis from RevisionRegTool.\n
    - Use ConcurrentTool.run() to execute all enabled tools on each paragraph concurrently.\n

    Requires(context):
        These parameters are obtained from `self.context_manager` and do not need to be passed explicitly:
        - para_states (List[ParaState]): The paragraph states of the document, retrieved from `self.context_manager`.

    Args:
        These parameters are need to be passed explicitly:
        process_function (Callable[[str, Any], ParaState]):
            A function taking (para_id) and returning a paragraph state.
    """

    example = CONCURRENT_TOOL_EXAMPLE

    def run(self):
        para_states = self.context_manager.get_para_states()
        process_function = self.parameters.get("process_function")

        if not process_function:
            self.logger.error(f"{self.name}: No process function provided")
            raise NoAIProcessParagraphError()

        if not para_states:
            return

        self.logger.info(f"{self.name}: Processing {len(para_states)} paragraphs with tools")
        # Prepare to collect results
        # Optimize worker count based on paragraph count
        optimal_workers = self._worker_count(len(para_states), 20)

        revisions = []
        with concurrent.futures.ThreadPoolExecutor(max_workers=optimal_workers) as executor:
            # Create a dictionary to map futures to their respective paragraphs and tools
            future_to_para_id = {
                executor.submit(process_function, paragraph.para_id): paragraph.para_id for paragraph in para_states
            }
            for future in concurrent.futures.as_completed(future_to_para_id):
                para_id = future_to_para_id[future]
                try:
                    merged_result = future.result()
                    if merged_result:
                        revisions.append(merged_result)
                except Exception as e:
                    self.logger.exception(f"{self.name}: Error processing paragraph {para_id}: {e}")

        # for para_state in para_states:
        #     try:
        #         merged_result = process_function(para_state.para_id)
        #         if merged_result:
        #             revisions.append(merged_result)
        #     except Exception as e:
        #         self.logger.exception(f"{self.name}: Error processing paragraph {para_state.para_id}: {e}")

        return revisions

    def _worker_count(self, paragraph_count: int, max_workers: int) -> int:
        """Calculate optimal worker count based on paragraph count and max workers."""
        if paragraph_count <= 10:
            optimal = 5
        elif paragraph_count <= 100:
            optimal = 20
        else:
            optimal = 50
        optimal = min(optimal, paragraph_count)
        if max_workers is not None:
            optimal = min(optimal, max_workers)

        return optimal
