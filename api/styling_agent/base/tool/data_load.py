import json
from urllib.parse import urlparse

from base.client.aws_lambda import lambda_client
from base.config.settings import config
from base.error.convert import ConvertLambdaError

from .base import Tool


class DataLoadTool(Tool):
    """Context is: # Integrate - base.tool\n
    DataLoadTool is integrated into base.tool for loading and processing word documents from file path:\n
    - Use DataLoadTool run for execute.\n

    Provides(context):
        These parameters are obtained from `self.context_manager` and do not need to be passed explicitly:
        - s3_file_path (str): The S3 file path of the Word document.
        - xml_content (bytes): The XML content of the Word document.

    Args:
        - path (str): The file path to the Word document (.docx) to be processed.

    Returns:
        - xml_content (bytes): The XML content of the Word document.
    """

    example = """```python\n
    from base.tool import DataLoadTool\n
    # prepare your data in dictionary format as we previously mentioned
    data = ['path': ""]
    wlt = DataLoadTool(**data)
    xml_content = wlt.run()
    # Check if xml_content is empty and return empty dict if so
    if not xml_content:
        return dict('updated_s3_key':"")\n
    ```"""

    def run(self) -> dict:
        # 1. download file from S3 to local
        parsed = urlparse(self.parameters["path"])
        if parsed.scheme != "s3":
            raise ValueError(f"Unsupported path format: {self.parameters['path']}")

        bucket = parsed.netloc
        key = parsed.path.lstrip("/")

        # 2. call word converter
        payload = {"Bucket": bucket, "Key": key}
        res = lambda_client.invoke(
            function_name=config.WORD_CONVERTER_NAME,
            payload=json.dumps(payload),
        )
        if res.get("Data") is None:
            self.logger.error("Convert failed, lambda response Data is None")
            raise ConvertLambdaError()

        self.logger.info("Convert success.")
        raw = res.get("Data")

        self.context_manager.set("s3_file_path", f"s3://{bucket}/{key}")
        self.context_manager.set("xml_content", raw)
        return raw
