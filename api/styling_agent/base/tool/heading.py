from base.example.heading import HEADING_EXAMPLE
from base.util.heading import para_checker

from .base import Tool


class HeadingCheckTool(Tool):
    """Context is: # Integrate - base.tool\n
    HeadingCheckTool has integrated base.tool for identify, check and modify heading paragraph format in doc.
    This tool validates paragraph styling properties against content requirements:\n
    - Use HeadingCheckTool run for execute.\n

    Requires(context):
        These parameters are obtained from `self.context_manager` and do not need to be passed explicitly:
        - para_state (ParaState): The paragraph state of the document, retrieved from `self.context_manager`.
          - para_content (str): The content of the paragraph.
          - par_style_text (str): The paragraph style, e.g., <pStyle'>Heading1</pStyle>

    Provides(context):
        - para_state (ParaState): The updated paragraph state after running the styling tool.
          - modified_para_content (str): The modified content of the paragraph.
          - commented_para_content (str): The content with comments added, if applicable.

    Args:
        The following parameters must be explicitly passed.
        a dictionary with the following keys:
            - para_id (str): The ID of the paragraph.

    Returns:
      a dictionary with the following keys:
        - para_id (str): The ID of the paragraph.
        - modified_para_content (str): The content updated based on property checks.
        - commented_para_content (str): The content with comments added, if applicable.
    """

    example = HEADING_EXAMPLE

    def run(self):
        para_id = self.parameters.get("para_id", "")
        para_state = self.context_manager.get_para_state(para_id)
        if not para_state:
            self.logger.error(f"{self.name}: No paragraph state found for para_id {para_id}.")
            return
        par_style_text = para_state.par_style_text
        modified_para_content = para_state.modified_para_content

        default_result = {
            "para_id": para_id,
            "modified_para_content": modified_para_content,
            "commented_para_content": "",
        }

        try:
            if par_style_text != "":
                para_res = para_checker.check_heading(par_style_text, modified_para_content)
                default_result["modified_para_content"] = para_res["para_content"]
                default_result["commented_para_content"] = para_res["comment_content"]
                para_state.modified_para_content = para_res["para_content"]
                para_state.commented_para_content = para_res["comment_content"]

        except Exception as e:
            self.logger.error(f"{self.name}: error in paragraph property check: {e}")

        return default_result
