import json
import os
from datetime import datetime, timedelta, timezone
from pathlib import Path
from urllib.parse import urlparse

from base.client.aws_lambda import lambda_client
from base.client.s3 import s3_client
from base.config.settings import config
from base.error import NoAIModifiedRevisionError
from base.error.apply_revision import ApplyRevisionInternalError

from .base import Tool

class WordUpdateTool(Tool):
    """Context is: # Integrate - base.tool\n
    WordUpdateTool is integrated into base.tool for updating word documents with final revisions list from file path:\n
    - Use WordUpdateTool run for execute.\n

    Requires(context):
        These parameters are obtained from `self.context_manager` and do not need to be passed explicitly:
        - s3_file_path (str): The S3 file path of the Word document, retrieved from `self.context_manager`.
        - patch_result (dict): The patch result of the document, retrieved from `self.context_manager`.

    Returns:
        - updated_s3_key (dict): dictionary with the following keys:
            - Bucket (str): The S3 bucket name.
            - Key (str): The S3 key where the updated Word document is stored.
    """

    example = """```python\n
    # Creating a text data\n
    from base.tool import WordUpdateTool
    # prepare your data in dictionary format as we previously mentioned
    data = ['path': "", "patches": []]
    wut = WordUpdateTool(**data)
    updated_s3_key = wut.run()\n
    ```"""

    def run(self) -> bytes:
        s3_file_path = self.context_manager.s3_file_path
        parsed = urlparse(s3_file_path)
        if parsed.scheme != "s3":
            raise ValueError(f"Unsupported path format: {self.parameters['path']}")

        bucket = parsed.netloc
        word_key = parsed.path.lstrip("/")

        patches = self.context_manager.patch_result
        if not patches:
            raise NoAIModifiedRevisionError()

        patches_key = self.upload_json_to_s3(patches, bucket)

        # Test if need to save patches locally
        if os.environ.get("JOB_PREFIX") == "local-run":
            shanghai_tz = timezone(timedelta(hours=8))
            date_time = datetime.now(shanghai_tz).strftime("%Y%m%d%H%M")

            def resolve_patches_path(date_time: str) -> Path:
                curr = Path(__file__).resolve().parent

                while not (curr / "pyproject.toml").exists():
                    if curr.parent == curr:
                        raise RuntimeError(
                            "Could not locate project root directory (pyproject.toml not found)"
                        )
                    curr = curr.parent

                project_root = curr

                data_dir = project_root / "script_data" / "data"
                data_dir.mkdir(parents=True, exist_ok=True)

                return data_dir / f"patches/{date_time}.json"

            destination_path = resolve_patches_path(date_time)
            if not destination_path.parent.exists():
                destination_path.parent.mkdir(parents=True, exist_ok=True)
            with open(destination_path, "w", encoding="utf-8") as file:
                json.dump(patches, file, indent=2, ensure_ascii=False)
            self.logger.info(f"Patches saved to local path: {destination_path}")

        payload = {
            "Document": {"Bucket": bucket, "Key": word_key},
            "Revision": {"Bucket": bucket, "Key": patches_key},
        }
        res = lambda_client.invoke(
            function_name=config.WORD_APPLY_NAME,
            payload=json.dumps(payload),
        )
        if not res:
            self.logger.error("Apply revision lambda response is None")
            raise ApplyRevisionInternalError()
        return res

    def upload_json_to_s3(self, obj: dict, bucket: str, prefix="revisions/") -> str:
        BASE = os.environ["JOB_PREFIX"]
        dest_key = f"{BASE}/ai_output.json"
        body = json.dumps(obj).encode("utf-8")
        s3_client.put_object(bucket, dest_key, body)
        return dest_key
