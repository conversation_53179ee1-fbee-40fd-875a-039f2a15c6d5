import re
import uuid
from typing import Any, Dict

from .base import Tool


class WordCountCheckTool(Tool):
    """A tool for counting the number of English words in text and checking if it exceeds a specified limit.

    The tool will add a patch to the para_state with the word count comment and use for whole document.

    Requires(context):
        These parameters are obtained from `self.context_manager` and do not need to be passed explicitly:
        - xml_content (str): The XML content of the document, retrieved from `self.context_manager`.
        - fulltext (str): The full text content of the document, retrieved from `self.context_manager`.

    Provides(context):
        - para_state (ParaState): The updated paragraph state after running the styling tool.
          - patches (list): The tool will append the patch operations to the patches attribute of the para_state.

    Args:
        - content_type (str): The type of content to check. Defaults to "Guidance Notes".

    Returns:
        list: A list containing a single 'commentAdd' operation. If parameters are missing,
              returns an empty list.
    """

    example = """```python
        from base.tool import WordCountCheckTool

        # Run the WordCountCheckTool
        word_count_tool = WordCountCheckTool()
        word_count_tool.run()
        ```"""

    def run(self):
        # Get input parameters
        # TODO: content_type should be passed as a parameter, currently hardcoded
        content_type = self.parameters.get("content_type", "Guidance Notes")
        xml_content = self.context_manager.xml_content
        fulltext = self.context_manager.fulltext

        # Validate parameters
        if not fulltext or not content_type or not xml_content:
            self.logger.warning(f"{self.name}: Missing required parameters, returning empty list")
            return []

        try:
            # Use regex to count English words
            word_pattern = r"\b[a-zA-Z]+\b"
            words = re.findall(word_pattern, fulltext)
            word_count = len(words)

            # Hardcoded word limit dictionary
            word_limits = {"Guidance Notes": 2500}

            # Get the corresponding word limit
            word_limit = word_limits.get(content_type)
            if word_limit is None:
                self.logger.warning(f"{self.name}: Unsupported content type '{content_type}'")
                return []

            # Compare word count and limit, generate comment text
            if word_count > word_limit:
                comment_text = f"Word count ({word_count}) exceeds the limit ({word_limit}). Please consider reducing the content."
            else:
                comment_text = f"Word count ({word_count}) is within the limit ({word_limit})."

            # Find the first Segment of the first paragraph in xml_content as the comment target
            target_element = self._find_first_segment(xml_content)
            if not target_element:
                self.logger.warning(f"{self.name}: Could not find a suitable target position for the comment")
                return []

            para_id = target_element.get("para_id", "")
            comment_op = self._make_comment(target_element, comment_text)
            para_state = self.context_manager.get_para_state(para_id)
            if not para_state:
                self.logger.error(f"{self.name}: No paragraph state found for para_id {para_id}.")
                return
            para_state.patches.append(comment_op)
        except Exception as e:
            self.logger.error(f"{self.name}: Error occurred during processing: {e}")
            return

    def _make_comment(self, target_element: dict, comment_text: str) -> Dict[str, Any]:
        """Create annotation operations, maintaining the same format as CheckListTool"""
        target = [
            {
                "segId": target_element["segId"],
                "range": target_element.get("range", {"start": 0, "end": 0}),
            }
        ]

        return {
            "op": "commentAdd",
            "id": str(uuid.uuid4()),
            "target": target,
            "comment": {"text": comment_text},
        }

    def _find_first_segment(self, xml_content: dict) -> dict | None:
        """Find the first Segment of the first paragraph from xml_content"""
        try:
            if not isinstance(xml_content, dict):
                return None

            # Extract the first paragraph that has segments
            elements = xml_content.get("Elements", [])
            for element in elements:
                if element.get("ElementType") == "Paragraph":
                    segments = element.get("Segments", [])
                    if not segments:
                        continue

                    plain_text = element.get("PlainText", "")
                    if not plain_text:
                        continue

                    # find the first segment with non-empty SegmentText
                    for segment in segments:
                        segment_text = segment.get("SegmentText", "")
                        if not segment_text:
                            continue

                        return {
                            "segId": segment.get("SegmentId", ""),
                            "range": {
                                "start": segment.get("Start", 0),
                                "end": segment.get("End", 0),
                            },
                            "para_id": element.get("ElementId", ""),
                            "para_content": plain_text,
                        }
            return None
        except Exception as e:
            self.logger.error(f"{self.name}: Error while searching for Segment: {e}")
            return None

    def _get_current_timestamp(self):
        """Get the current timestamp"""
        from datetime import datetime

        return datetime.now().isoformat()
