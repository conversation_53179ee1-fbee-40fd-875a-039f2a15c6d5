import re

from base.example.legis_check import LEGIS_EXAMPLE
from base.model import ParaState
from base.prompt.tooling_prompt import prompt_legislation_check
from base.util.convert import convert_yaml_to_json_if_possible
from base.util.legislation import replace_legis_info

from .base import LLMTool


class LegislationCheckTool(LLMTool):
    """Context is: # Integrate - base.tool\n
    LegislationCheckTool has integrated base.tool for identify, check and modify legislation style in doc. There is one aspect of LegislationCheckTool understand xml with user's document:\n
    - Use LegislationCheckTool run for execute.\n

    Requires(context):
        These parameters are obtained from `self.context_manager` and do not need to be passed explicitly:
        - para_state (ParaState): The paragraph state of the document, retrieved from `self.context_manager`.
          - para_content (str): The content of the paragraph.

    Provides(context):
        - para_state (ParaState): The updated paragraph state after running the styling tool.
          - modified_para_content (str): The modified content of the paragraph.

    Args:
        The following parameters must be explicitly passed.
        a dictionary with the following keys:
        - para_id (str): The ID of the paragraph.

    Returns:
      para_state (ParaState): The updated paragraph state after running the styling tool.
      The para_state contains the following attributes:
        - para_id (str): The ID of the paragraph.
        - para_content (str): The content of the paragraph.
        - modified_para_content (str): The modified content of the paragraph.
        - commented_para_content (str): The content of the paragraph with comments.
        - run_style_text (str): The run style text of the paragraph.
        - par_style_text (str): The paragraph style text.
        - patches (list): A list of patches applied to the paragraph.
    """

    example = LEGIS_EXAMPLE

    def run(self) -> ParaState | None:
        para_id = self.parameters.get("para_id", "")
        para_state = self.context_manager.get_para_state(para_id)
        if not para_state:
            self.logger.error(f"{self.name}: No paragraph state found for para_id {para_id}.")
            return None
        revision_paragraph = para_state.para_content
        para_state.modified_para_content = revision_paragraph
        try:
            result_content = self.llm_predict(revision_paragraph)
        except Exception as e:
            self.logger.error(f"{self.name}: error in legislation check: {e}")
            return para_state

        pattern1 = r"<modified_text>(.*?)</modified_text>"
        legis_count = len(re.findall("<legis>", result_content))  # patch for result content different
        pattern2 = r"(?:<legis>.*?)<legis>(.*?)</legis>" if legis_count > 1 else r"<legis>(.*?)</legis>"
        matched_result = re.findall(pattern1, result_content, re.DOTALL)
        matches = re.findall(pattern2, result_content, re.DOTALL)
        if not matched_result:
            self.logger.debug(f"{self.name}: error no modified patterns found")
            return para_state

        modified_snippet = matched_result[0].strip()
        if not matches:
            self.logger.debug(f"{self.name}: error no legislation patterns found")
            para_state.modified_para_content = modified_snippet
            return para_state

        legis_list = matches[0].strip().split("|")
        modified_snippet = replace_legis_info(modified_snippet, legis_list)
        para_state.modified_para_content = modified_snippet
        return para_state

    def prompt_template(self, text):
        return prompt_legislation_check.format(text)

    def post_process(self, text):
        return convert_yaml_to_json_if_possible(text)
