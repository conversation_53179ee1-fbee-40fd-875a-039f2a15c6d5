from base.example.auhtor_check import AUTHOR_ATTRIBUTION_EXAMPLE
from base.model import ParaState
from base.util.author_check import author_helper

from .base import Tool


class AuthorCheckTool(Tool):
    """Context is: # Integrate - base.tool\n
    AuthorAttributionTool has integrated base.tool for identify, check and modify author attribution format for concurrent paragraphs.
    There are four different formatting requirements for author attribution:\n
    - Use AuthorAttributionTool run for execute.\n

    Requires(context):
        These parameters are obtained from `self.context_manager` and do not need to be passed explicitly:
        - xml_content (str): The XML content of the document, retrieved from `self.context_manager`.
        - para_state (ParaState): The paragraph state of the document, retrieved from `self.context_manager`.
          - para_content (str): The content of the paragraph.

    Provides(context):
        - para_state (ParaState): The updated paragraph state after running the styling tool.
          - patches (list): The tool will append the patch operations to the patches attribute of the para_state.

    Args:
        The following parameters must be explicitly passed.
        a dictionary with the following keys:
        - para_id (str): The ID of the paragraph.

    Returns:
      a dictionary with the following keys:
        - para_state (ParaResultList): A ParaResultList object containing the results of the author attribution check.
    """

    example = AUTHOR_ATTRIBUTION_EXAMPLE

    def run(self) -> ParaState | None:
        para_id = self.parameters.get("para_id", "")
        xml_content = self.context_manager.get_xml_content()
        para_state = self.context_manager.get_para_state(para_id)
        if not para_state:
            self.logger.error(f"{self.name}: No paragraph state found for para_id {para_id}.")
            return None
        revision_paragraph = para_state.para_content

        try:
            if author_helper.is_author_paragraph(revision_paragraph):
                code, mod_text = author_helper.validate_with_regex(revision_paragraph)
                if code == -1:
                    return para_state
                if mod_text != revision_paragraph:
                    patche_content = author_helper.construct_patch_content(
                        para_id=para_id, code=code, xml_content=xml_content
                    )
                    para_state.patches.extend(patche_content)
                    # default_result["modified_para_content"] = mod_text
                    return para_state
                # verify
                match_res, idxs = author_helper.extract_tag_and_compare(para_state.run_style_text, revision_paragraph)
                if match_res == "not matched":
                    # build the comment str here
                    patche_style = author_helper.construct_patch_style(
                        para_id=para_id,
                        code=code,
                        indexes=idxs,
                        xml_content=xml_content,
                    )
                    para_state.patches.extend(patche_style)

        except Exception as e:
            self.logger.error(f"{self.name}: error in author attribution check: {e}")
        return para_state
