import base64
import datetime
import io
import json
import os
import re
import time
import traceback
from urllib.parse import unquote_to_bytes

import multipart
from base.agent import Agent
from base.client.aws_lambda import lambda_client
from base.client.s3 import s3_client
from base.config.settings import config
from base.error.planner import PlannerBadRequestError
from base.schema.executor import ExecutorRequest
from base.util.constants import (
    DEFAULT_ERROR_MSG,
    EMAIL_EMPTY_ERROR_MSG,
    EMAIL_INVALID_ERROR_MSG,
    EMAIL_SENT_DEFAULT_MSG,
    EMPTY_UPLOAD_FILE_ERROR_MSG,
    VALID_FILE_ERROR_MSG,
    VALID_FILE_EXTENSIONS,
)
from base.util.lambda_util import check_email, get_package_version, get_response
from common.util.logger import register_logger
from common.util.request_id_gen import request_id_context
from loguru import logger

register_logger()


def lambda_handler(event, context):
    logger.info(f"Agent version: {get_package_version()}")
    request_id = context.aws_request_id if hasattr(context, "aws_request_id") else "local-run"
    request_id_context.set(request_id)

    raw_path = try_to_get_raw_path(event)
    func_call = router_mapping.get(raw_path)

    start_time = time.monotonic()
    response = get_response(500, {"message": DEFAULT_ERROR_MSG})
    if not func_call:
        response = get_response(400, {"message": "Invalid path"})
    else:
        try:
            response = func_call(event, context)
        except PlannerBadRequestError as e:
            response = get_response(e.status_code, {"message": e.message})
        except Exception:
            tb = traceback.format_exc(-1)
            logger.error(f"Planner handler unexcept error: {tb}")

    end_time = time.monotonic()
    logger.info(f"Planner handler execution time: {end_time - start_time:.2f} seconds")
    return response


def base_handler(event, invocation_type: str = "RequestResponse", email: str | None = None):
    logger.info(f"Processing file upload request with invocation type: {invocation_type}")
    mode = try_to_get_mode(event)
    upload_file = try_to_get_upload_file(event)
    if not upload_file:
        raise PlannerBadRequestError(EMPTY_UPLOAD_FILE_ERROR_MSG)

    file_name = upload_file["name"]
    file_bytes = upload_file["bytes"]
    logger.debug(f"Received file: {file_name} ({len(file_bytes)} bytes)")

    # Check if the file format is valid
    if not is_valid_file_format(file_name):
        raise PlannerBadRequestError(VALID_FILE_ERROR_MSG)

    base_s3_key = get_base_s3_key(file_name)
    legal_id = get_legal_id(email)
    doc_suffix = "original.docx" if not legal_id else f"original_{legal_id}.docx"
    if legal_id:
        base_s3_key = f"{base_s3_key}_{legal_id}"
    document_name = f"{base_s3_key}/{file_name}_{doc_suffix}"

    upload_status = s3_client.put_object(
        bucket_name=config.S3_BUCKET,
        file_name=document_name,
        file_bytes=file_bytes,
    )
    if not upload_status:
        raise Exception("Failed to upload document to S3")

    s3_document_path = f"s3://{config.S3_BUCKET}/{document_name}"
    if mode == "full":
        user_question = f"please according the file path use {s3_document_path}, use styling tool, checklink tool, checklist tool, author check tool, word count check tool and concurrent tool generate the python script"
    elif mode == "revision":
        user_question = f"please according the file path use {s3_document_path}, use styling tool, checklink tool, author check tool and concurrent tool generate the python script only for revision paragraphs"
    script_content = generate_processing_script(user_question)
    script_key = f"{base_s3_key}/agent_script.py"

    script_upload_status = s3_client.put_object(config.S3_BUCKET, script_key, script_content.encode("utf-8"))
    if not script_upload_status:
        raise Exception("Failed to upload script to S3")

    executor_payload = get_executor_payload(script_key, base_s3_key)
    if email:
        executor_payload["email"] = email
    return request_executor(executor_payload, invocation_type)


def async_planner_handler(event, context):
    user_provided_email = try_to_get_email(event)

    # check if the email is valid
    if not user_provided_email:
        raise PlannerBadRequestError(EMAIL_EMPTY_ERROR_MSG)

    if not check_email(user_provided_email):
        raise PlannerBadRequestError(EMAIL_INVALID_ERROR_MSG)

    base_handler(event, "Event", user_provided_email)
    return get_response(200, {"message": EMAIL_SENT_DEFAULT_MSG})


def planner_handler(event, context):
    response = base_handler(event)
    if not response:
        raise Exception("Failed to get response from executor.")

    status_code = response["statusCode"]
    data = json.loads(response["body"])
    # not 0000 means failed from executor
    if status_code != 200 or data.get("code") != "0000":
        error_message = data.get("message") or DEFAULT_ERROR_MSG
        return get_response(status_code, {"message": error_message})

    data_response = data.get("data") or {}
    presigned_url = data_response.get("presigned_url", "")
    if presigned_url:  # return presigned_url if exists, otherwise return the error message
        return get_response(200, {"downloadUrl": presigned_url})
    raise Exception("Failed to get presigned URL from executor")


def try_to_get_raw_path(event: dict) -> str | None:
    raw_path = event.get("rawPath") or event.get("path") or event.get("requestContext", {}).get("http", {}).get("path")
    return raw_path


def try_to_get_email(event: dict) -> str | None:
    """
    Try to get the email from the event.
    :param event: The event to get the email from.
    :return: The email if found, otherwise None.
    """
    query_string_parameters = event.get("queryStringParameters") or {}
    email = query_string_parameters.get("email")
    return email


def try_to_get_mode(event: dict) -> str:
    """
    Try to get the mode from the event.
    :param event: The event to get the mode from.
    :return: The mode if found, otherwise "sync".
    """
    query_string_parameters = event.get("queryStringParameters") or {}
    mode = query_string_parameters.get("mode", "full")  # Default to 'full'

    # Validate mode value
    if mode not in ["full", "revision"]:
        logger.warning(f"Invalid mode '{mode}' provided, defaulting to 'full'")
        mode = "full"

    return mode


def try_to_get_upload_file(event: dict) -> dict | None:
    try:
        upload_file = {}

        raw_file = event["body"]
        body = base64.b64decode(raw_file) if event.get("isBase64Encoded") else raw_file.encode("utf-8")
        stream = io.BytesIO(body)
        headers = {k.lower(): v for k, v in event["headers"].items()}
        content_type = headers.get("content-type", "")
        content_type = unquote_to_bytes(content_type)

        def on_file(f):
            f.file_object.seek(0)
            upload_file["name"] = f.file_name.decode() if f.file_name else "upload"
            upload_file["bytes"] = f.file_object.read()

        multipart.parse_form(
            headers={"Content-Type": content_type},
            input_stream=stream,
            on_field=None,
            on_file=on_file,
        )
        return upload_file
    except Exception as e:
        logger.error(f"Error parsing multipart form: {e}")
    return None


def is_valid_file_format(file_name: str) -> bool:
    file_extionsion = os.path.splitext(file_name)[-1].lower()
    return file_extionsion in VALID_FILE_EXTENSIONS


def get_base_s3_key(file_name: str) -> str:
    request_id = request_id_context.get()
    file_name = os.path.splitext(file_name)[0]
    date_prefix = datetime.date.today().isoformat()
    base = f"jobs/{date_prefix}/{file_name}/{datetime.datetime.now().strftime('%Y%m%d%H%M')}_{request_id}"
    return base


def get_legal_id(email: str | None) -> str:
    if not email:
        return ""
    email_split = email.split("@")
    if len(email_split) != 2:
        return ""
    legal_id = email_split[0]
    return legal_id.replace(".", "_")


def generate_processing_script(user_question: str) -> str:
    try:
        script_content = Agent(config).plan(user_question)
        post_process_script = re.sub(r"```(?:python)?\s*", "", script_content)
        return post_process_script
    except Exception as e:
        logger.error(f"Error generating processing script: {e}")
    return ""


def get_executor_payload(script_key: str, base_s3_key: str) -> dict:
    request_id = request_id_context.get()
    return ExecutorRequest(
        script_bucket=config.S3_BUCKET,
        script_key=script_key,
        base=base_s3_key,
        request_id=request_id,
    ).to_dict()


def request_executor(payload: dict, invocation_type: str = "RequestResponse") -> dict | None:
    logger.debug(f"Invoking executor with script: {payload}")
    resp = lambda_client.invoke(config.EXECUTOR_NAME, json.dumps(payload), invocation_type)

    return resp


router_mapping = {"/planner": async_planner_handler, "/v2": async_planner_handler, "/": planner_handler}
