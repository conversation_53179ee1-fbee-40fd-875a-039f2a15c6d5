import json
from pathlib import Path

from loguru import logger
from llm_proxy import create_simple_proxy

from base.config.settings import Config
from base.process.input_transform import convert_json_to_xml
from base.prompt.planner_prompt import planner_template
from base.tool import (
    Tool,
    CheckLinkTool,
    DataLoadTool,
    PreProcessTool,
    RevisionRegTool,
    WordUpdateTool,
    ConcurrentTool,
    StylingTool,
    PatchTool,
    HeadingCheckTool,
    AuthorCheckTool,
    CheckListTool,
    WordCountCheckTool,
)


class Agent:
    def __init__(self, config: Config):
        self.config = config

    def get_tools(self):
        tools: dict[str, type[Tool]] = {
            "RevisionRegTool": RevisionRegTool,
            # "QuotationCheckTool": QuotationCheckTool,
            "CheckLinkTool": CheckLinkTool,
            "CheckListTool": CheckListTool,
            # "LegislationCheckTool": LegislationCheckTool,
            "PreProcessTool": PreProcessTool,
            "DataLoadTool": DataLoadTool,
            # "AbbrTool": AbbrTool,
            # "MergeParagraphResultTool": MergeParagraphResultTool,
            "WordUpdateTool": WordUpdateTool,
            "StylingTool": StylingTool,
            "PatchTool": PatchTool,
            "ConcurrentTool": ConcurrentTool,
            "AuthorCheckTool": AuthorCheckTool,
            "HeadingCheckTool": HeadingCheckTool,
            "WordCountCheckTool": WordCountCheckTool,
        }
        return tools

    def load_input(self):
        data = Path("demo.json").read_text()
        data = json.loads(data)
        return convert_json_to_xml(data)

    def save2process(self, script):
        with open("/agent_script_integration_v1.py", "w+") as file:
            file.writelines(script)

    def get_prompt_template(self, xml_path: str):
        return planner_template.format(xml_path)

    def load_prompt(self, query):
        _planner_prompt = self.get_prompt_template(query)
        tools = self.get_tools()
        for tool in tools.values():
            _planner_prompt += tool.name
            _planner_prompt += tool.description
            if isinstance(tool.example, str):
                _planner_prompt += tool.example
            else:
                _planner_prompt += "```python\n" + tool.example.code_snippet + "\n```\n"

        logger.debug(f"Planner prompt: {_planner_prompt}")
        return _planner_prompt

    def plan(self, query) -> str:
        prompt = self.load_prompt(query)
        llm_model = self.config.AGENT_MODEL or self.config.LLM_MODEL
        logger.info(f"Agent request with model: {llm_model}")
        llm = create_simple_proxy(
            prompt=prompt,
            model=self.config.LLM_MODEL,
            temperature=self.config.LLM_MODEL_TEMPERATURE,
            tenant=self.config.LLM_TENANT,
            timeout=self.config.LLM_MODEL_TIMEOUT,
            max_tokens=self.config.LLM_MODEL_MAX_TOKENS,
        )
        python_script = llm.predict(
            stream=self.config.LLM_STREAM,
            tracing_info={"asset_id": self.config.ASSET_ID},
        )
        logger.debug(f"Agent generate python script: {python_script}")
        return python_script

    def replay_metrics(self):
        pass
