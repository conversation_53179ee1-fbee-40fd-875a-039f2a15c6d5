from base.error import AgentError

class ApplyRevisionInternalError(AgentError):
    """Exception raised for errors in the ApplyRevision process."""
    status_code = 500
    code = "A001"
    message = "Apply AI revision due to an internal error."

class ApplyRevisonResponseError(AgentError):
    """Exception raised for errors in the ApplyRevision process."""
    status_code = 400
    code = "A002"
    message = "Apply AI revision to document failed."
