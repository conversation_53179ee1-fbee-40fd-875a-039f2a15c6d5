from base.error import AgentError

class SolrClientError(AgentError):
    """Exception raised for errors in the Solr client."""
    status_code = 500
    code = "S001"
    message = "Solr client failed due to an internal error."

class DatabaseError(AgentError):
    """Exception raised for errors in the database."""
    status_code = 500
    code = "D001"
    message = "Solr client failed due to an internal error."
