from base.error import AgentError


class ConvertLambdaError(AgentError):
    """Exception raised for errors in the conversion process."""

    status_code = 500
    code = "C001"
    message = "Coversion failed due to an internal error."


class ConvertRevisionEmptyError(AgentError):
    """Exception raised for errors in the data loading process."""

    status_code = 400
    code = "C002"
    message = "No revision found for the given document."


class ConvertWithEmptyReturnError(AgentError):
    """Exception raised when the conversion returns an empty result."""

    status_code = 500
    code = "C003"
    message = "Conversion returned an empty result, please check the input document."