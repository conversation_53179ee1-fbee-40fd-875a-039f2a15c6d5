class AgentError(Exception):
    """Base class for all exceptions raised by the agent."""

    status_code: int = 500
    code: str = "0000"
    message = "Agent internal error."

    def __init__(self, message=None, status_code=None):
        self.message = message or self.message
        self.status_code = status_code or self.status_code

    def __str__(self):
        return f"{self.message} (error code: {self.code})"


class NoAIModifiedRevisionError(AgentError):
    """Exception raised for empty revision errors."""

    status_code = 400
    code = "E001"
    message = "AI modified revision is empty."


class NoAIProcessParagraphError(AgentError):
    """Exception raised for empty paragraph errors."""

    status_code = 400
    code = "E002"
    message = "Error occurs when AI process paragraph using concurrent tool."


class LegislationCheckError(AgentError):
    """Exception raised for legislation check errors."""

    def __init__(self, message=None, status_code=None):
        super().__init__(message=message or "Legislation check failed.", status_code=status_code or 400)
        self.code = "E002"
