from typing import Annotated, Iterable, Literal

from pydantic import BaseModel, BeforeValidator, ConfigDict, Field


def str_to_bool(v):
    if isinstance(v, str):
        if v == "":
            return False
        if v.lower() == "true":
            return True
        elif v.lower() == "false":
            return False
    elif isinstance(v, bool):
        return v
    return bool(v)  # Fallback to bool conversion for other types


LenientBool = Annotated[bool, BeforeValidator(str_to_bool)]
ElementId = Annotated[str, Field(alias="ElementId")]
SegmentId = Annotated[str, Field(alias="SegmentId")]
HyperlinkId = Annotated[str, Field(alias="HyperlinkId")]
CommentId = Annotated[str, Field(alias="CommentId")]
RevisionId = Annotated[str, Field(alias="RevisionId")]


class SegmentProperties(BaseModel):
    """
    Properties for a text segment.
    """

    color: str | None = Field(default=None, alias="Color")
    font_size: str | None = Field(default=None, alias="FontSize")
    font_family: str | None = Field(default=None, alias="FontFamily")
    bold: LenientBool = Field(default=False, alias="Bold")
    italic: LenientBool = Field(default=False, alias="Italic")
    underline: LenientBool = Field(default=False, alias="Underline")
    model_config = ConfigDict(serialize_by_alias=True)


class Segment(BaseModel):
    segment_id: SegmentId = Field(alias="SegmentId")
    segment_text: str = Field(alias="SegmentText")
    start: int = Field(alias="Start")
    end: int = Field(alias="End")
    properties: SegmentProperties = Field(alias="Properties")
    field_id: str = Field(alias="FieldId")
    hyperlink_id: HyperlinkId = Field(alias="HyperlinkId")
    content_revision_id: str = Field(alias="ContentRevisionId")
    prop_revisions: list[dict] = Field(alias="PropRevisions")
    comment_ids: list[CommentId] = Field(alias="CommentIds")
    model_config = ConfigDict(serialize_by_alias=True)


class ListInfo(BaseModel):
    """Information about a list item in a document."""

    num_id: int = Field(alias="NumId")
    level: int = Field(alias="Level")
    list_type: str = Field(alias="ListType")
    indent: int = Field(alias="Indent")
    level_text: str = Field(alias="LevelText")
    model_config = ConfigDict(serialize_by_alias=True)


class ElementProperties(BaseModel):
    """
    Properties for an element.
    """

    style: str | None = Field(default=None, alias="Style")
    indent_left: int | None = Field(default=None, alias="IndentLeft")
    list_info: ListInfo | None = Field(default=None, alias="ListInfo")
    model_config = ConfigDict(serialize_by_alias=True)


class BaseDocumentElement(BaseModel):
    key: str = Field(alias="Key")
    element_id: ElementId = Field(alias="ElementId")
    element_type: Literal["Paragraph", "List", "Table"] = Field(alias="ElementType")
    model_config = ConfigDict(serialize_by_alias=True)


class BlockElement(BaseDocumentElement):
    element_type: Literal["Paragraph", "List"] = Field(alias="ElementType")  # type: ignore[assignment]
    plain_text: str = Field(alias="PlainText")
    properties: ElementProperties = Field(alias="Properties")
    segments: list[Segment] = Field(alias="Segments")
    model_config = ConfigDict(serialize_by_alias=True)


class NoKeyBlockElement(BlockElement):
    key: str = ""


class TableCell(BaseModel):
    """Represents a cell within a table row."""

    cell_id: int = Field(alias="CellId")
    elements: list[NoKeyBlockElement] = Field(alias="Elements")
    model_config = ConfigDict(serialize_by_alias=True)


class TableRow(BaseModel):
    """Represents a row within a table."""

    row_id: int = Field(alias="RowId")
    cells: list[TableCell] = Field(alias="Cells")
    model_config = ConfigDict(serialize_by_alias=True)


class Comment(BaseModel):
    """Represents a comment in the document."""

    comment_id: CommentId = Field(alias="CommentId")
    author: str = Field(alias="Author")
    date: str = Field(alias="Date")
    text: str = Field(alias="Text")
    targets: list[SegmentId] = Field(alias="Targets")
    model_config = ConfigDict(serialize_by_alias=True)


class Hyperlink(BaseModel):
    """Represents a hyperlink in the document."""

    hyperlink_id: HyperlinkId = Field(alias="HyperlinkId")
    uri: str = Field(alias="Uri")
    targets: list[SegmentId] = Field(alias="Targets")
    model_config = ConfigDict(serialize_by_alias=True)


Comments = Annotated[dict[CommentId, Comment], Field(alias="Comments")]
Hyperlinks = Annotated[dict[HyperlinkId, Hyperlink], Field(alias="Hyperlinks")]


class Annotations(BaseModel):
    """Represents annotations including comments and hyperlinks in the document."""

    comments: Comments = Field(alias="Comments")
    hyperlinks: Hyperlinks = Field(alias="Hyperlinks")
    model_config = ConfigDict(serialize_by_alias=True)


class TableElement(BaseDocumentElement):
    """Represents a table element."""

    element_type: Literal["Table"] = Field(alias="ElementType")  # type: ignore[assignment]
    rows: list[TableRow] = Field(alias="Rows")
    model_config = ConfigDict(serialize_by_alias=True)


class NativeRevision(BaseModel):
    """Represents a native revision."""

    key: str = Field(alias="Key")
    rev_id: RevisionId = Field(alias="RevId")
    rev_type: str = Field(alias="RevType")
    author: str = Field(alias="Author")
    date: str = Field(alias="Date")
    model_config = ConfigDict(serialize_by_alias=True)


Element = Annotated[BlockElement | TableElement, Field(discriminator="element_type")]
NativeRevisions = Annotated[dict[RevisionId, NativeRevision], Field(alias="NativeRevisions")]


class DocumentInfo(BaseModel):
    version: str = Field(alias="Version")
    document_id: str = Field(alias="DocumentId")
    elements: list[Element] = Field(alias="Elements")
    annotations: Annotations = Field(alias="Annotations")
    native_revisions: NativeRevisions = Field(alias="NativeRevisions")

    model_config = ConfigDict(serialize_by_alias=True)

    def iter_paragraph(self) -> Iterable[BlockElement]:
        for element in self.elements:
            if isinstance(element, BlockElement):
                yield element
            elif isinstance(element, TableElement):
                for row in element.rows:
                    for cell in row.cells:
                        for element in cell.elements:
                            yield element

    @property
    def fulltext(self) -> str:
        full_text = []
        for para in self.iter_paragraph():
            full_text.append(para.plain_text)
        return "\n".join(full_text)
