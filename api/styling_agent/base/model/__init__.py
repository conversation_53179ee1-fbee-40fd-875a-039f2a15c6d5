from typing import Literal

from pydantic import BaseModel, Field


class ParaState(BaseModel):
    para_id: str
    para_type: Literal["paragraph", "list", "table"]
    para_content: str = ""
    modified_para_content: str = ""
    commented_para_content: str = ""
    run_style_text: str = ""
    par_style_text: str = ""
    links: list[dict[str, str]] = Field(default_factory=list)
    link_revisions: dict[str, str] = Field(default_factory=dict)
    patches: list[dict] = Field(default_factory=list)
    enabled_tools: list[str] = Field(default_factory=list)

    def is_list(self) -> bool:
        return self.para_type == "list"

    def is_paragraph(self) -> bool:
        return self.para_type == "paragraph"

    def is_heading_enabled(self) -> bool:
        return "heading" in self.enabled_tools

    def is_quotation_enabled(self) -> bool:
        return "quotation" in self.enabled_tools

    def is_abbr_enabled(self) -> bool:
        return "abbr" in self.enabled_tools

    def is_capital_enabled(self) -> bool:
        return "capital" in self.enabled_tools

    def is_grammar_enabled(self) -> bool:
        return "grammar" in self.enabled_tools

    def is_spelling_enabled(self) -> bool:
        return "spelling" in self.enabled_tools

    def is_emphasis_enabled(self) -> bool:
        return "emphasis" in self.enabled_tools

    def is_leg_ac_enabled(self) -> bool:
        return "leg_ac" in self.enabled_tools

    def is_link_enabled(self) -> bool:
        return "link" in self.enabled_tools

    def is_num_enabled(self) -> bool:
        return "num" in self.enabled_tools


class ParaResult(BaseModel):
    para_id: str
    para_content: str
    modified_para_content: str
    commented_para_content: str
    run_style_text: str
    patches: list[dict]
