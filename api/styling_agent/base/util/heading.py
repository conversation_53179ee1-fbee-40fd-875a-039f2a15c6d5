import re


class ParaPropertyCheker:
    @staticmethod
    def check_heading(par_style: str, para_content: str) -> dict:
        pattern = r"<pStyle>(.*?)</pStyle>"
        matches = re.findall(pattern, par_style)
        para_res = {
            "para_content": para_content,
            "comment_content": "",
        }
        comment_template = "{context}[{comment_idx}][Capitalize Tool]"
        if not matches:
            return para_res
        modified = False
        for match in matches:
            if match.lower().startswith("heading"):
                # heading should be Capitalized
                if para_content and para_content[0].islower():
                    para_content = para_content.capitalize()
                    modified = True

                # heading should not have a period at the end
                if para_content.endswith(".") or para_content.endswith("。"):
                    # Remove the period at the end of the paragraph content
                    para_content = para_content[:-1]
                    modified = True
                para_res["para_content"] = para_content
                if modified:
                    para_res["comment_content"] = comment_template.format(
                        context=para_content, comment_idx=1
                    )
        return para_res


para_checker = ParaPropertyCheker()
