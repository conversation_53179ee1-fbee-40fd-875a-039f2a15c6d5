from dataclasses import dataclass
from typing import Any, Dict, Iterator, List, Optional, Sequence, Tuple

from base.model.document import BlockElement, DocumentInfo, Element, NoKeyBlockElement
from base.model.document import Segment as SegmentModel


@dataclass
class Segment:
    seg_id: str
    rev_id: Optional[str]
    kind: str  # "INS" | "BASE"
    start: int
    end: int
    # ↓ 仅当 include_table_info=True 才写入
    table: Optional[str] = None
    row: Optional[int] = None
    col: Optional[int] = None


def _iter_paragraphs(
    elements: Sequence[Element],
    ctx: Tuple[Optional[str], Optional[int], Optional[int]] = (None, None, None),
) -> Iterator[
    tuple[BlockElement | NoKeyBlockElement, Optional[str], Optional[int], Optional[int]]
]:
    """深度优先遍历，返回 (paragraph_dict, table_id, row, col)"""
    tbl_id, row_idx, col_idx = ctx
    for el in elements:
        if el.element_type == "Paragraph" or el.element_type == "List":
            yield el, tbl_id, row_idx, col_idx

        elif el.element_type == "Table":
            tid = el.element_id
            for r, row in enumerate(el.rows):
                for c, cell in enumerate(row.cells):
                    yield from _iter_paragraphs(cell.elements, (tid, r, c))

        # ...


def _seg_revision(seg: SegmentModel) -> Tuple[Optional[str], str]:
    """返回 (rev_id, kind)；kind ∈ {INS, BASE}"""
    content_rev = {"revId": seg.content_revision_id}
    rev_id = content_rev.get("revId") or None
    if isinstance(rev_id, str) and not rev_id.strip():
        rev_id = None
    kind = "INS" if content_rev and content_rev.get("type") == "ins" else "BASE"
    return rev_id, kind


def build_segments(
    doc: DocumentInfo, *, include_table_info: bool = True
) -> Dict[str, Dict[str, Any]]:
    """
    解析整份 JSON 文档，返回:
        {para_id: {"text": str, "segments": List[Segment]}}
    """
    result: Dict[str, Dict[str, Any]] = {}

    for para, tbl, row, col in _iter_paragraphs(doc.elements):
        pid = para.element_id
        seg_objs, parts, cursor = [], [], 0
        for s in para.segments:
            txt = s.segment_text
            if not txt:
                continue

            rev_id, kind = _seg_revision(s)

            seg = Segment(
                seg_id=s.segment_id,
                rev_id=rev_id,
                kind=kind,
                start=cursor,
                end=cursor + len(txt),
            )

            if include_table_info:
                seg.table, seg.row, seg.col = tbl, row, col

            seg_objs.append(seg)
            parts.append(txt)
            cursor += len(txt)

        # merged: List[Segment] = list(seg_objs)
        # for sg in seg_objs:
        #     if (merged and merged[-1].rev_id is None and sg.rev_id is None
        #             and merged[-1].kind == sg.kind == "BASE"
        #             and merged[-1].end == sg.start):
        #         merged[-1].end = sg.end
        #     else:
        #         merged.append(sg)

        # result[pid] = {"text": "".join(parts), "segments": merged}
        result[pid] = {"text": "".join(parts), "segments": seg_objs}

    return result


@dataclass
class Segment2:
    seg_id: str
    start: int  # 段落内字符起
    end: int  # 段落内字符终 (半开)


def build_para_map_lean(doc: DocumentInfo) -> dict[str, dict]:
    full = build_segments(doc, include_table_info=False)

    for meta in full.values():
        meta["segments"] = [
            Segment2(s.seg_id, s.start, s.end) for s in meta["segments"]
        ]
    return full


if __name__ == "__main__":
    sample = {
        "Elements": [
            {
                "ElementType": "Paragraph",
                "ElementId": "P0",
                "Segments": [{"SegmentId": "P0_0", "SegmentText": "Hello"}],
            },
            {
                "ElementType": "Table",
                "ElementId": "T1",
                "Rows": [
                    {
                        "Cells": [
                            {
                                "Elements": [
                                    {
                                        "ElementType": "Paragraph",
                                        "ElementId": "P1",
                                        "Segments": [
                                            {"SegmentId": "P1_0", "SegmentText": "C00"}
                                        ],
                                    }
                                ]
                            },
                            {
                                "Elements": [
                                    {
                                        "ElementType": "Paragraph",
                                        "ElementId": "P2",
                                        "Segments": [
                                            {
                                                "SegmentId": "P2_0",
                                                "SegmentText": "C01",
                                                "ContentRevisionId": "rev",
                                            }
                                        ],
                                    }
                                ]
                            },
                        ]
                    }
                ],
            },
        ]
    }

    mapping = build_segments(sample)
    assert mapping["P0"]["text"] == "Hello"
    assert mapping["P1"]["segments"][0].table == "T1"
    assert mapping["P1"]["segments"][0].row == 0
    assert mapping["P2"]["segments"][0].rev_id == "rev"
    print("Self-test passed.")
