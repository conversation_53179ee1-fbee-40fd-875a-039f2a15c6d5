import re

def remove_existing_style_labels(commented_text, original_styles):
    """
    Compare commented_text and original_styles. For each content in tag pairs in original_styles,
    if it is contained in commented_text, remove the "[number][Emphasis Check Tool]" labels.
    
    Args:
        commented_text (str): Text with style tags and tool labels
        original_styles (str): Text with existing style tags
        
    Returns:
        str: Modified commented_text with labels removed for pre-existing styled content
    """
    # Extract content within style tags from original_styles
    style_pattern = r'<([iub])>(.*?)</\1>'
    original_styled_content = re.findall(style_pattern, original_styles)
    
    # Create a set of content that was already styled
    existing_styled_content = set()
    for tag, content in original_styled_content:
        existing_styled_content.add(content.strip())
    
    # Pattern to match styled content with labels in commented_text
    label_pattern = r'<([iub])>(.*?)</\1>\[(\d+)\]\[([^\]]+)\]'
    
    def replace_if_existing(match):
        tag = match.group(1)
        content = match.group(2).strip()
        # label_num = match.group(3)
        # tool_name = match.group(4)
        
        # If this content was already styled in original_styles, remove the label
        if content in existing_styled_content:
            return f'<{tag}>{match.group(2)}</{tag}>'
        else:
            # Keep the label for new styling
            return match.group(0)
    
    # Apply the replacement
    modified_text = re.sub(label_pattern, replace_if_existing, commented_text)
    
    return modified_text