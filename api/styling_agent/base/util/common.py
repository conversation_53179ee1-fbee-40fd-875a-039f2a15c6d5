import functools
import json
import os
import time
import traceback
from typing import TYPE_CHECKING, Any

import yaml
from loguru import logger
from pydantic import BaseModel

from base.client.s3 import s3_client
from base.config.settings import config
from base.util.shared import SharedTime
from base.context_manager import get_context

if TYPE_CHECKING:
    from base.tool import Tool

# Global varibale to store file path
_local_json_path = None


def method_log(func):
    """decorator for method logging"""

    @functools.wraps(func)
    def wrapper(*args, **kwargs):
        # Test if the first argument is an instance of the class
        if len(args) > 0 and hasattr(args[0], "__class__"):
            self = args[0]
            class_name = self.__class__.__name__
            method_name = func.__name__
            log_prefix = f"[{class_name}] calling method [{method_name}]"
            log_args = args[1:]  # Skip the first argument (self)
        else:
            method_name = func.__name__
            log_prefix = f"Calling method [{method_name}]"
            log_args = args  # Non skipping
        logger.debug(f"{log_prefix} with args->{log_args}, kwargs->{kwargs}")
        start_time = time.time()
        try:
            result = func(*args, **kwargs)
            end_time = time.time()
            elapsed_time = end_time - start_time
            logger.debug(f"{log_prefix} ({elapsed_time:.4f} seconds) with return-> {result}")
            if SharedTime.get(class_name) is None:
                SharedTime[class_name] = {"Calls": 0, "TotalTime": 0}
            SharedTime[class_name]["Calls"] += 1
            SharedTime[class_name]["TotalTime"] += elapsed_time
        except Exception as e:
            logger.error(f"{log_prefix} with error-> {e}")
            raise
        else:
            return result

    return wrapper


def wrap_run(cls):
    """Wrap the run() method of a Tool class to log input and output to S3."""
    original_run = cls.run

    @functools.wraps(original_run)
    def wrapper(self: "Tool", *args, **kwargs):
        base = os.environ["JOB_PREFIX"]
        para_id = self.parameters.get("para_id")
        if para_id:
            base = f"{base}/{para_id}"

        parameters_clone = self.parameters.copy()
        # NOTE: Remove xml_content from parameters for logging, because it can be large
        parameters_clone.pop("xml_content", None)

        # —— before log —— #
        # HACK: if the name is ConcurrentTool, we don't upload input parameters
        if self.name != "ConcurrentTool":
            ctx = get_context()
            _data = {"parameters": parameters_clone, "context": {}}
            if "para_id" in parameters_clone:
                para_state = ctx.get_para_state(parameters_clone["para_id"])
                _data["context"]["para_state"] = para_state.model_dump() if para_state else None
            else:
                _data["context"] = ctx.dict()
            _upload_log(f"{base}/{self.name}_input.json", _data)

        try:
            output = original_run(self, *args, **kwargs)
        except Exception as e:
            tb = traceback.format_exc()
            self.logger.error(f"{self.name}: raised error:\n{tb}")
            _upload_log(f"{base}/{self.name}_error.json", {"error": str(e)})
            raise
        else:
            # —— after log —— #
            self.logger.debug(f"{self.name}: finished, output: {output!r}")
            _upload_log(f"{base}/{self.name}_output.json", output)
            return output
        finally:
            llm_out = getattr(self, "_last_llm_output", None)
            if llm_out is not None:
                _upload_log(f"{base}/{self.name}_llm_predict.yml", llm_out)

    return wrapper


def _recursive_serialize_to_json(obj: Any) -> str:
    """
    Recursively serialize an object to JSON string format.

    Args:
        obj: The object to serialize

    Returns:
        JSON string representation of the object
    """

    def _process_object(item: Any) -> Any:
        """Recursively process objects for JSON serialization"""
        if isinstance(item, BaseModel):
            # Convert BaseModel to dict for further processing
            return _process_object(item.model_dump())
        elif isinstance(item, dict):
            # Recursively process dictionary values
            return {key: _process_object(value) for key, value in item.items()}
        elif isinstance(item, (list, tuple)):
            # Recursively process list/tuple items
            return [_process_object(item) for item in item]
        elif isinstance(item, set):
            # Convert set to list and process
            return [_process_object(item) for item in item]
        else:
            # Return primitive types as-is
            return item

    # Process the object recursively
    processed_obj = _process_object(obj)

    # Handle BaseModel objects specially
    if isinstance(obj, BaseModel):
        return obj.model_dump_json(indent=2)
    else:
        return json.dumps(processed_obj, indent=2, ensure_ascii=False)


def _recursive_serialize_to_yaml(obj: Any) -> str:
    """
    Recursively serialize an object to YAML string format.

    Args:
        obj: The object to serialize

    Returns:
        YAML string representation of the object
    """

    def _process_object(item: Any) -> Any:
        """Recursively process objects for YAML serialization"""
        if isinstance(item, BaseModel):
            # Convert BaseModel to dict for further processing
            return _process_object(item.model_dump())
        elif isinstance(item, dict):
            # Recursively process dictionary values
            return {key: _process_object(value) for key, value in item.items()}
        elif isinstance(item, (list, tuple)):
            # Recursively process list/tuple items
            return [_process_object(item) for item in item]
        elif isinstance(item, set):
            # Convert set to list and process
            return [_process_object(item) for item in item]
        else:
            # Return primitive types as-is
            return item

    # If already a string, return as-is
    if isinstance(obj, str):
        return obj

    # Process the object recursively
    processed_obj = _process_object(obj)

    return yaml.safe_dump(processed_obj, allow_unicode=True)


def _upload_log(key: str, obj: Any):
    ext = os.path.splitext(key)[1].lower()

    if ext in (".yml", ".yaml"):
        body_str = _recursive_serialize_to_yaml(obj)
    else:
        body_str = _recursive_serialize_to_json(obj)

    if os.getenv("JOB_PREFIX", "").startswith("local-run"):
        # Extract folder structure from obj["path"] if available
        folder_path = ""
        global _local_json_path

        if isinstance(obj, dict) and "parameters" in obj and "path" in obj["parameters"]:
            s3_path = obj["parameters"]["path"]
            # Extract path parts from s3://bucket/jobs/date/job_name/timestamp/filename
            if s3_path.startswith("s3://") and "/jobs/" in s3_path:
                path_parts = s3_path.split("/")
                if len(path_parts) >= 7:  # s3, bucket, jobs, date, job_name, timestamp, filename
                    job_name = path_parts[5]  # dog heading
                    timestamp = path_parts[6]  # local-run_202506231520
                    folder_path = os.path.join("local_data", job_name, timestamp)
            if not _local_json_path:
                _local_json_path = folder_path

        cur_path = "local-run" if not _local_json_path else _local_json_path

        local_path = os.path.join(
            os.path.dirname(__file__),
            "..",
            "..",
            "script_data",
            "data",
            cur_path,
            key,
        )
        os.makedirs(os.path.dirname(local_path), exist_ok=True)
        with open(local_path, "w", encoding="utf-8") as f:
            f.write(body_str)
        return

    s3_client.put_object(config.S3_BUCKET, key, body_str.encode("utf-8"))
    pass
