import json
import re
from importlib.metadata import PackageNotFoundError, version


def get_package_version() -> str:
    try:
        return version("base")
    except PackageNotFoundError:
        return "unknown"


def get_response(status_code: int, body: dict):
    return {
        "statusCode": status_code,
        "body": json.dumps(body),
        "headers": {
            "Content-Type": "application/json",
            "Access-Control-Allow-Headers": "Content-Type",
            "Access-Control-Allow-Origin": "*",
            "Access-Control-Allow-Methods": "OPTIONS,POST,GET",
        },
    }


def check_email(email: str) -> bool:
    """
    Check if the email is valid.
    :param email: The email to check.
    :return: True if the email is valid, otherwise False.
    """
    email_regex = r"^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$"
    return re.match(email_regex, email)
