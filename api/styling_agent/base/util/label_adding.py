import difflib
import re
from copy import deepcopy
from datetime import datetime, timezone
from typing import Any, Dict, List, Tuple

from base.util.constants import COMMNET_SYMBOLS
from base.util.convert import build_paragraph_level_spans, type_map
from base.util.revision_meta_helper import find_changed_segments
from loguru import logger
from merge3 import Merge3


def compare_texts_enhanced(text1, text2, check_tool):
    """
    An enhanced text comparison function that combines character-level precision
    with word-level intelligence to produce optimal difference formatting.

    Formats:
    - Deletions: {delete tool|delete|text}
    - Additions: {add tool|add|text}

    The function first tries word-level comparison for better contextual understanding,
    then falls back to character-level comparison within changed words for precision.
    Any replacements are represented as a deletion followed by an addition.
    """
    if not text1 and not text2:
        return ""
    if not text1:
        return f"{{{check_tool}|add|{text2}}}"
    if not text2:
        return f"{{{check_tool}|delete|{text1}}}"

    words1 = text1.split()
    words2 = text2.split()

    word_matcher = difflib.SequenceMatcher(None, words1, words2)

    result = []
    for op, i1, i2, j1, j2 in word_matcher.get_opcodes():
        if op == "equal":
            if result and result[-1] and not result[-1].endswith(" ") and not result[-1].endswith("}"):
                result.append(" ")
            result.append(" ".join(words1[i1:i2]))
        elif op == "replace":
            old_text = " ".join(words1[i1:i2])
            new_text = " ".join(words2[j1:j2])

            if result and not result[-1].endswith(" ") and not result[-1].endswith("}"):
                result.append(" ")

            char_result = compare_at_char_level(old_text, new_text, check_tool)
            result.append(char_result)
        elif op == "delete":
            if result and not result[-1].endswith(" ") and not result[-1].endswith("}"):
                result.append(" ")
            result.append(f"{{{check_tool}|delete|{' '.join(words1[i1:i2])}}}")
        elif op == "insert":
            if result and not result[-1].endswith(" ") and not result[-1].endswith("}"):
                result.append(" ")
            result.append(f"{{{check_tool}|add|{' '.join(words2[j1:j2])}}}")

    result_str = "".join(result).strip()

    result_str = result_str.replace(" }", "}")
    result_str = result_str.replace("{ ", "{")
    result_str = result_str.replace("  ", " ")

    return result_str


def compare_at_char_level(text1, text2, check_tool):
    """
    Compare two texts at character level and return formatted differences.
    Used for more precise comparison within replaced word sections.
    All replacements are converted to deletion+addition pairs.
    """
    matcher = difflib.SequenceMatcher(None, text1, text2)

    result = []
    for op, i1, i2, j1, j2 in matcher.get_opcodes():
        if op == "equal":
            result.append(text1[i1:i2])
        elif op == "delete":
            result.append(f"{{{check_tool}|delete|{text1[i1:i2]}}}")
        elif op == "insert":
            result.append(f"{{{check_tool}|add|{text2[j1:j2]}}}")
        elif op == "replace":
            deleted = text1[i1:i2]
            added = text2[j1:j2]
            result.append(f"{{{check_tool}|delete|{deleted}}}")
            result.append(f"{{{check_tool}|add|{added}}}")

    return "".join(result)


def classify_by_opcodes(opcodes, old_len):
    """
    Classify edit type based on SequenceMatcher opcodes.
    Returns one of: 'prefix', 'suffix', 'wrap', 'inplace'.
    """
    has_del_or_replace = False
    has_head = has_tail = has_middle = False

    for tag, i1, _, _, _ in opcodes:
        if tag in ("delete", "replace"):
            has_del_or_replace = True
        elif tag == "insert":
            if i1 == 0:
                has_head = True
            elif i1 == old_len:
                has_tail = True
            else:
                has_middle = True

    if old_len == 0:
        if has_head or has_tail or has_middle:
            return "suffix"
        return "inplace"

    if has_del_or_replace or has_middle:
        return "inplace"
    if has_head and has_tail:
        return "wrap"
    if has_head:
        return "prefix"
    if has_tail:
        return "suffix"
    return "inplace"  # should not happen


def _get_revision_by_id(para_id: str, rev_id: str, xml_content: list[dict]):
    # Placeholder for actual implementation to retrieve revision by ID
    # This should be replaced with the actual logic to fetch the revision data
    para_id = str(para_id)  # Ensure para_id is a string
    for item in xml_content:
        if item["ParaId"] != para_id:
            continue
        for r in item["Runs"]:
            if r.get("Id") == rev_id:
                return r
    return {"Author": "unknown", "Date": datetime.now(timezone.utc).strftime("%Y-%m-%dT%H:%M:%SZ")}


_TOKEN_RE = re.compile(r"\s+|[A-Za-z0-9]+|[^\w\s]")


def _tokenize(s: str) -> List[str]:
    return _TOKEN_RE.findall(s)


def diff_sentinels(text1: str, text2: str, spans, param: str) -> List[Tuple[int, str, str, str]]:
    """
    按照 “token 级 diff + 字符级 diff” 流程，比较两个文本里相同 RevId 段落的差异，
    返回 [(文本片段, 'del'/'ins', param), …]
    """
    diffs: List[Tuple[int, str, str, str]] = []
    if not text1 or not text2:
        return diffs

    changed_segments = find_changed_segments(text1, text2, spans)
    for rev_id, changed_dict in changed_segments.items():
        old_seg = changed_dict["old_seg"]
        new_seg = changed_dict["new_seg"]

        if old_seg == new_seg:
            continue

        # —— 第一阶段：token 级别 diff，定位变化区块 ——
        old_toks = _tokenize(old_seg)
        new_toks = _tokenize(new_seg)
        sm_tok = difflib.SequenceMatcher(lambda t: t.isspace(), old_toks, new_toks, autojunk=False)

        for tag, i1, i2, j1, j2 in sm_tok.get_opcodes():
            # 只有发生变动的区块才需要下游字符级 diff
            if tag == "equal":
                continue

            sub_old = "".join(old_toks[i1:i2])
            sub_new = "".join(new_toks[j1:j2])

            # —— 第二阶段：字符级 diff ——
            sm_char = difflib.SequenceMatcher(lambda ch: ch.isspace(), sub_old, sub_new, autojunk=False)
            for ctag, ci1, ci2, cj1, cj2 in sm_char.get_opcodes():
                if ctag in ("delete", "replace") and ci1 != ci2:
                    diffs.append((rev_id, sub_old[ci1:ci2], "del", param))
                if ctag in ("insert", "replace") and cj1 != cj2:
                    diffs.append((rev_id, sub_new[cj1:cj2], "ins", param))

    return diffs


comment_templates = {
    "legislation": "Legislation update.",
    "quotation": "Quotation update.",
    "abbr": "Abbreviation update.",
    "link": "External Link update",  # check_link temporary
    "unknown_tool": "AI processing update.",  # Handle unidentified tool changes
}


def compare_texts_enhanced_v2(
    text1: str,
    text2: str,
    check_tool: str,
    word_json: list[dict],
    para_id: str,
    suggestion_lookup: dict[tuple[str, str, str], list[str]],
) -> list[dict]:
    """
    对比两个文本，返回一组 patch dict：
      - ParaId、RevId、Kind 沿用word
      - Ops 是 difflib 生成的最小增删列表
      - Author/Comment 标记为 check_tool
    """
    paragraph_rev_maps = build_paragraph_level_spans(word_json)
    logger.info(f"paragraph_rev_maps: {paragraph_rev_maps}")
    rev_map = paragraph_rev_maps.get(str(para_id), {})
    logger.info(f"rev_map: {rev_map}")
    spans = rev_map.get("spans", [])
    logger.info(f"spans: {spans}")

    PAIRS = {"(": ")", "[": "]", "{": "}", "“": "”", "‘": "’", "«": "»"}
    OPEN = set(PAIRS.keys())
    CLOSE = set(PAIRS.values())
    today_utc = datetime.now(timezone.utc).strftime("%Y-%m-%dT%H:%M:%SZ")

    # ────────── 批注标记辅助 ──────────
    def _mark_comment_boundaries(ops, *, check_tool="styling_agent"):
        """
        给 ops[*] 标记批注边界：
        1. 同一条 op 内既有开又有闭 → 自闭合
        2. 单字符 '('      → CommentStart
            单字符 ')' 匹配 → CommentEnd
            单字符 ')' 不匹配 → 自闭合
        3. 其余 AI-op：
            ► 若当前位于括号批注区段内 → 不打标记
            ► 否则               → 自闭合
        """
        ai_idxs = [i for i, o in enumerate(ops) if o.get("Author") == check_tool]
        if not ai_idxs:
            return ops

        # 清掉任何旧标记
        for o in ops:
            o.pop("CommentStart", None)
            o.pop("CommentEnd", None)

        # 单条 op 同时含开 & 闭括号 → 自闭合
        for i in ai_idxs:
            txt = ops[i]["Text"]
            if any(ch in OPEN for ch in txt) and any(ch in CLOSE for ch in txt):
                ops[i]["CommentStart"] = ops[i]["CommentEnd"] = True

        # 顺序扫描其余 AI-op
        stack = []  # 记录未闭合开括号 [(idx, expected_close)]
        for i in ai_idxs:
            # 已自闭合的不再处理
            if "CommentStart" in ops[i] and "CommentEnd" in ops[i]:
                continue

            t = ops[i]["Text"]

            # --- 2-a  单字符开括号 ---
            if len(t) == 1 and t in OPEN:
                ops[i]["CommentStart"] = True
                stack.append((i, PAIRS[t]))
                continue

            # --- 2-b  单字符闭括号 ---
            if len(t) == 1 and t in CLOSE:
                if stack and t == stack[-1][1]:  # 正常配对
                    ops[i]["CommentEnd"] = True
                    stack.pop()
                else:  # 孤立闭括号
                    ops[i]["CommentStart"] = ops[i]["CommentEnd"] = True
                continue

            # --- 2-c  其它 AI-op ---
            if not stack:  # 不在任何括号批注内
                ops[i]["CommentStart"] = ops[i]["CommentEnd"] = True
            # 若 stack 非空：处于未闭合括号区段 → 不打标记

        # 仍未配对的开括号补 CommentEnd
        for i, _ in stack:
            ops[i]["CommentEnd"] = True

        return ops

    TOKEN_RE = re.compile(r"\s+|[A-Za-z0-9]+|[^\w\s]")  # 空白 / 单词 / 单个标点

    def tokenize(s: str):
        return TOKEN_RE.findall(s)

    def build_ops_and_type(old: str, new: str, rev_id: str):
        revision = _get_revision_by_id(para_id, rev_id, word_json)
        a = tokenize(old)
        b = tokenize(new)

        sm = difflib.SequenceMatcher(lambda t: t.isspace(), a, b, autojunk=False)
        opcodes = sm.get_opcodes()
        ops = []

        for _, i1, i2, j1, j2 in opcodes:
            # 对每个 changed-token 块再跑字符级 diff
            old_seg = "".join(a[i1:i2])
            new_seg = "".join(b[j1:j2])

            sm_char = difflib.SequenceMatcher(lambda ch: ch.isspace(), old_seg, new_seg, autojunk=False)
            for ctag, ci1, ci2, cj1, cj2 in sm_char.get_opcodes():
                if ctag in ("replace", "delete") and ci1 != ci2:
                    del_suggestion_lookup = suggestion_lookup[(rev_id, old_seg[ci1:ci2], "del")]
                    if not del_suggestion_lookup:
                        logger.info(f"del_suggestion_lookup is empty for {rev_id}, {old_seg[ci1:ci2]}")
                        continue
                    tool_name = del_suggestion_lookup.pop(0)  # collections.deque + popleft()
                    ops.append(
                        {
                            "Op": "del",
                            "Text": old_seg[ci1:ci2],
                            "Author": check_tool,
                            "Date": today_utc,
                            "Comment": comment_templates.get(tool_name, f"AI update by {tool_name}"),
                        }
                    )
                if ctag in ("replace", "insert") and cj1 != cj2:
                    ins_suggestion_lookup = suggestion_lookup[(rev_id, new_seg[cj1:cj2], "ins")]
                    if not ins_suggestion_lookup:
                        logger.info(f"ins_suggestion_lookup is empty for {rev_id}, {new_seg[cj1:cj2]}")
                        continue
                    tool_name = ins_suggestion_lookup.pop(0)
                    # Chekc if new_seg move to comment
                    ops_text = new_seg[cj1:cj2]
                    ops_comment = comment_templates.get(tool_name, f"AI update by {tool_name}")
                    for symbol in COMMNET_SYMBOLS:
                        if ops_text.find(symbol) != -1:
                            ops_comment = f"{ops_comment} {symbol}"
                            ops_text = ""
                            break

                    ops.append(
                        {
                            "Op": "ins",
                            "Text": ops_text,
                            "Author": check_tool,
                            "Date": today_utc,
                            "Comment": ops_comment,
                        }
                    )
                elif ctag == "equal" and cj1 != cj2:
                    ops.append(
                        {
                            "Op": type_map.get(revision["Type"]),
                            "Text": new_seg[cj1:cj2],
                            "Author": revision["Author"],
                            "Date": revision["Date"],
                            "Comment": revision.get("Comment", ""),
                        }
                    )

        edit_type = classify_by_opcodes(opcodes, len(a))
        ai_idxs = [i for i, o in enumerate(ops) if o["Author"] == check_tool]
        if not ai_idxs:
            return ops, edit_type, revision

        ops = _mark_comment_boundaries(ops)
        return ops, edit_type, revision

    patches = []
    # patch for modified text2 base on style properties
    text2 = process_title(text2, word_json, para_id)
    changed_segments = find_changed_segments(text1, text2, spans)
    for rev_id, changed_dict in changed_segments.items():
        old_seg = changed_dict["old_seg"]
        new_seg = changed_dict["new_seg"]

        if old_seg != new_seg:
            ops, edit_type, revision = build_ops_and_type(old_seg, new_seg, rev_id)
            patch = {
                "RevId": rev_id,
                "EditType": edit_type,
                "Ops": ops,
            }

            patches.append(split_ops_by_links(patch, revision))

    return patches


def split_ops_by_links(patch: Dict[str, Any], revision: Dict) -> Dict[str, Any]:
    """
    convert patch["Ops"] to a new list of ops, where each op is split
    according to the display texts in patch["Links"].
    Args:
        patch: A dictionary containing "Ops" (list[dict]) and optional "Links" (list[dict]).
                 Each op dict must have at least "Op", "Author", and "Text" fields.
    Returns:
        The modified patch object, where patch["Ops"] has been split and rewritten.
    """
    links: List[Dict[str, Any]] = revision.get("Links", [])
    display_texts = [link["DisplayText"] for link in links]

    if not display_texts:
        return patch

    pattern = "(" + "|".join(re.escape(d) for d in display_texts) + ")"
    splitter = re.compile(pattern)

    new_ops: List[Dict[str, Any]] = []
    for op in patch["Ops"]:
        is_ins = op.get("Op") == "ins"
        is_non_ai = op.get("Author") != "styling_agent"

        if is_ins and is_non_ai:
            original_text: str = op.get("Text", "")
            parts = splitter.split(original_text)

            for part in parts:
                if not part:
                    continue

                new_op = deepcopy(op)
                new_op["Text"] = part

                new_ops.append(new_op)
        else:
            new_ops.append(deepcopy(op))

    patch["Ops"] = new_ops
    return patch


def has_conflict_lines(lines: list[str]) -> bool:
    """
    Detect if any Git-style conflict markers remain in the merged lines.
    """
    return (
        any(line.startswith("<<<<<<<") for line in lines)
        and any(line.startswith("=======") for line in lines)
        and any(line.startswith(">>>>>>>") for line in lines)
    )


def merge_rv_sections_skip_conflict(base: str, *variants: str) -> str:
    """
    Merge `base` with any number of `variants`, but if merging
    a particular variant produces conflict markers, skip it and
    proceed to the next variant.
    """
    # Split each input into lines (preserving newline)
    base_lines = base.splitlines(keepends=True)
    cur = base_lines

    for variant in variants:
        other = variant.splitlines(keepends=True)
        # Try a three-way merge between base, current result, and this variant
        attempt = list(Merge3(base_lines, cur, other).merge_lines())

        if has_conflict_lines(attempt):
            logger.debug(f"Warning: skipped a variant due to conflict; continuing with next.")
            continue

        cur = attempt

    return "".join(cur)


def process_title(text2, word_json, para_id):
    """
    Process the title text2 to ensure it is formatted correctly.
    This function checks if the title is in the word_json and returns it.
    If not, it returns the original text2.
    """
    for item in word_json:
        if item["ParaId"] == str(para_id) and item.get("StyleProperties"):
            if item["StyleProperties"].get("Style").startswith("Heading"):
                if text2.endswith("."):
                    text2 = text2[:-1]
    return text2
