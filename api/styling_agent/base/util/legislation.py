import re
from urllib.parse import urljoin

from loguru import logger

from base.config.settings import config
from base.client.caas_repo import caas_repo_client
from base.error import LegislationCheckError
from base.util.constants import JURIS_MAPPING, JX_JURISDICTION, YEAR_NOT_FOUND, JX_NOT_FOUND


def search_all_legislations(legis, jurisdiction: str = None):
    try:
        match_phrase_field = None
        legis_cap = ' '.join([word.capitalize() for word in legis.split()])
        if "Act" in legis_cap or "Rule" in legis_cap or "Regulation" in legis_cap:
            match_phrase_field = "leg_title"
        elif "Bill" in legis_cap:
            match_phrase_field = "bill_title"
        else:
            logger.debug(f"Unsupported legislation format: {legis}")
            raise LegislationCheckError("Unsupported legislation format")

        if match_phrase_field:
            req_url = urljoin(config.CAAS_REPO_URL, config.META_ENDPOINT)
            body = {"query": {"match_phrase": {match_phrase_field: legis}}}

            response = caas_repo_client.query_meta(req_url, body)
            hits = response["data"]["hits"]["hits"]
            # filter by jurisdiction if provided
            if jurisdiction and JURIS_MAPPING.get(jurisdiction):
                filtered_jx = JURIS_MAPPING[jurisdiction]
                hits = list(
                    filter(lambda item: item["_source"].get("jurisdiction", "").upper() == filtered_jx.upper(), hits)
                )

            if not hits:
                logger.debug(f"No documents found for legislation: {legis}, jurisdiction: {jurisdiction}")
                raise LegislationCheckError(
                    f"No documents found for legislation: {legis}, jurisdiction: {jurisdiction}"
                )

            mapped_item = hits[0]["_source"]  # always return the first hit
            hit_jurisdiction = mapped_item.get("jurisdiction")
            if hit_jurisdiction:
                hit_jurisdiction_cap = hit_jurisdiction.capitalize()
                hit_jurisdiction = hit_jurisdiction_cap if hit_jurisdiction_cap in JX_JURISDICTION else JX_NOT_FOUND

            hit_year = mapped_item.get(match_phrase_field, YEAR_NOT_FOUND)[-4:]
            if not hit_year.isdigit():
                hit_year = YEAR_NOT_FOUND
            return hit_jurisdiction, hit_year
    except LegislationCheckError:
        return JX_NOT_FOUND, YEAR_NOT_FOUND
    except Exception as err:
        logger.error(f"Error searching legislation: {err}")
        return JX_NOT_FOUND, YEAR_NOT_FOUND


def replace_legis_info(text, legis_list):
    try:
        for legis in legis_list:
            escaped = re.escape(legis.strip())
            pattern_text = escaped.replace(r"\ ", r"\s+")
            anchor_pat = (
                r"(?P<anchor>"
                  r"\[\s*\d+\s*\]"
                  r"\[\s*Leg(?:islation)?\s*Check\s*Tool\s*\]"
                r")?"
            )
            pattern = re.compile(rf"{pattern_text}{anchor_pat}", flags=re.IGNORECASE)
            if "[year]" in legis and "[JX]" in legis:
                legis_input = legis[: legis.find("[year]")]
                jx, year = search_all_legislations(legis_input)
                legis_old = legis
                legis_new = legis.replace("[year]", year).replace("[JX]", jx)
                text = text.replace(legis_old, legis_new)
            elif "[year]" in legis:
                legis_input = legis[: legis.find("[year]")]
                match = re.search(r"\((.*?)\)", legis)
                jx_input = JX_JURISDICTION.get(match.group(1)) if match else None
                jx, year = search_all_legislations(legis_input, jx_input)
                legis_old = legis
                legis_new = legis.replace("[year]", year)
                text = text.replace(legis_old, legis_new)
            elif "[JX]" in legis:
                legis_input = legis[: legis.find("[JX]") - 1]
                original_year = legis_input.strip().split()[-1]
                legis_input = ' '.join(legis_input.strip().split()[:-1])
                jx, year = search_all_legislations(legis_input)
                if year != original_year:
                    legis_correct_year = legis_input + f" {year} ([JX])"
                legis_new = legis_correct_year.replace("[JX]", jx)
                text = text.replace(legis, legis_new)
            else:
                continue
            text = pattern.sub(
                lambda m: legis_new + (m.group("anchor") or ""),
                text,
                # count=1
            )
    except Exception as e:
        logger.error(f"Error replacing legislation info: {e}")
    return text

if __name__ == "__main__":
    # Example usage
#     legis_example = 'PRIVACY AND OTHER LEGISLATION AMENDMENT Bill 2023 ([JX])'
#     legis_list = [legis_example]
#     snippet = """
# In December 2024, the Federal Government passed the PRIVACY AND OTHER LEGISLATION AMENDMENT Bill 2023 ([JX]) (Bill), which introduced major changes to the Privacy Act. Among other things, the Bill requires entities to take reasonable technical and organisational steps in the face of a privacy breach. For more information about these reforms, see Guidance Note: Privacy Act reforms.
# """
    legis_example = 'Federal Circuit and Family Court of Australia [year] ([JX])'
    legis_list = [legis_example]
    snippet = """
However, it is important to bear in mind that, in Div 2 of the Federal Circuit and Family Court of Australia [year] ([JX]) (FCFCA), the ability of the parties recover indemnity costs may be constrained by statutory costs provisions in the particular area of law to which the proceeding relates.
"""
    updated_text = replace_legis_info(snippet, legis_list)
    print(updated_text)  # Should print the text with replaced legislation information