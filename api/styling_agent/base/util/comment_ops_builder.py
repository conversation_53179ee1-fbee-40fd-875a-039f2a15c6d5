import uuid
import difflib
import re

from base.util.segment_parser import Segment2


MARK_RE = re.compile(r"\[(\d+)]\[(.+?Tool)]")


def find_marks(text: str):
    for m in MARK_RE.finditer(text):
        yield {
            "idx": int(m.group(1)),
            "tool": m.group(2),
            "start": m.start(),
            "end": m.end(),
        }


def strip_marks(text: str) -> tuple[str, dict]:
    cleaned, offset_map = [], {}
    removed = 0
    i = 0
    while i < len(text):
        m = MARK_RE.match(text, i)
        if m:
            for j in range(m.start(), m.end()):
                offset_map[j] = None
            removed += m.end() - m.start()
            i = m.end()
            continue
        offset_map[i] = i - removed
        cleaned.append(text[i])
        i += 1
    return "".join(cleaned), offset_map


def diff_ranges(orig: str, mod: str) -> list[tuple[int, int]]:
    sm = difflib.SequenceMatcher(None, orig, mod)
    return [(j1, j2) for tag, _, _, j1, j2 in sm.get_opcodes() if tag != "equal"]


# -------------- 4) tool-specific pickup ----------
def pickup_quotation(mod: str, anchor: int) -> tuple[int, int]:
    """
    anchor = modified_text 中锚点左侧字符的位置
    返回 (start, end) 使 mod[start:end] 刚好是要评论的引文
    """
    # ① 尝试: 锚点前 100 字里找最近的 " 开头
    left = mod[max(0, anchor - 100) : anchor]
    if '"' in left:
        q_start = mod.rfind('"', max(0, anchor - 100), anchor)
        q_end = mod.find('"', anchor, anchor + 200)
        if q_start != -1 and q_end != -1:
            return q_start, q_end + 1

    # ② Block-quote: 向后找到下一个换行/句号
    start = anchor
    while start > 0 and mod[start - 1] not in "\n":
        start -= 1
    end = anchor
    while end < len(mod) and mod[end] not in "\n":
        end += 1
    return start, end


ABBR_PATTERNS = [
    re.compile(r"\b(s|ss|r|rr|pt|div|sch|cl|para)\s+\d+[A-Za-z\-]*", re.I),
    re.compile(r"\([A-Z][A-Za-z0-9]{1,25}(?:\s+[A-Z][A-Za-z0-9]{1,25})*\s*(?:Act|Regulations?|Rules?)?\)", re.I),
]

LEGIS_PATTERNS = [
    # Title Act 1999 ([JX])
    re.compile(
        r"[A-Z][A-Za-z &’‘\-]*?\s+(?:Act|Regulations?|Rules?|Bill)s?\s+"
        r"\d{4}\s*\(\[?[A-Za-z]{2,}\]?\)",
        re.I,
    ),
    # Title Act ([JX])   （无年份）
    re.compile(
        r"[A-Z][A-Za-z &’‘\-]*?\s+(?:Act|Regulations?|Rules?|Bill)s?\s*"
        r"\(\[?[A-Za-z]{2,}\]?\)",
        re.I,
    ),
]


def pickup_abbrev(mod: str, anchor: int) -> tuple[int, int]:
    context_start = max(0, anchor - 120)
    ctx = mod[context_start : min(len(mod), anchor + 2)]

    best = None
    for pat in ABBR_PATTERNS:
        for m in pat.finditer(ctx):
            st = context_start + m.start()
            ed = context_start + m.end()

            if st <= anchor <= ed:
                return st, ed
            if ed <= anchor:
                best = (st, ed)

    return best if best else (anchor - 1, anchor)


def pickup_legis(mod: str, anchor: int) -> tuple[int, int]:
    ctx_start = max(0, anchor - 200)
    ctx = mod[ctx_start : min(len(mod), anchor + 2)]

    best = None
    for pat in LEGIS_PATTERNS:
        for m in pat.finditer(ctx):
            st = ctx_start + m.start()
            ed = ctx_start + m.end()

            #   1) 找到最近的 '(' 和 ')' 相对坐标
            if st <= anchor <= ed:
                # 先定位原串里的 '(' ')'
                left = mod.find("(", st, ed)
                right = mod.find(")", left + 1, ed) + 1
                if left != -1 and right != -1 and left <= anchor <= right:
                    return left, right  # 仅括号段
                return st, ed  # 否则整段

            if ed <= anchor:  # 不覆盖但在 anchor 前
                best = (st, ed)

    return best if best else (anchor - 1, anchor)


def locate_segment(segs: list[Segment2], pos: int) -> Segment2:
    """
    在段落 segs 列表里找到覆盖 pos 的 Segment2。
    若 pos 正好等于某 run 结束边界，则归到左侧 run。
    """
    for sg in segs:
        if sg.start <= pos < sg.end:
            return sg
    # pos == end 的边界，找最后一个 end == pos 的
    for sg in reversed(segs):
        if sg.end == pos:
            return sg
    return segs[-1]  # 兜底


def build_offset_map(old: str, new: str) -> list[int]:
    """
    返回一个长度 = len(new)+1 的数组 M，
    使得  M[i] = new 文本坐标 i 在 old 文本里的对应坐标
    （删除算跳过，插入算与前一字符同坐标）
    """
    sm = difflib.SequenceMatcher(None, old, new, autojunk=False)
    m = [0]*(len(new)+1)
    oi = ni = 0
    for tag, i1,i2, j1,j2 in sm.get_opcodes():
        while ni < j1:
            m[ni] = oi
            ni += 1; oi += 1
        if tag in ("equal", "replace"):
            for _ in range(j2-j1):
                m[ni] = oi
                ni += 1; oi += 1
        elif tag == "insert":
            for _ in range(j2-j1):
                m[ni] = oi
                ni += 1
        elif tag == "delete":
            oi += (i2-i1)
    m[ni] = oi    # len(new) → len(old)
    return m


def build_comment_ops(
    old_txt: str,
    modified: str,
    commented: str,
    para_id: str,
    para_map: dict[str, dict],
    *,
    detail: bool = False,
) -> list[dict]:

    cleaned, mark_offset = strip_marks(commented)
    # assert cleaned == modified         # 如果上游保证一致，可放开

    diff_spans = diff_ranges(old_txt, modified)
    new2old    = build_offset_map(old_txt, modified)

    segs_meta  = para_map[para_id]["segments"]   # List[Segment2]
    patches: list[dict] = []

    for mk in find_marks(commented):

        # ── 1) anchor_new：锚点左侧有效字符在 new 坐标
        p_raw = mk["start"] - 1
        while p_raw >= 0 and mark_offset.get(p_raw) is None:
            p_raw -= 1
        anchor_new = mark_offset.get(p_raw, 0)

        # ── 2) tool-specific (st_new, ed_new)
        if mk["tool"].startswith("Quotation"):
            st_new, ed_new = pickup_quotation(modified, anchor_new)
        elif mk["tool"].startswith("Abbreviation"):
            st_new, ed_new = pickup_abbrev(modified, anchor_new)
        elif mk["tool"].startswith("Legislation"):
            st_new, ed_new = pickup_legis(modified, anchor_new)
        else:
            st_new, ed_new = anchor_new, anchor_new + 1

        # ── 3) clip 到当前 diff 段，确保不越界
        for d_st, d_ed in diff_spans:
            if d_st <= anchor_new < d_ed:
                st_new = max(st_new, d_st)
                ed_new = min(ed_new, d_ed)
                break

        # ── 4) new → old 坐标映射
        st_old = new2old[st_new]
        ed_old = new2old[ed_new] if ed_new < len(new2old) else len(old_txt)

        if st_old >= len(old_txt):
            st_old = len(old_txt) - 1

        # 过滤掉左侧空白，避免把前一个 run 的空格算进去
        # while st_old < len(old_txt) and old_txt[st_old].isspace():
        #     st_old += 1

        # ── 5) 生成 target = [{segId, range}]
        targets = []
        for s in segs_meta:
            # 与 [st_old, ed_old) 相交
            if s.start < ed_old and s.end >= st_old:
                rng_start = max(st_old, s.start) - s.start
                rng_end   = min(ed_old, s.end)  - s.start
                targets.append({
                    "segId": s.seg_id,
                    "range": { "start": rng_start, "end": rng_end }
                })

        comment_txt = f"AI suggestion: {mk['tool']}"# if detail else "AI suggestion"

        patches.append({
            "id": str(uuid.uuid4()),
            "op": "commentAdd",
            "target": targets,
            "comment": { "text": comment_txt }
        })

    return patches
