import json
from dataclasses import dataclass

import yaml

type_map = {
    "Insert": "ins",
    "Delete": "del",
    # eg. "MoveSource": "movsrc",
}

@dataclass
class Span:
    rev_id: str | None
    kind:   str          # "INS" | "DEL" | "BASE"
    start:  int
    end:    int          # 半开区间


def convert_yaml_to_json_if_possible(input_str):
    try:
        data = yaml.safe_load(input_str)
        if data is None:
            return input_str
        return json.dumps(data, indent=2, ensure_ascii=False)
    except yaml.YAMLError:
        return input_str


def build_paragraph_level_spans(paragraphs_data: list[dict]) -> dict[str, dict]:
    """
    Builds a dictionary where keys are ParaIds and values contain
    the plain text and a list of Span objects for that paragraph.
    """
    paragraph_output = {}

    for para_struct in paragraphs_data:
        para_id = para_struct.get("ParaId")
        if not para_id: # Skip if no ParaId
            continue

        if "Runs" not in para_struct or not para_struct["Runs"]:
            # Handle paragraphs with no runs (e.g., empty paragraphs)
            paragraph_output[para_id] = {"text": "", "spans": []}
            continue

        current_para_plain_text_parts = []
        current_para_spans: list[Span] = []
        current_char_offset = 0 # Tracks the start index for the next span

        for run_item in para_struct["Runs"]:
            key = run_item.get("Key")
            current_text_segment = ""
            current_rev_id = None
            current_kind = "BASE" # Default kind for normal runs

            if key == "Run":
                current_text_segment = run_item.get("Text", "")
                # current_rev_id and current_kind remain as default (None, "BASE")
            elif key == "Revision":
                current_rev_id = run_item.get("Id")
                revision_type = run_item.get("Type") # e.g., "Insert", "Delete"

                # Handle text which might be a list or string
                text_data = run_item.get("Text", [])
                if isinstance(text_data, list):
                    current_text_segment = "".join(text_data)
                elif isinstance(text_data, str):
                    current_text_segment = text_data
                else:
                    current_text_segment = "" # Should not happen based on typical input

                if revision_type == "Insert":
                    current_kind = "INS"
                # TODO: remove delete runs handling
                # elif revision_type == "Delete":
                #     current_kind = "DEL"
                # Add other revision types if necessary, e.g., "MoveSource", "MoveTarget"
                # For simplicity, we'll stick to INS/DEL/BASE.
                else:
                    # It's good to log or handle unknown types, but for spans,
                    # we might still create a span with an "UNKNOWN" kind.
                    print(f"Warning: ParaId '{para_id}', RevId '{current_rev_id}': "
                          f"Unknown or unhandled revision type '{revision_type}'. "
                          f"Defaulting kind to 'UNKNOWN_REV_TYPE'.")
                    current_kind = "UNKNOWN_REV_TYPE" # Or perhaps fall back to BASE or skip?
            
            else: # Unknown key type in Runs
                print(f"Warning: ParaId '{para_id}': Unknown key '{key}' in Runs. Skipping this run item.")
                continue


            if current_text_segment: # Only create a span if there's text
                segment_len = len(current_text_segment)
                
                # Check if this segment can be merged with the previous span
                # (if rev_id and kind are the same)
                if (current_para_spans and
                    current_para_spans[-1].rev_id == current_rev_id and
                    current_para_spans[-1].kind == current_kind and
                    current_para_spans[-1].end == current_char_offset): # Must be contiguous
                    # Merge with the previous span by extending its end
                    current_para_spans[-1].end += segment_len
                else:
                    # Create a new span
                    new_span = Span(
                        rev_id=current_rev_id,
                        kind=current_kind,
                        start=current_char_offset,
                        end=current_char_offset + segment_len
                    )
                    current_para_spans.append(new_span)
                
                current_para_plain_text_parts.append(current_text_segment)
                current_char_offset += segment_len

        paragraph_text = "".join(current_para_plain_text_parts)
        
        # Verification (optional but good for debugging)
        # Ensure total length of spans matches paragraph_text length if spans are contiguous and cover everything
        if current_para_spans:
            total_span_len_covered = current_para_spans[-1].end
            if total_span_len_covered != len(paragraph_text):
                 # This might happen if there are empty runs or if the logic for current_char_offset has issues.
                 # Or if spans are not meant to be fully contiguous (e.g., gaps).
                 # For this implementation, they should match.
                print(
                    f"Warning/Debug: ParaId '{para_id}': Mismatch in text length and span coverage. "
                    f"Text len: {len(paragraph_text)}, Spans cover up to: {total_span_len_covered}."
                )
                # Depending on strictness, this could be an error:
                # raise ValueError(f"Mismatch for ParaId '{para_id}': Text len {len(paragraph_text)}, Spans end {total_span_len_covered}")


        paragraph_output[para_id] = {
            "text": paragraph_text,
            "spans": current_para_spans # List of Span objects
        }

    return paragraph_output
