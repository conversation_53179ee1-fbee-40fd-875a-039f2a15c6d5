# Planner
import re


# Check Link Tool
CNAME_PATTERNS = re.compile(
    r"(?:"
    r"(?:cdn\.)?cloudflare\.net|"
    r"cloudflare-gateway\.com|"
    r"edgekey\.net|"
    r"edgesuite\.net|"
    r"akamaiedge\.net|"
    r"akamaihd\.net|"
    r"cloudfront\.net|"
    r"azureedge\.net|"
    r"(?:imperva|incapsula)\.net|"
    r"incapdns\.net|"
    r"sucuridns\.com|"
    r"fastly(?:lb)?\.net|"
    r"stackpathcdn\.com|"
    r"maxcdnedge\.net|"
    r"llnwd\.net"
    r")$",
    re.I,
)
# ------------------------------
# Whitelist: only official gov suffixes and lexis.com
# ------------------------------
WHITE_LIST_SUFFIXES = {
    ".gov.au",  # Australia
    ".gov.uk",  # United Kingdom
    ".gov",  # United States
    ".lexis.com",  # LexisNexis
    ".lexisnexis.com",  # LexisNexis legacy
    ".totalpatentone.com",  # TotalPatent One
    ".nexis.com",  # Nexis
    ".newsdesk.lexisnexis.com",  # Newsdesk by LexisNexis
}

# ------------------------------
# Blacklist: these domains are blocked
# ------------------------------
BLOCKED_DOMAINS = {
    "austlii.edu.au",  # AustLII
    "thomsonreuters.com",  # Thomson Reuters
    "jade.io",  # Jade
}

# Abbr Mapping
mapping_dict = {
    "regulation": "reg",  # 'regulations': 'regs',
    "Schedule": "Sch",  # 'Schedules'  : 'Schs',
    "section": "s",  # 'sections'   : 'ss',
    "subsection": "subs",  # 'subsections': 'subss',
    "number": "No",  # 'numbers'    : 'Nos',
    "Article": "Art",  # 'Articles'   : 'Arts',
    "rule": "r",
    "rules": "rr",
    "Part": "Pt",  # 'Parts'      : 'Pts',
    "paragraph": "para",  # 'paragraphs' : 'paras',
    "Chapter": "Ch",
    "chapter": "ch",
}


other_abbr_dict = {
    "Consumer Data Right": "CDR",
    "Australian financial services licence": "AFSL",
    "Australian credit licence": "ACL",
    "Acting Chief Justice": "ACJ",
    "Acting Judge/Acting Judges": "AJ/AJJ",
    "Administrative Appeals Tribunal": "AAT",
    "(administrator appointed)": "(admin apptd)",
    "Alternative Dispute Resolution": "ADR",
    "amended": "am",
    "annexure": "annex",
    "Appeal Judge/Appeal Judges": "JA/JJA",
    "Appendix/Appendixes": "App/Apps",
    "Article/Articles": "Art/Arts",
    "Associate/Associates": "Assoc/Assocs",
    "Association/Associations": "Assn/Assns",
    "Attorney-General (jurisdiction follows)": "A-G (NSW)",
    "Australia": "Aust",
    "Australian Broadcasting Authority": "ABA",
    "Australian Capital Territory": "ACT",
    "Australian Competition and Consumer Tribunal": "ACompT",
    "Australian Industrial Relations Commission": "AIRC",
    "Australian Law Reform Commission": "ALRC",
    "Australian Securities and Investments Commission": "ASIC",
    "Australian Stock Exchange": "ASX",
    "Brothers": "Bros",
    "Business Rules": "BR",
    "Chancery Court or Division": "Ch",
    "chapter/chapters": "ch/chs",
    "Chapter/Chapters": "Ch/Chs",
    "Chief Industrial Magistrate's Court of New South Wales": "CIM (NSW)",
    "Chief Justice/Chief Justices": "CJ/CJJ",
    "Class order": "CO",
    "clause/clauses": "cl/cll",
    "Commission": "Comm",
    "Commissioner": "Cmr",
    "Commonwealth ": "Cth",
    "Company/Companies": "Co/Cos",
    "compare": "cf",
    "Co-operative/Co-operatives": "Co-op/Co-ops",
    "Corporation/Corporations": "Corp/Corps",
    "County Council": "CC",
    "Court of Criminal Appeal (with jurisdiction)": "NSWCCA",
    "deceased": "(dec'd)",
    "definition": "def",
    "Department/Departments": "Dept/Depts",
    "Deputy Commissioner of Taxation": "DCT",
    "District Court (with jurisdiction)": "NSWDC",
    "Director of Public Prosecutions (jurisdiction follows)": "DPP (NSW)",
    "Director-General ": "D-G",
    "Division/Divisions": "Div/Divs",
    "edition": "ed",
    "editor/editors": "Ed/Eds",
    "Equal Opportunity Commission (jurisdiction follows)": "EOC (Vic)",
    "Equal Opportunity Tribunal (jurisdiction follows)": "EOT (NSW)",
    "Equity Court or Division": "Eq",
    "European Community": "EC",
    "European Union": "EU",
    "Ex parte": "Ex p",
    "Example/Examples": "eg/egs",
    "Exhibit/Exhibits": "Ex/Exs",
    "Exchequer Court or Division": "Exch",
    "Family Court of Australia/Court of Appeal": "FamC/FamCA",
    "Federal Commissioner of Taxation": "FCT",
    "Federal Court of Australia/Court of Appeal": "FCA/FCAFC",
    "Federal Magistrates Court": "FMCA",
    "Federal Magistrates Court (Family)": "FMCAfam",
    "figure/figures": "fig/figs",
    "(and) following": "ff",
    "for example, for instance": "eg",
    "footnote/footnotes": "fn/fnn",
    "Gazette": "Gaz",
    "Goods and Services Tax": "GST",
    "Government": "Govt",
    "Government Insurance Office": "GIO",
    "gram": "gm",
    "High Court of Australia": "HCA",
    "High Court of New Zealand": "HC (NZ)",
    "House of Lords": "HL",
    "Human Rights and Equal Opportunity Commission": "HREOC",
    "Imperial": "Imp",
    "in liquidation": "(in liq)",
    "in provisional liquidation": "(in prov liq)",
    "in voluntary liquidation": "(in vol liq)",
    "Incorporated": "Inc",
    "Industrial Appeal Court of Western Australia": "IAC (WA)",
    "Industrial Commission (jurisdiction follows)": "IC (SA)",
    "Industrial Court (jurisdiction follows)": "IC (NSW)",
    "Industrial Relations Commission (jurisdiction follows)": "IRC (NSW)",
    "Industrial Relations Court (jurisdiction follows)": "IRC of A",
    "inserted": "insrt",
    "Inspector": "Insp",
    "International": "Int",
    "International Centre for Settlement of Investment Disputes Review": "ICSID Rev",
    "International Labour Organisation": "ILO",
    "judge/judges": "J/JJ",
    "kilogram": "kg",
    "kilometre": "km",
    "King's Bench Court or Division": "KB",
    "Land and Environment Court of New South Wales": "NSWLEC",
    "Limited": "Ltd",
    "litre": "L",
    "Local Court(with jurisdiction)": "NSWLC",
    "Local Government Court": "Loc Govt Ct",
    "Lord Justice/Lord Justices": "LJ/LJJ",
    "Magistrate's Court": "Mag Ct",
    "metre": "m",
    "Migration Review Tribunal": "MRT",
    "millilitre": "mL",
    "Municipal Council": "MC",
    "National Companies and Securities Commission": "NCSC",
    "New South Wales": "NSW",
    "New Zealand": "NZ",
    "Northern Territory": "NT",
    "note/notes": "n/nn",
    "Number/Numbers": "No/Nos",
    "operation on proclamation": "opn on proc",
    "operational": "opn",
    "Order/Orders": "O/Os",
    "Ordinance/Ordinances": "Ord/Ords",
    "page/pages": "p/pp",
    "paragraph/paragraphs": "para/paras",
    "Pay as You Go Tax": "PAYG",
    "Part/Parts": "Pt/Pts",
    "Planning and Environment Court of Queensland": "PEC (Qld)",
    "Policy Statement": "PS/S",
    "Practice Note": "PN/PNN",
    "Precedent/Precedents": "Pr/Prr",
    "President": "P",
    "Private": "Pte",
    "Privy Council (Judicial Committee of the)": "PC",
    "Proprietary": "Pty",
    "(provisional liquidator appointed)": "(prov liq apptd)",
    "Public Limited Company": "plc",
    "Queen's Bench Court or Division (jurisdiction follows)": "QB (Ontario)",
    "Queensland": "Qld",
    "Queensland Government Industrial Gazette": "QGIG",
    "(receiver and manager appointed)": "(rec and mgr apptd)",
    "Refugee Review Tribunal": "RRT",
    "renumbered": "renum",
    "registered": "reg'd",
    "regulation/regulations": "reg/regs",
    "repatriation": "repat",
    "repealed": "rep",
    "Rex, Regina, The King, The Queen": "R",
    "rule/rules": "r/rr",
    "Schedule/Schedules": "Sch/Schs",
    "section/sections": "s/ss",
    "Shire Council": "SC",
    "standard/standards": "std/stds",
    "State Government Insurance Office (jurisdiction follows)": "SGIO (SA)",
    "Statutory Rule/Statutory Rules": "SR/SRs",
    "Steamship": "SS",
    "Social Security Appeals Tribunal": "SSAT",
    "South Australia": "SA",
    "subclause/subclauses": "subcl/subcll",
    "Subdivision/Subdivisions": "Subdiv/Subdivs",
    "subparagraph/subparagraphs": "subpara/subparas",
    "subsection/subsections": "subs/subss",
    "substituted": "subst",
    "Supreme Court of the United States": "SC (US)",
    "Sydney Futures Exchange": "SFE",
    "Tasmania": "Tas",
    "that is": "ie",
    "trading as": "t/as",
    "Trade Practices Commission": "TPC",
    "United Kingdom": "UK",
    "United Nations Treaty Series": "UNTS",
    "Victoria": "Vic",
    "volume/volumes": "vol/vols",
    "(voluntary administrator appointed)": "(vol admin apptd)",
    "Western Australia": "WA",
}

# -----------------------------------------------------
# Error messages
# -----------------------------------------------------
LAMBDA_ERROR_MSG = "Lambda function failed with code:"
DEFAULT_ERROR_MSG = "Generic error encountered while processing, please contact admin and try again later."
EMPTY_UPLOAD_FILE_ERROR_MSG = "The upload file is empty."
VALID_FILE_EXTENSIONS = [".docx", ".doc"]
VALID_FILE_ERROR_MSG = f"The upload document is not a valid file format. Valid formats are: {', '.join(VALID_FILE_EXTENSIONS)}."
EMAIL_SENT_DEFAULT_MSG = "The processed document will be sent to your email shortly. Please check your inbox."
EMAIL_INVALID_ERROR_MSG = "Invalid email format."
EMAIL_EMPTY_ERROR_MSG = "Please provide email address."

# -----------------------------------------------------
# Legislation check
JX_NOT_FOUND = "Legislation/jurisdiction not found in solr database"
YEAR_NOT_FOUND = ""
SOLR_DEFAULT_TIMEOUT = 30

JURIS_MAPPING = {
    "commonwealth": "Cth",
    "south australia": "SA",
    "new south wales": "NSW",
    "queensland": "Qld",
    "tasmania": "Tas",
    "northern territory": "NT",
    "australian capital territory": "ACT",
    "victoria": "Vic",
    "western australia": "WA",
}
JX_JURISDICTION = {
    "Cth": "commonwealth",
    "SA": "south australia",
    "NSW": "new south wales",
    "Nsw": "new south wales",
    "QLD": "queensland",
    "Qld": "queensland",
    "TAS": "tasmania",
    "Tas": "tasmania",
    "NT": "northern territory",
    "Act": "australian capital territory",
    "ACT": "australian capital territory",
    "VIC": "victoria",
    "Vic": "victoria",
    "WA": "western australia",
    "Wa": "western australia",
}

COMMNET_SYMBOLS = {JX_NOT_FOUND}

AUTHOR_CHECK_TOOL = {
    1: "Author attribution needs to follow authored by PG team format.",
    2: "Author attribution needs to follow authored externally and updated by PG team format.",
    3: "Author attribution needs to follow authored by multiple jurisdictional authors' format.",
    4: "Author attribution needs to follow adapted from another document format.",
    5: "AI unable identify author information, please check if author information is correctly listed.",
}
