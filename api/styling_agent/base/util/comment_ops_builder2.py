import html
import re
import unicodedata
import uuid
from difflib import SequenceMatcher
from typing import List, Tuple

from base.util.segment_parser import Segment2

# Support both numbered tags [1][Tool] and unnumbered tags [Tool]
MARKER_RE = re.compile(
    r"""
    (?:\[(\d+)\])?          # 可选的 [1] 之类
    \[([^\]]*?Tool)]        # 必须的 [... Tool]，且中途不能再有 ]
    """,
    re.VERBOSE,
)

# token 组合：同样收紧
_MARKER_TOKEN = r"(?:\[\d+\]\[[^\]]+?Tool\]|\[[^\]]+?Tool\])"

MARKER_TOK_RE = re.compile(rf"^(?:{_MARKER_TOKEN})+$")
_TRAILING_MARKERS_RE = re.compile(rf"(?:{_MARKER_TOKEN})+$")

EXCEPTION_TEXTS = (
    "Legislation/jurisdiction not found in solr database",
    "wrong abbr detected",
)

_EXCEPTION_NOTE_RE = re.compile(
    r"\s*\(?(" + "|".join(map(re.escape, EXCEPTION_TEXTS)) + r")\)?\s*",
    re.I,
)

STYLE_BLOCK_RE = re.compile(
    r"""
        (?P<tags>(?:<[ibu]\b[^>]*>)+)        # 起始标签序列
        (?P<inner>
            (?:(?!</?[ibu]\b).)*?            # 只吃到下一段 <i|b|u> 或 </i|b|u>
        )
        (?P<closers>(?:</[ibu]>\s*)+)        # 闭合标签，至少 1 次
    """,
    re.I | re.S | re.VERBOSE,
)


TOKEN_RE = re.compile(r"\s+|\w+|[^\w\s]", re.UNICODE)


def normalize_ws(s: str) -> str:
    return "".join(" " if unicodedata.category(ch) == "Zs" else ch for ch in s)


def tokenize(s: str) -> List[Tuple[str, int, int]]:
    return [(m.group(), m.start(), m.end()) for m in TOKEN_RE.finditer(s)]


_TAG = re.compile(r"<[^>]*>")
_ENTITY = re.compile(r"&[^;]+;")


def build_index_map(src: str, tgt: str) -> list[int]:
    """
    idx_map[j]  = src 中与 tgt[j] 对应的索引
    idx_map[j]  = -1            （若 tgt[j] 落在标签/实体或无法对应）
    长度保持 == len(tgt) —— 不会失配 i1/i2
    """
    src_low = html.unescape(src).casefold()
    tgt_low = html.unescape(tgt).casefold()

    m = [-1] * len(tgt)
    i = j = 0
    while i < len(src_low) and j < len(tgt_low):
        # ① 跳过标签
        if tgt_low[j] == "<":
            if entity_match := _TAG.match(tgt_low, j):
                j = entity_match.end()
                continue
        # ② 跳过实体
        if tgt_low[j] == "&":
            if entity_match := _ENTITY.match(tgt_low, j):
                j = entity_match.end()
                continue
        # ③ 普通字符比对（忽略大小写）
        if src_low[i] == tgt_low[j]:
            m[j] = i
            i += 1
            j += 1
        else:
            i += 1
    return m


def overlap(a0: int, a1: int, b0: int, b1: int) -> bool:
    # 1. Real geometric overlap
    if a1 > b0 and b1 > a0:
        return True
    # 2. Touching but spilling across the boundary:
    #    - left interval ends where right starts, and right is >1 char
    #    - OR right interval ends where left starts, and left is >1 char
    return a1 == b0 and (b1 - b0) > 1


def classify_style(tag_seq: str, inner: str) -> str | None:
    tags = set(re.findall(r"[ibu]", tag_seq.lower()))
    inner_clean = re.sub(r"<[^>]+>", "", inner).strip().lower()
    if tags == {"i", "u"} and inner_clean.startswith("practice tip"):
        return None
    if tags == {"i"}:
        return "Italic should not be used in this scenario."
    if tags == {"b"}:
        return "Bold should not be used in this scenario."
    if tags == {"u"}:
        return "Underline should not be used in this scenario."
    return "Please check the word styling."


def build_comment_ops(original: str, commented: str, run_style_text: str, segments: List[Segment2]) -> List[dict]:
    """
    Build comment operations from marked text.
    
    This function processes both numbered tags (e.g., [1][Tool]) and unnumbered tags (e.g., [Tool]).
    The MARKER_RE regex pattern supports both formats:
    - Numbered: [number][ToolName] where group(1) = number, group(2) = ToolName
    - Unnumbered: [ToolName] where group(1) = None, group(2) = ToolName
    
    Args:
        original: Original text without markers
        commented: Text with comment markers
        run_style_text: Text with style information
        segments: List of text segments
        
    Returns:
        List of comment operation dictionaries
    """
    original, commented, run_style_text = map(normalize_ws, (original, commented, run_style_text))
    patches, last_end = [], 0
    for mark in MARKER_RE.finditer(commented):
        # Extract tool name from group(2) - works for both numbered and unnumbered tags
        tool = mark.group(2)
        if tool == "Emphasis Check Tool":
            patches += _build_style_comment(original, commented, run_style_text, segments, mark)
        elif tool in [
            "Quotation Check Tool",
            "Legislation Check Tool",
            "Abbreviation Check Tool",
            "Grammar Check Tool",
            "Spelling Check Tool",
            "Capital Check Tool",
            "Number Check Tool",
        ]:
            patches.extend(_build_text_comment(original, commented, segments, mark, last_end))
        else:
            # Unknown / unsupported tool → no patches
            pass
        last_end = mark.end()

    uniq, seen = [], set()
    for op in patches:
        key = (
            op["comment"]["text"],
            tuple(sorted((t["segId"], t["range"]["start"], t["range"]["end"]) for t in op["target"])),
        )
        if key not in seen:
            seen.add(key)
            uniq.append(op)

    return uniq


def _strip_trailing_db_note(txt: str) -> str:
    """去掉结尾的 'in solr database'（忽略大小写），并整体 strip 一下。"""
    return re.sub(r"\s*in\s+solr\s+database\s*$", "", txt, flags=re.I).strip()


def _build_text_comment(
    original: str, commented: str, segments: List[Segment2], mark: re.Match, last_end: int
) -> List[dict]:
    j_s, j_e, tool = *mark.span(), mark.group(2)
    spans, snippet_acc = [], []
    tok_orig = tokenize(original)

    def token_start(idx):  # token anchor
        for _, s, e in tok_orig:
            if s <= idx < e:
                return s
        return idx

    for tag, i1, i2, j1, j2 in SequenceMatcher(None, original, commented, False).get_opcodes():
        if j1 < last_end or j2 > j_e:
            continue
        snip = commented[j1:j2]
        if tag in ("replace", "delete"):
            spans.append((i1, i2))
            if tag == "replace":
                snippet_acc.append(snip)
        elif tag == "insert" and not MARKER_TOK_RE.fullmatch(snip):
            # ptr = token_start(i1); spans.append((ptr, ptr + j2 - j1)); snippet_acc.append(snip)
            # ① 去掉尾部 markers
            cleaned = _TRAILING_MARKERS_RE.sub("", snip)
            # ② 去掉 EXCEPTION_TEXT 及其括号
            cleaned = _EXCEPTION_NOTE_RE.sub("", cleaned)

            ptr = token_start(i1)

            if cleaned:  # 仍有可见字符才建 span
                spans.append((ptr, ptr + len(cleaned)))
            # —— ❷ 只有异常提示或标记 → 零宽 span 也要留
            elif any(t in snip for t in EXCEPTION_TEXTS):
                spans.append((ptr, ptr))  # start == end  ⇒  零宽

            snippet_acc.append(snip)  # 保留完整片段给 ctext 判定
        elif tag == "equal":
            snippet_acc.append(snip)

    if not spans:
        return []

    # comment text
    snippet_before_anchor = "".join(snippet_acc).strip()
    match = _EXCEPTION_NOTE_RE.search(snippet_before_anchor)
    if match:
        full_msg = match.group(0).strip(" ()")  # 取到完整短语（去掉包裹的括号、空格）
        ctext = _strip_trailing_db_note(full_msg)
    else:
        ctext = f"AI suggestion: {tool}"
    # ctext = EXCEPTION_TEXT.replace("in solr database", "").strip() if EXCEPTION_TEXT in snippet_before_anchor else f"AI suggestion: {tool}"

    return _make_patch(spans, segments, ctext)


def _safe_bound(i1: int, i2: int, idx_map: list[int]) -> tuple[int, int]:
    """返回 (left, right)；若区间内全是标签，取最近正文字符作为零宽光标"""
    valid = [idx_map[k] for k in range(i1, i2) if idx_map[k] != -1]
    if valid:  # 区间里有正文字符
        return min(valid), max(valid) + 1

    # 向后找最近正文字符
    for k in range(i2, len(idx_map)):
        if idx_map[k] != -1:
            anchor = idx_map[k]
            return anchor, anchor  # 零宽

    # 否则回退到区间前最近字符
    anchor = max((v for v in idx_map[:i1] if v != -1), default=0)
    return anchor, anchor + 1
    # 半开区间


def _complete_style_block(html_run: str, s: int, e: int) -> str:
    """
    取 run_style_text[s:e]，向右补字符直到 <i/b/u> 标签全部闭合，避免截断。
    """
    frag = html_run[s:e]
    bal = []  # 未闭合标签栈
    tag_re = re.compile(r"</?\s*([ibu])[^>]*>", re.I)

    def _balance(txt):
        st = []
        for m in tag_re.finditer(txt):
            t = m.group(1).lower()
            if m.group(0)[1] == "/":
                if st and st[-1] == t:
                    st.pop()
            else:
                st.append(t)
        return st

    bal = _balance(frag)
    i = e
    while bal and i < len(html_run):
        frag += html_run[i]
        if html_run[i] == ">":
            bal = _balance(frag)
        i += 1
    return frag


# ────────────────────────────────────────────────────────────
#  Patch ①  向左补齐 <i/b/u> 起始标签
# ────────────────────────────────────────────────────────────
_STYLE_OPEN_RE = re.compile(r"<\s*[ibu]\b[^>]*>", re.I)


def _extend_left_to_tag(html_run: str, lo: int) -> int:
    """
    如果 lo 落在样式块内部，则回溯到最近的 <i/b/u> 起始标记处。
    否则原样返回。
    """
    # 已经在标签起始处就不用动
    if lo == 0:
        return lo

    # for m in _STYLE_OPEN_RE.finditer(html_run, 0, lo):
    #     pass  # 取最后一次匹配
    # return m.start() if m else lo
    # ① 先拿到离 lo 最近的起始标签
    tags = list(_STYLE_OPEN_RE.finditer(html_run, 0, lo))
    if not tags:
        return lo
    pos = tags[-1].start()

    # ② 继续向左，只要 tag 与 tag 之间 “没有正文字符”
    idx = len(tags) - 2
    while idx >= 0:
        prev = tags[idx]
        gap = html_run[prev.end() : pos]
        if gap.strip() == "":  # 全是空白 / 换行 ⇒ 仍属同一 tag‑block
            pos = prev.start()
            idx -= 1
        else:
            break

    return pos


def _trim_ws(orig: str, l: int, r: int) -> tuple[int, int]:
    """收缩到首尾非空白字符；若全为空白则返回 (l, l) 形成零宽范围。"""
    while l < r and orig[l].isspace():
        l += 1
    while r > l and orig[r - 1].isspace():
        r -= 1
    return l, r


def _extend_right_whitespace(original: str, rgt: int) -> int:
    """把右边界扩到连续空格之后第一个非空格字符前"""
    while rgt < len(original) and original[rgt] == " ":
        rgt += 1
    return rgt


def _build_style_comment(
    original: str,
    commented: str,
    run_style_text: str,
    segments: List[Segment2],
    mark: re.Match,
) -> List[dict]:
    """Emphasis Check：对每个违规 <b/i/u> 块生成精确批注"""

    j_marker = mark.start()

    # ① 上一个 marker 结束
    prev_end = 0
    for m in MARKER_RE.finditer(commented):
        if m.start() >= j_marker:
            break
        prev_end = m.end()

    # ② 找候选窗口 [lo, hi)
    lo = hi = None
    for tag, i1, i2, j1, j2 in SequenceMatcher(None, run_style_text, commented, False).get_opcodes():
        if j1 < prev_end or j2 > j_marker:
            continue
        lo = i1 if lo is None else min(lo, i1)
        hi = i2 if hi is None else max(hi, i2)

    if lo is None or hi is None:
        return []

    lo = _extend_left_to_tag(run_style_text, lo)
    idx_map = build_index_map(original, run_style_text)
    html_frag = _complete_style_block(run_style_text, lo, hi)

    scen_spans: dict[str, list[tuple[int, int, bool]]] = {}

    # ③ 扫描所有 <b/i/u> 区块
    for blk in STYLE_BLOCK_RE.finditer(html_frag):
        # scen = classify_style(blk.group('tags'), blk.group('inner'))
        # if scen is None:
        #     continue                            # 合法 → 跳过
        # —— 0. 若仅空白差异，直接跳过 ———————————————
        # ① 找到 commented 中对应 inner 的片段
        run_html = blk.group(0)  # <b><i>word</i></b>  ← 含标签
        run_inner = blk.group("inner")  # word               ← 纯文本

        # —— 0. 若只有首尾空白变化就跳过 ————————————
        plain_run = re.sub(r"<[^>]+>", "", run_inner)
        run_tags = set(re.findall(r"<([ibu])\b", run_html.lower()))
        core = plain_run.strip()
        hit_pos = commented.find(core, prev_end, j_marker)
        if hit_pos != -1:
            comm_inner = commented[hit_pos : hit_pos + len(core)]
            start_pos = commented.rfind("<", prev_end, hit_pos)  # 只在当前窗口内回溯
            if start_pos == -1:
                start_pos = hit_pos  # 前面没有标签

            end_pos = commented.find(">", hit_pos + len(core), j_marker)
            if end_pos == -1:
                end_pos = hit_pos + len(core)  # 后面没有标签
            else:
                end_pos += 1  # 包含 '>' 本身
            comm_slice = commented[start_pos:end_pos]
            comm_tags = set(re.findall(r"<([ibu])\b", comm_slice.lower()))
            if plain_run.strip() == comm_inner.strip() and run_tags == comm_tags:
                continue  # 只有首/尾空白不同 → 忽略

        # —— 1. 样式合法性判断 ——
        scen = classify_style(blk.group("tags"), run_inner)
        if scen is None:
            continue  # 案件名等豁免

        i1 = lo + blk.start("inner")
        i2 = lo + blk.end("inner")
        # lft, rgt = _safe_bound(i1, i2, idx_map)
        lft, rgt = _trim_ws(original, *_safe_bound(i1, i2, idx_map))
        if lft == rgt:  # 纯空白或被收缩为空 → 跳过
            continue

        has_trail_ws = bool(blk.group("inner")) and blk.group("inner")[-1].isspace()
        scen_spans.setdefault(scen, []).append((lft, rgt, has_trail_ws))

    # ④ 生成批注 —— 只取最后一块，并向右吸收尾随纯空白
    patches_pos: list[tuple[int, dict]] = []
    for scen, spans in scen_spans.items():
        # tail = [spans[-1]]                  # 最后一块
        # 最后一块如果只剩空白，回退到前一块
        idx = len(spans) - 1
        while idx and original[spans[idx][0] : spans[idx][1]].isspace():
            idx -= 1
        tail = [spans[idx]]
        idx = len(spans) - 1
        while idx + 1 < len(spans):
            nxt = spans[idx + 1]  # 右侧下一块
            if nxt[0] == tail[-1][1] or run_style_text[tail[-1][1] : nxt[0]].strip() == "":
                tail.append(nxt)  # 紧挨着或仅空白 ⇒ 合并
                idx += 1
            else:
                break

        # patch = _make_patch(tail, segments, f"AI Suggestion: {scen}")
        # 右扩空白
        tail_l, tail_r, ws_flag = tail[-1]
        if ws_flag:  # 如果最后一块有尾随空白
            tail_r = _extend_right_whitespace(original, tail_r)
        tail[-1] = (tail_l, tail_r, ws_flag)
        patch = _make_style_patch(tail, segments, f"AI Suggestion: {scen}")
        if patch:
            patches_pos.append((tail[0][0], patch[0]))  # _make_patch 只返 1 条

    # 按文中位置排序，保持输出顺序稳定
    return [p for _, p in sorted(patches_pos, key=lambda x: x[0])]


def _make_style_patch(
    spans: list[tuple[int, int, bool]],
    segments: list[Segment2],
    comment_text: str,
) -> list[dict]:
    # ① 合并 spans — 这里只剩最后一块及其空白扩展
    lft, rgt, _ = spans[0]
    span_len = rgt - lft
    anchor_seg = max(
        segments,
        key=lambda s: max(0, min(s.end, rgt) - max(s.start, lft)),
    )
    # ② 裁剪到锚段内部
    lft = max(lft, anchor_seg.start)
    rgt = min(rgt, anchor_seg.end)

    if rgt - lft < span_len:
        rgt = min(anchor_seg.end, lft + span_len)

    targets = [
        {
            "segId": anchor_seg.seg_id,
            "range": {"start": lft - anchor_seg.start, "end": rgt - anchor_seg.start},
        }
    ]
    return _build_comment_ops(targets, comment_text)


def _make_patch(spans, segments, comment_text):
    """
    根据 diff spans + 段信息生成批注操作。
    - anchor 段 = 满足 start <= lft < end 的第一段
      · 如果 lft 正好落在段尾，则被视为属于下一段
    - anchor 段的选区右端可越出自身尾，直达 rgt
    - 其它 overlap 段的选区截到自身尾
    - 非 anchor 且零宽范围 -> 跳过（避免脏锚点）
    """
    # ① 计算全局范围
    lft, rgt = min(s[0] for s in spans), max(s[1] for s in spans)

    # ② 零宽批注：直接在光标处挂零宽锚点
    if lft == rgt:
        targets = [
            {
                "segId": seg.seg_id,
                "range": {"start": lft - seg.start, "end": lft - seg.start},
            }
            for seg in segments
            if seg.start <= lft <= seg.end
        ]
        return _build_comment_ops(targets, comment_text)

    # ③ 找到 anchor 段 —— 第一段满足 start <= lft < end
    anchor_seg = next((s for s in segments if s.start <= lft < s.end), segments[-1])

    targets = []
    for seg in segments:
        # 仅处理真正 overlap 的段
        if not overlap(seg.start, seg.end, lft, rgt):
            continue

        seg_len = seg.end - seg.start
        s_off = max(0, lft - seg.start)

        # anchor 段可覆盖到 rgt；其余段截到自身尾
        if seg is anchor_seg:
            e_off = rgt - seg.start
        else:
            e_off = min(seg_len, rgt - seg.start)

        # 非 anchor 且零宽 -> 跳过
        if seg is not anchor_seg and s_off == e_off:
            continue

        targets.append(
            {
                "segId": seg.seg_id,
                "range": {"start": s_off, "end": e_off},
            }
        )

    return _build_comment_ops(targets, comment_text)


def _build_comment_ops(targets, comment_text):
    return (
        [
            {
                "id": str(uuid.uuid4()),
                "op": "commentAdd",
                "target": targets,
                "comment": {"text": comment_text},
            }
        ]
        if targets
        else []
    )
