import difflib
import re
import unicodedata
import uuid
from typing import Any

from base.util.segment_parser import Segment, Segment2


QUOTATION_SEP = "\n\n" 
# 示例 1: 基本形式
# "Legislation/jurisdiction not found in solr database"
# 示例 2: 带括号
# "(Legislation/jurisdiction not found in solr database)"
# 示例 3: 前后有空格
# "  Legislation/jurisdiction not found in solr database  "
# 示例 4: 带括号和空格
# "  (Legislation/jurisdiction not found in solr database)  "
# 示例 5: 只有左括号
# "(Legislation/jurisdiction not found in solr database"
# 示例 6: 只有右括号
# "Legislation/jurisdiction not found in solr database)"
# 示例 7: 大小写混合
# "LEGISLATION/JURISDICTION NOT FOUND IN SOLR DATABASE"
INVALID_PHRASES = [
    "Legislation/jurisdiction not found in solr database",
    "wrong abbr detected",
]

INVALID_NOTE_RE = re.compile(
    r"\s*\(?(" + "|".join(map(re.escape, INVALID_PHRASES)) + r")\)?\s*",
    flags=re.IGNORECASE,
)

def _strip_invalid_note(txt: str) -> str:
    """
    清理文本中的无效注释（例如："Legislation/jurisdiction not found in solr database"）
    
    该方法会智能地移除或替换无效注释：
    - 如果无效注释前后有空白字符，则完全删除该注释
    - 如果无效注释两边都不是空白字符，则用单个空格替换（避免词语粘连）
    
    Args:
        txt: 需要清理的原始文本
        
    Returns:
        清理后的文本
        
    Examples:
        "Hello Legislation/jurisdiction not found in solr database world" 
        -> "Hello  world"
        
        "Hello (Legislation/jurisdiction not found in solr database) world"
        -> "Hello world"
        
        "HelloLegislation/jurisdiction not found in solr databaseworld"
        -> "Hello world"
    """
    def repl(m: re.Match):
        # 获取匹配位置前后的字符，用于判断上下文
        before = txt[m.start()-1] if m.start() > 0 else " "
        after  = txt[m.end()]     if m.end()   < len(txt) else " "
        
        # 如果前后有空白字符，直接删除匹配内容；否则用空格替换避免词语粘连
        return "" if (before.isspace() or after.isspace()) else " "

    cleaned = INVALID_NOTE_RE.sub(repl, txt)
    return cleaned

STYLE_TAG_RE = re.compile(r'</?(?:i|b|u)\b[^>]*>', re.IGNORECASE)
def strip_style_tags(t: str) -> str:
    return STYLE_TAG_RE.sub('', t)

INLINE_MARKER_RE = re.compile(
    r"""
        \s*                # 可选前导空白
        \[ \d+ \]          # [number]
        \[ [^\]]+?Tool \]  # [Something Tool]
        \s*                # 可选尾随空白
    """,
    re.I | re.X,
)

def _strip_inline_markers(txt: str) -> str:
    """
    去掉 `[n][XXX Tool]` 这类内嵌批注标记，
    并智能处理前后空白：
      - 若标记前后本就有空格，则连同空格一起删
      - 若标记夹在词中间，则保留一个空格占位，避免单词粘连
    """
    def repl(m: re.Match) -> str:
        before = txt[m.start()-1] if m.start() > 0 else " "
        after  = txt[m.end()]     if m.end()   < len(txt) else " "
        # 如果前后有空白，删除整个匹配；否则留一个空格
        return "" if (before.isspace() or after.isspace()) else " "

    txt = INLINE_MARKER_RE.sub(repl, txt)
    single_tool_re = re.compile(r'\s*\[[^\]]*?Tool\]\s*', re.I)
    txt = single_tool_re.sub(repl, txt)

    return txt

def normalize_whitespace(s: str) -> str:
    return "".join(" " if unicodedata.category(ch)=="Zs" else ch for ch in s)


TOKEN_RE = re.compile(r"\s+|\w+|[^\w\s]", re.UNICODE)
def tokenize(s): return [(m.group(), m.start(), m.end()) for m in TOKEN_RE.finditer(s)]


def _s(t, i, d): return t[i][1] if 0 <= i < len(t) else d
def _e(t, i, d): return t[i][2] if 0 <= i < len(t) else d
def _locate(segs, pos):
    for sg in segs:
        if sg.start <= pos < sg.end: return sg
    for sg in reversed(segs):
        if sg.end == pos: return sg
    return segs[-1]

def build_edit_ops(old_txt: str, new_txt: str, segs: list[Segment], rev):
    old_txt = normalize_whitespace(old_txt)
    new_txt = normalize_whitespace(new_txt)
    new_txt = strip_style_tags(new_txt)
    new_txt = _strip_inline_markers(new_txt)
    new_txt = _strip_invalid_note(new_txt)
    tok_old, tok_new = tokenize(old_txt), tokenize(new_txt)
    seq_old, seq_new = [t[0] for t in tok_old], [t[0] for t in tok_new]
    sm = difflib.SequenceMatcher(None, seq_old, seq_new, autojunk=False)

    ops: list[dict[str, Any]] = []
    for tag, i1, i2, j1, j2 in sm.get_opcodes():
        if tag == "equal": continue
        ob, oe = _s(tok_old, i1, len(old_txt)), _e(tok_old, i2 - 1, len(old_txt))
        nb, ne = _s(tok_new, j1, len(new_txt)), _e(tok_new, j2 - 1, len(new_txt))

        # 单 token replace → 字符级 diff
        if tag == "replace" and i2 - i1 == 1 and j2 - j1 == 1:
            os, ns = old_txt[ob:oe], new_txt[nb:ne]
            for ctag, ci1, ci2, cj1, cj2 in difflib.SequenceMatcher(None, os, ns).get_opcodes():
                if ctag == "equal": continue
                sg = _locate(segs, ob + ci1)
                start_in_seg = (ob + ci1) - sg.start
                end_in_seg   = (ob + ci2) - sg.start
                if ctag == "replace":
                    ops.append({
                        "id": str(uuid.uuid4()),
                        "op": "replace",
                        "target": [{
                            "segId": sg.seg_id,
                            "range": {"start": start_in_seg, "end": end_in_seg}
                        }],
                        "text": ns[cj1:cj2],
                        "revision": rev,
                    })
                elif ctag == "delete" and ci1 != ci2:
                    ops.append(
                        {
                            "id": str(uuid.uuid4()),
                            "op": "delete",
                            "target": [
                                {
                                    "segId": sg.seg_id,
                                    "range": {"start": start_in_seg, "end": end_in_seg},
                                }
                            ],
                            "text": os[ci1:ci2],
                            "revision": rev,
                        }
                    )
                elif ctag == "insert" and cj1 != cj2:
                    rel = (ob + ci1) - sg.start
                    ops.append({
                        "id": str(uuid.uuid4()),
                        "op": "insert",
                        "target": [{"segId": sg.seg_id, "range": {"start": start_in_seg, "end": start_in_seg}}],
                        "text": ns[cj1:cj2],
                        "revision": rev,
                    })
            continue

        # 块级 replace
        if tag == "replace" and ob < oe and nb < ne:
            new_chunk = new_txt[nb:ne]
            pos = ob
            # 如果删除块跨多个 segment，就对每个 segment 各生成一次 replace
            while pos < oe:
                sg = _locate(segs, pos)
                seg_start = max(pos, sg.start)
                seg_end   = min(oe,  sg.end)
                rel_start = seg_start - sg.start
                rel_end   = seg_end   - sg.start

                # ops.append({
                #     "id": str(uuid.uuid4()),
                #     "op": "replace",
                #     "target": [{
                #         "segId": sg.seg_id,
                #         "range": {"start": rel_start, "end": rel_end}
                #     }],
                #     "text": new_chunk,
                #     "revision": rev,
                # })
                sub_start = seg_start - ob
                if seg_end == oe:                         # ← 最后一段
                    sub_end = len(new_chunk)              #   把剩下的全取走
                else:
                    sub_end = sub_start + (seg_end - seg_start)

                subtext = new_chunk[sub_start : sub_end]

                if subtext:                       # 删光的话 subtext 可能为空，可按需跳过
                    ops.append({
                        "id": str(uuid.uuid4()),
                        "op": "replace",
                        "target": [{
                            "segId": sg.seg_id,
                            "range": {"start": rel_start,
                                      "end":   rel_end}
                        }],
                        "text": subtext,
                        "revision": rev,
                    })
                pos = seg_end
        elif tag == "delete" and ob != oe:
            # sg = _locate(segs, ob)
            # ops.append({
            #     "op": "delete",# if tag == "delete" else "replace",
            #     "target": [sg.seg_id],
            #     "range": {"start": ob - sg.start, "end": oe - sg.start},
            #     "text": old_txt[ob:oe],
            #     "revision": rev,
            # })
            pos = ob
            while pos < oe:
                sg = _locate(segs, pos)
                seg_start = max(pos, sg.start)
                seg_end   = min(oe, sg.end)          # 不跨过此 run 的右边界

                ops.append(
                    {
                        "id": str(uuid.uuid4()),
                        "op": "delete",
                        "target": [{"segId": sg.seg_id, 
                                    "range": {"start": seg_start - sg.start, "end": seg_end - sg.start}}],
                        # "range": {
                        #     "start": seg_start - sg.start,
                        #     "end":   seg_end   - sg.start,
                        # },
                        "text": old_txt[seg_start:seg_end],
                        "revision": rev,
                    }
                )
                pos = seg_end                      # 跳到下一个未处理位置

        # 块级 insert
        elif tag == "insert" and nb != ne:
            anchor = ob if ob < len(old_txt) else max(len(old_txt) - 1, 0)
            sg = _locate(segs, anchor)
            rel = anchor - sg.start
            ops.append({
                "id": str(uuid.uuid4()),
                "op": "insert",
                "target": [{"segId": sg.seg_id,
                            "range": {"start": rel, "end": rel}}],
                # "range": {"start": rel, "end": rel},
                "text": new_txt[nb:ne],
                "revision": rev,
            })

    ops = _split_quotation_ops(ops)


    return ops


def _clone_op(op: dict) -> dict:
    """
    深拷贝 target，避免 range 被后续修改污染。
    """
    return {
        k: ([t.copy() for t in v] if k == "target" else v)
        for k, v in op.items()
    }


def _split_quotation_ops(ops: list[dict]) -> list[dict]:
    """
    旧文本 ──►  diff  ──►  粗颗粒 ops  ──►  _split_quotation_ops  ──►  精细 ops
                                 │              ├─ prefix（冒号等）
                                 │              ├─ quotationSeparate（"\n\n" 或 "\n"）
                                 │              └─ suffix（引文正文）


    | 术语                  | 解释                           | 例子                                   |
    | --------------------- | ----------------------------- | ------------------------------------ |
    | **prefix**            | 分隔符前的新增符号              | `"foo bar"`→`"foo:\n\nbar"` 中的 `":"` |
    | **quotationSeparate** | `"\n\n"` 或 `"\n"`，标记引用块起点 | `"\n\n"` 或 `"\n"`                   |
    | **suffix**            | 分隔符后紧跟的新文本            | `"bar2"`                             |
    | **anchor**            | 插入/替换的光标位置             | prefix 处理完后的位置                       |

    ┌─────────────────────────────────────────┐
    │ op(text=":\n\nbar2", range=3..7)        │
    └───────────────┬─────────────────────────┘
                    ▼
        ┌────────────────┐     (1) prefix
        │":"  replace 3..4│
        └────────────────┘
                    ▼
        ┌────────────────┐     (2) quotationSeparate
        │"\n\n" insert @4 │
        └────────────────┘
                    ▼
        ┌────────────────┐     (3) suffix
        │"bar2" replace 5..7│
        └────────────────┘

    拆分后的示例：
    -----------------------
    输入操作：
      {
        "op": "replace",
        "target": [{"segId": "pZ-000", "range": {"start": 3, "end": 7}}],
        "text": ":\n\nbar2"
      }

    拆分为：
      ① prefix replace (冒号替换空格):
         {"op": "replace", "target": [... {"start": 3, "end": 4}], "text": ":"}

      ② quotationSeparate 插入分隔符:
         {"op": "quotationSeparate", "target": [... {"start": 4, "end": 4}], "text": "\n\n"}

      ③ suffix replace (后缀文本替换):
         {"op": "replace", "target": [... {"start": 5, "end": 7}], "text": "bar2"}

    """
    result: list[dict] = []

    for op in ops:
        kind = op.get("op")
        text = op.get("text", "")

        # 检查是否包含双换行符或单换行符（优先匹配双换行符）
        sep_to_use = None
        if QUOTATION_SEP in text:
            sep_to_use = QUOTATION_SEP
        elif "\n" in text:
            sep_to_use = "\n"

        if kind in ("insert", "replace") and sep_to_use:
            seg_id      = op["target"][0]["segId"]
            orig_range  = op["target"][0]["range"].copy()     # {'start', 'end'}
            prefix, sep, suffix = text.partition(sep_to_use)
            # 旧文本长度，即 end – start，用来判断这条 op 原来是纯插入（old_len = 0）还是替换（old_len > 0）
            old_len     = orig_range["end"] - orig_range["start"]

            # ---------- 1. prefix ----------
            if prefix:
                pre = _clone_op(op)
                pre["id"]   = str(uuid.uuid4())
                pre["text"] = prefix
                if old_len == 0:
                    # old text 中没有可替换字符 → 只做“insert”, 所以拆开后的新range定在开头
                    pre["op"] = "insert"
                    pre["target"] = [{
                        "segId": seg_id,
                        "range": {"start": orig_range["start"], "end": orig_range["start"]},
                    }]
                else:
                    # old text 中有字符 → 只替换前缀长度那部分, 缩窄 range 到 start + len(prefix)
                    pre["target"][0]["range"] = {
                        "start": orig_range["start"],
                        "end":   orig_range["start"] + len(prefix),
                    }
                result.append(pre)

            # 重新计算 anchor 与剩余旧字符数
            # anchor 处理后剩余文本的起点，也就是紧接着放置分隔符的位置。
            anchor          = orig_range["start"] + len(prefix)
            # 在prefix替换掉后，old text 里还剩多少字符。
            remaining_old   = old_len - len(prefix)

            # ==> prefix == ""
            #     suffix == "bar"
            #     old_len == 7 - 3 == 4
            #     remaining_old = 4 - 0 = 4

            # → 可直接替换空格 ⇒ sep_replaced = True
            # → quotationSeparate 做 replace: range = [3, 4]

            # old_txt: "foo"
            # new_txt: "foo:\n\nbar"   # 全是新增，没有要被替换的旧字符

            # ==> prefix == ":"
            #     suffix == "bar"
            #     old_len == 0  # insert
            #     remaining_old = 0 - 1 = -1

            # → prefix 已占用旧字符 ⇒ sep_replaced = False
            # → quotationSeparate 必须 insert（不能替换不存在的字符）

            # ⇒ prefix == ""     （文本开头就是分隔符）
            # ⇒ sep    == "\n\n"
            # ⇒ suffix == "bar"
            #    remaining_old = 0 - 0 = 0 => sep_replaced = False

            sep_replaced    = (prefix == "" and remaining_old >= 1)

            # ---------- 2. separator ----------
            sep_op = _clone_op(op)
            sep_op["id"]   = str(uuid.uuid4())
            sep_op["op"]   = "quotationSeparate"
            sep_op["text"] = sep
            if sep_replaced:
                # "foo bar"
                #     ↑ index 3
                # ↓ replace [3,4) with "\n\n" ↓
                # "foo\n\nbar"
                sep_op["target"][0]["range"] = {"start": anchor, "end": anchor + 1}
            else:
                # insert
                sep_op["target"] = [{
                    "segId": seg_id,
                    "range": {"start": anchor, "end": anchor},
                }]
            result.append(sep_op)

            # ---------- 3. suffix ----------
            if suffix:
                suf = _clone_op(op)
                suf["id"]   = str(uuid.uuid4())
                suf["text"] = suffix

                remaining_after_sep = remaining_old - (1 if sep_replaced else 0)
                if remaining_after_sep > 0:
                    # 情况 A：还有旧字符 → 用 replace 替掉它们
                    # 区间从 suffix 的起点 到 原 range 的 end
                    suf["op"] = "replace"
                    suf["target"][0]["range"] = {
                        "start": anchor + (0 if sep_replaced else 1),
                        "end":   orig_range["end"],
                    }
                else:
                    # 情况 B：没有旧字符 → insert new suffix
                    suf["op"] = "insert"
                    suf["target"] = [{
                        "segId": seg_id,
                        "range": {"start": anchor, "end": anchor},
                    }]
                result.append(suf)
        else:
            result.append(op)

    return result
