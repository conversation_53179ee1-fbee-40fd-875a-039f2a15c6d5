import re


def is_hyperlink(text: str) -> bool:
    """Check if the text contains HTTP or HTTPS URL links.

    This function detects:
    - http:// URLs
    - https:// URLs
    """
    if not text or not isinstance(text, str):
        return False

    # Simple pattern for HTTP/HTTPS URLs
    url_pattern = r"https?://"

    return bool(re.search(url_pattern, text.strip(), re.IGNORECASE))


def extract_links(text: str) -> list[str]:
    """Extract HTTP/HTTPS links from text using regex.

    Args:
        text: The text to extract links from

    Returns:
        List of extracted HTTP/HTTPS URLs
    """
    if not text or not isinstance(text, str):
        return []

    # Regex pattern to match HTTP/HTTPS URLs
    url_pattern = r'https?://[^\s<>"{}|\\^`\[\]]+[^\s<>"{}|\\^`\[\].,;:]'

    # Find all matches
    links = re.findall(url_pattern, text, re.IGNORECASE)

    return links
