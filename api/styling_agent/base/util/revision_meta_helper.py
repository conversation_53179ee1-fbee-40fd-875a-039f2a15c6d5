import difflib
import re
from collections import defaultdict
from copy import deepcopy
from functools import cached_property
from typing import List

from loguru import logger


class RevisionMetaHelper:

    target_meta_fields = ["Type", "Key", "Author", "Date", "Style", "Comment"]

    def __init__(self, original_revision: dict):
        self.original_revision = original_revision
        self._offset = 0

    def _convert_text_to_str(self, data: list[dict]):
        for item in data:
            text = item.get('Text')
            if not isinstance(text, list):
                continue
            item['Text'] = "".join(text)

    def _get_revision_meta(self, revision_run: dict):
        meta = {}
        for key, val in revision_run.items():
            if key in self.target_meta_fields:
                meta[key] = val
        return meta

    @cached_property
    def original_revision_runs(self):
        original_runs = self.original_revision.get('Runs')
        self._convert_text_to_str(original_runs)

        target_runs = []
        for run in original_runs:
            text = run.get('Text')
            meta = self._get_revision_meta(run)
            for item in text:
                target_runs.append(
                    {
                        "Text": item,
                        "Meta": meta
                    }
                )
        return target_runs

    def _search_revision_run(self, target_word: str):
        original_run = self.original_revision_runs[self._offset]
        self._offset += len(target_word)
        if original_run['Text'] == target_word:
            return original_run
        elif original_run['Text'] == " " and target_word != " ":
            # if original run is space, check next run 
            next_run = self.original_revision_runs[self._offset]
            self._offset += len(target_word)
            if next_run['Text'] == target_word:
                return next_run
        return None

    def has_metadata(self, change: dict):
        if change.get('Author') and change.get('Date'):
            return True
        return False

    def _is_same_run(self, item: dict, previous_item: dict):
        if (item['Type'] == previous_item['Type']) and (item['Author'] == previous_item['Author']):
            return True
        return False

    def _combine_revision(self, revisions: list[dict]):
        combined_revision_run = []
        for item in revisions:
            if not combined_revision_run:
                combined_revision_run.append(deepcopy(item))
                continue

            previous_item = combined_revision_run[-1]
            if self._is_same_run(item, previous_item):
                previous_item['Text'] += item['Text']
            else:
                combined_revision_run.append(deepcopy(item))
        return combined_revision_run

    def _get_final_revision(self, revision: list[dict]):
        for item in revision:
            if (item.get("Key") or "").lower() == "run":
                item["Type"] = "Run"
            if isinstance(item.get("Text"), str):
                item["Text"] = [item["Text"]]
        return revision

    def run(self, change_texts: list[dict]):
        logger.debug(f"Revision helper, original revision: {self.original_revision}")
        logger.debug(f"Revision helper, change texts: {change_texts}")
        if not change_texts or not self.original_revision:
            logger.debug("Revision helper, change texts or revision not found.")
            return change_texts

        try:
            self._convert_text_to_str(change_texts)
            revision = []
            for change_text in change_texts:
                text = change_text.get('Text')

                change_type = (change_text.get('Type') or "").lower()
                if change_type == 'delete': # delete always from ai
                    revision.append(change_text)
                    self._offset += len(text)
                    continue

                # insert with meta always from ai
                if change_type == "insert" and self.has_metadata(change_text): 
                    revision.append(change_text)
                    continue

                for item in text:
                    target_run = self._search_revision_run(item)
                    new_run = deepcopy(change_text)
                    new_run['Text'] = item
                    if target_run:
                        meta_data = target_run.get('Meta') or {}
                        
                        new_run.update(meta_data)
                    revision.append(new_run)

            combined_revision = self._combine_revision(revision)
            final_revision = self._get_final_revision(combined_revision)
            logger.debug(f"Revision helper, final revision: {final_revision}")
            return final_revision
        except Exception as e:
            logger.error("Error in Revision Meta Helper: ex: {e}")
            return change_texts


# ---------- ② 辅助：把字符串 token 化并带上 token→字符坐标 ----------
# TOKEN_RE = re.compile(r"\s+|[A-Za-z0-9]+|[^\w\s]")      # 空白 | 单词 | 单符号
TOKEN_RE = re.compile(
    r"\s+"        # 空白
    r"|[A-Za-z]"  # 单个字母（区分大小写）
    r"|[0-9]+"    # 数字
    r"|[^\w\s]"   # 其它单符号
)


def tokenize_with_pos(s: str):
    """return [(token, char_start, char_end), ...]"""
    return [(m.group(), m.start(), m.end()) for m in TOKEN_RE.finditer(s)]


def find_changed_segments(old_plain: str,
                          new_plain: str,
                          spans: List["Span"]) -> dict[str, dict]:
    """
    Return {rev_id: {"kind": kind, "old_seg": ..., "new_seg": ...}}
    Only revisions whose content actually changed are included.
    """
    tok_old = tokenize_with_pos(old_plain)
    tok_new = tokenize_with_pos(new_plain)
    seq_old = [t[0] for t in tok_old]
    seq_new = [t[0] for t in tok_new]

    sm = difflib.SequenceMatcher(None, seq_old, seq_new, autojunk=False)
    bucket = defaultdict(lambda: {"kind": "", "old_seg": "", "patches": []})

    # helper: get span by char pos / overlap
    def anchor_span(char_pos: int):
        for sp in spans:
            if sp.start < char_pos < sp.end:       # ① 落在内部
                return sp

        # ② 正好卡在边界：找 end == pos 的最后一条
        left = [sp for sp in spans if sp.end == char_pos]
        if left:
            cand = left[-1]
            if cand.kind != "BASE":
                return cand
            # 左邻是 BASE ⇒ 尝试用它右边那个
            idx = spans.index(cand)
            if idx + 1 < len(spans):
                return spans[idx + 1]
            # TODO: log, alert?
            return cand

        # 理论上不会到这里，兜底, log?
        return spans[-1]

    def spans_overlapping(a: int, b: int):
        for sp in spans:
            if sp.end > a and sp.start < b:
                yield sp

    # walk opcodes
    for tag, i1, i2, j1, j2 in sm.get_opcodes():
        old_beg = tok_old[i1][1] if i1 < len(tok_old) else len(old_plain)
        old_end = tok_old[i2-1][2] if i2-1 < len(tok_old) else old_beg
        new_beg = tok_new[j1][1] if j1 < len(tok_new) else len(new_plain)
        new_end = tok_new[j2-1][2] if j2-1 < len(tok_new) else new_beg

        # deletions / left side of replace
        if tag in ("delete", "replace") and old_beg != old_end:
            for sp in spans_overlapping(old_beg, old_end):
                b = bucket[sp.rev_id]
                if not b["old_seg"]:
                    b["old_seg"] = old_plain[sp.start:sp.end]
                    b["kind"]    = sp.kind
                # record a delete patch – empty replacement text
                rel_a = max(old_beg, sp.start) - sp.start
                rel_b = min(old_end, sp.end)   - sp.start
                b["patches"].append((rel_a, rel_b, "", "delete"))

        # insertions / right side of replace
        if tag in ("insert", "replace") and new_beg != new_end:
            sp_anchor = anchor_span(old_beg)
            b = bucket[sp_anchor.rev_id]
            if not b["old_seg"]:
                b["old_seg"] = old_plain[sp_anchor.start:sp_anchor.end]
                b["kind"]    = sp_anchor.kind
            # b["new_parts"].append(new_plain[new_beg:new_end])
            # record insert/replace patch
            # rel_a = old_beg - sp_anchor.start      # insertion point in old_seg
            # rel_b = rel_a if tag == "insert" else (old_end - sp_anchor.start)
            rel_a = max(old_beg, sp_anchor.start) - sp_anchor.start
            rel_b = (
                rel_a                                 # pure insert
                if tag == "insert"
                else min(old_end, sp_anchor.end) - sp_anchor.start
            )

            b["patches"].append((rel_a, rel_b, new_plain[new_beg:new_end], tag))

    # build output
    changed = {}
    for rid, info in bucket.items():
        if not rid:
            continue
        seg  = info["old_seg"]
        if not info["patches"]:
            continue

        # sort patches by start offset; inserts naturally
        patches = sorted(info["patches"], key=lambda p: (p[0], p[3] != "insert"))
        builder = []
        cursor  = 0
        for start, end, repl, tag in patches:
            builder.append(seg[cursor:start])  # unchanged prefix
            if tag in ("insert", "replace"):
                builder.append(repl)
            if tag != "insert":
                cursor = end                   # for del / replace skip old text
            else:
                cursor = start                 # pure insert, keep cursor
        builder.append(seg[cursor:])                # tail
        new_seg = "".join(builder)
        # new_seg = "".join(info["new_parts"]) or info["old_seg"]
        if new_seg != seg:
            changed[rid] = {
                "kind":    info["kind"],
                "old_seg": seg,
                "new_seg": new_seg,
            }
    return changed


# new_text = "On 26 November 2024, Treasury released an exposure draft of the Competition and Consumer (Consumer Data Right) Amendment (2024 Measures No. 2) Rules 2024 ((Amending Rules)), explanatory materials (EM) and an information sheet for consultation. If implemented, the (Amending Rules) will operate to extend the CDR to the NBL sector and narrow the scope of CDR data for the banking and NBL sectors."
# # new_text = "A printed advertisement that relates to the provision of credit to which the NCC applies is required to have the ACL No included in the printed advertisement: , NCCPA and , National Consumer Credit Protection Regulations 2010 (Cth) (NCCP Regs)rotection Regulations 2010 (Cth). For further information on when an ACL holder must include its ACL No, see Guidance Note: Primary obligationsrequirements of a credit licensees."
# find_changed_segments(paragraph_rev_maps['0DDE0D3E']['text'], new_text, paragraph_rev_maps['0DDE0D3E']['spans'])