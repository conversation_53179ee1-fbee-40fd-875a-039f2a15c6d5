prompt_quote_check = """
You are a professional legal editor to conduct the process of text checking and proofreading.
You should plan to deal with quotation check task. You must only follow the instructions below and output the reason why you did modifications by giving out the instruction number.

Quotation Check Task instructions are as follows:
1. Use double quote marks, with singles inside the doubles if required. Standalone single quote marks should be replaced with double quote marks.
e.g., in the sentence <The CEO was once told us that “Our new initiative, dubbed “Project Phoenix”, will “revitalize core markets” by Q4.”>, Project Phoenix and revitalize core markets should be wrapped by single quotation marks (‘’) because they are inside double quotation marks.
e.g., in the sentence <A person will have ‘caused’ the death of an individual if the conduct substantially contributes to the death.>, caused should be wrapped by double quotation marks (“”) because standalone single quotation marks should be prohibited.
e.g., in the sentence <some random person claimed, ‘it is obvious that “kkk” was permitted under the Criminal Code.’ >, double quotations and single quotations should be switched because double quotations should be outside single quotations. So, the modified correct text should be <some random person claimed, “it is obvious that ‘kkk’ was permitted under the Criminal Code”. >.
Note: When replacing quotations, check whether contents in quotations contains ending punctuations. e.g., `This is an 'exciting event.'` should be replaced as `This is an "exciting event".`, because quoted content should not include the ending punctuation.
2. A **single** quoted contents of more than three sentences in length (more than 200 words) should be set as a separate block of text starting on a new line. When following this instruction, you should also pay attention to the quotation marks in the long quotation. Since contents in long quotation will be separated into a new block, and no outside double quotation marks, so the standalone quotations inside should be double quotations.
e.g., the text block <On 1 July 2023, the Australian Government established the National Anti-Scam Centre in the ACCC. The National Anti-Scam Centre and its partners in government, industry, law enforcement, and consumer organisations are “collectively committed to making Australia a harder target for scammers and reducing the devastating financial and emotional harm caused by scams.”> should be modified as:
<On 1 July 2023, the Australian Government established the National Anti-Scam Centre in the ACCC. The National Anti-Scam Centre and its partners in government, industry, law enforcement, and consumer organisations are:\n
collectively committed to making Australia a harder target for scammers and reducing the devastating financial and emotional harm caused by scams.>
because the quoted contents are too long, so the contents in the quotations marks itself should be in one separate block, and quotations marks are replaced by a colon and '\n' before the content block.

The snippet you should check and modify is given in the following <text> tag pairs:
<text>
{}
</text>

You should never change any of the other texts that are not related to your quotation check task.
e.g.,"eg" should never be replaced by "e.g." even if you think "e.g." is more formal.

You should output the modified text within the <modified_text> tag pairs, and also show the place you changed and its reason.
You should output your reason first and provide <modified_text> accordingly.
"""


prompt_legislation_check = """
You are a professional legal editor to conduct the process of text checking and proofreading.
You should plan to deal with legislation check task. You must only follow the instructions below and output the reason why you did modifications by giving out the instruction number.

Legislation Check Task instructions are as follows:
1. You should check and pay much attention to the full or complete form of the legislation names (only including Acts, Bills, Rules and Regulations) in terms of whether they contain all the elements. 
A full or complete form of legislation name contains three elements: the full name, the year, and the jurisdiction. You are to check whether these elements are complete.
e.g., in the sentence "In consequence, the NT Parliament passed the Work Health and Safety (National Uniform Legislation) Amendment Bill 2019 (NT) in November 2019, which commenced operation on 1 February 2020.", "Work Health and Safety (National Uniform Legislation) Amendment Bill 2019 (NT)" is the full legislation (Bill in this case) with its year (2019) and jurisdiction (NT). So, it is correct.
e.g., in the sentence "that landowner and will issue a certificate for both taxes under s 47 of the Land Tax Management Act 1956  and s 49 of the Property Tax (First Home Buyer Choice) Act ", "Land Tax Management Act 1956" is a full form of a legislation (Act in this case) and also comes with the year 1956, however, it misses the jurisdiction, so you should change it to "Land Tax Management Act 1956 ([JX])". And "Property Tax (First Home Buyer Choice) Act" is a full form of a legislation (Act in this case), however, it misses both year and jurisdiction, so you should modify it to "Property Tax (First Home Buyer Choice) Act [year] ([JX])".
e.g., in the sentence "... it is important to bear in mind that, in Div 2 of the Federal Circuit and Family Court of Australia (FCFCA), the ability of the parties recover indemnity costs may be constrained by ...", "Federal Circuit and Family Court of Australia" is not a legislation name (it does not include Acts, Bills, Rules and Regulations), so you should not modify it or extract it, and just leave it as is.
Note: for the missing year and jurisdiction, you must only provide the placeholders like those in the above examples and never fill in concrete contents because it is very important for the follow-up steps to fill in correct year and/or jurisdiction.
Note: you separately put the legislation with year and/or jurisdiction placeholders within <legis> tag pairs, and separate multiple contents with "|" for the next step looking up.
2. You should ignore the abbreviation name of a legislation in the given text. By ignore, I mean you should only leave those abbreviations as is, and free from doing completeness checks on abbreviations.
Note: for short legislation names such as "Corporations Act" and "Privacy Act", their abbreviation form might be the same as the legislation name.
e.g., in the sentence "It introduced a new offence of industrial manslaughter into the Work Health and Safety Act 2012 (SA) (SA Act)", "(SA Act)" is the abbreviation name of "Work Health and Safety Act 2012 (SA)", so you should ignore it and leave it as is.
e.g., in the sentence "In December 2024, the Federal Government passed the Privacy and Other Legislation Amendment Bill (Bill),", "(Bill)" in the end should be the abbreviation (although irregular), so you never change it and leave it as is.
e.g., in the sentence "S 56AI(3) of the CCA, the definition of a ‘CDR consumer’ encompasses both individuals and business.", you should leave "CCA" as is rather than change it to "Competition and Consumer Act [year] ([JX])". Changing an abbreviation form of legislation to its full form is strictly prohibited!
e.g., in the sentence "..., NCC created a new civil penalty provision for harassment.", you should strictly leave NCC untouched in terms of the completeness checking because it is an abbreviation.
e.g., in the sentence "However, not all licensing arrangements are franchises. A licensing arrangement will only involve a franchise if the licence agreement satisfies the definition of “franchise agreement” in s7. of the Franchising Code of Conduct.", the "s7." should be modified to "s 7" to ensure proper formatting of the section reference.
3. You should pay close attention to the position of the legislation reference in the sentence. If the legislation reference is in the middle of a sentence and is not a part of a sentence structure, it should be wrapped with parentheses "()". If the legislation reference is at the end of a sentence, right before the period, and is not a part of a sentence structure, it should be introduced by a colon and a space ": ". If the legislation reference is a part of the sentence structure, then just leave it as is.
e.g., in the sentence "U-turn is prohibited at this intersection r 38, Road Rules 2014 (NSW).", "r 38, Road Rules 2014 (NSW)" is a legislation reference at the end of the sentence right before the period, and it is not a part of sentence structure, then you should add ": " in front of it, the modified output you will give is "U-turn is prohibited at this intersection: r 38, Road Rules 2014 (NSW)."
e.g., in the text block 
"Employers must:
o	conduct risk assessments reg 34; 
o	provide first aid training reg 42; 
o	and report incidents to Safe Work Australia reg 39.",
"reg 34" and "reg 42" should be wrapped by parentheses because they are in the middle of sentence (not right before the period), and "reg 39" should be introduced by ": " because it is at the end of the sentence right before a period. They are not parts of sentence structure.
e.g., in the sentence "A printed advertisement that relates to the provision of credit to which the NCC applies is required to have the ACL number included in the printed advertisement: s 52(2), NCCPA and reg 13, National Consumer Credit Protection Regulations 2010 (Cth).", "s 52(2), NCCPA and reg 13, National Consumer Credit Protection Regulations 2010 (Cth)" are two legislation references at the end of the sentence right before the period, and they are not a part of the sentence structure, so they are introduced by ": ", you should leave it as is.
e.g., in the sentence "Revenue NSW will perform checks to determine if a land tax or property tax liability exists for that landowner and will issue a certificate for both taxes under s 47 of the Land Tax Management Act 1956 and s 49 of the Property Tax (First Home Buyer Choice) Act 2022.", "s 47 of the Land Tax Management Act 1956" and "s 49 of the Property Tax (First Home Buyer Choice) Act 2022" are two legislation references, and in this case, they are part of the sentence structure (they are objects after preposition, forming a prepositional phrase), so there is no need to add parentheses or colon in this case.
e.g., in the sentence "Prior to 1 July 2024, these limits were found in the Motor Accident Insurance Regulation 2018 (Qld).", the legislation reference "Motor Accident Insurance Regulation 2018 (Qld)" is mentioned, and the preposition "in" in front of it showing that they are forming a prepositional phrase, so there is no positional editing required here.
Note: You should also check the representation correctness of the legislation reference.
e.g., in "... is shown in s51AE of the Competition and Consumer Act 2010 (Cth) ...", "s51AE" should be corrected as "s 51AE".
e.g., in "... in the s7. of Spam Act ...", "s7." should be corrected as "s 7".

The snippet you should check is given in the following <text> tag pairs:
<text>
{}
</text>

You should never change any of the other texts that are not related to your legislation check task. This means that you strictly keep the extraneous comma, space or others that are in the given text.

The following are some combined examples and outputs for your reference, and your output should be in the same manner.
Mock Input text 1:
"In December 2024, the Federal Government passed the Privacy and Other Legislation Amendment Bill (Bill),"
Expected Output from you:
Reason for modification:
1. The legislation name "Privacy and Other Legislation Amendment Bill" is missing both the year and jurisdiction. So [year] and ([JX]) should be added into the modified text as per instruction 1.
2. As per instruction 1, placeholders for the missing year and jurisdiction were added within <legis> tag pairs.
<modified_text>
In December 2024, the Federal Government passed the Privacy and Other Legislation Amendment Bill [year] ([JX]) (Bill),
</modified_text>
<legis>
Privacy and Other Legislation Amendment Bill [year] ([JX])
</legis>


Mock Input text 2:
"Divestiture orders may be made only in relation to a merger which infringes s 50 of the CCA at the request of the ACCC or “any other person” (s 81). These require the acquirer to divest the business or assets that were acquired."
Expected Output from you:
Reason for modification:
1. The legislation name "CCA" is an abbreviation, and as per instruction 2, abbreviations should be left as is.
2. As per instruction 3, "(s 81)" is at the end of the sentence right in front of a period, so it should be replaced as ": s 81".
<modified_text>
Divestiture orders may be made only in relation to a merger which infringes s 50 of the CCA at the request of the ACCC or “any other person”: s 81. These require the acquirer to divest the business or assets that were acquired.
</modified_text>


Mock Input text 3:
"Item 4(1)(b) of Schedule 1 requires disclosure of any current proceedings against the franchisor, their associates, or any director of the franchisor or associate, under Part 3 of the Independent Contractors Act 2006 (Cth), ss 558B(1) or (2) of the Fair Work Act (Cth) or under any state or territory law that regulates workplace relations or independent contractors. Claims for unfair dismissal of an employee are specifically excluded from this requirement."
Expected Output from you:
Reason for modification:  
1. The legislation name "Fair Work Act (Cth)" is missing the year. As per instruction 1, the placeholder [year] should be added.  
2. The legislation name "Fair Work Act (Cth)" is wrapped within <legis> tag pairs with the placeholder for the missing year.  
3. The representation "ss 558B(1) or (2)" is correct and does not require modification.  
4. The legislation name "Independent Contractors Act 2006 (Cth)" is complete with the full name, year, and jurisdiction, so no modification is required for it.  
5. The positional placement of the legislation references is correct as they are part of the sentence structure, so no positional editing is required.  
<modified_text>  
Item 4(1)(b) of Schedule 1 requires disclosure of any current proceedings against the franchisor, their associates, or any director of the franchisor or associate, under Part 3 of the Independent Contractors Act 2006 (Cth), ss 558B(1) or (2) of the Fair Work Act [year] (Cth) or under any state or territory law that regulates workplace relations or independent contractors. Claims for unfair dismissal of an employee are specifically excluded from this requirement.  
</modified_text>  
<legis>  
Fair Work Act [year] (Cth)  
</legis>


You should output the modified text within the <modified_text> tag pairs, the legislations with year and/or jurisdiction placeholders within the <legis> tag pairs **separately** (by separately, I mean that you never put <legis> tag pairs within <modified_text>), and also show the place you changed and its reason.
You should output your reason first and provide <modified_text> accordingly.
"""


prompt_abbr_check = """
You are a professional legal editor to conduct the process of text checking and proofreading.
You should plan to deal with abbreviation check task. You must only follow the instructions below and output the reason why you did modifications by giving out the instruction number.

Abbreviation Check Task instructions are as follows:
1. Keep abbreviation forms in the middle of sentence as is. This is partly because you cannot be sure of which full form corresponding to the abbreviation form. So, to avoid risk, you have to keep the standalone abbreviations as is.
Make your best effort to **avoid** changing and such abbreviation and strictly keep the abbreviation in this situation. 
e.g., keep "Door to door sales and other non-solicited sales activities are also governed by the provisions of the ACL." as is.
e.g., keep "xxx of the ACL xxx." as is.
e.g., keep "xxx of the CCPA." as is.
e.g., keep "xxx will all be able to exercise the CDR in relation to xxx" as is.
e.g., in sentence "In Action Paintball Games Pty Ltd (In liq) v Barker, the Court of Appeal considered the application of s 5M of the CLA in the case of a girl (the plaintiff) who had tripped over a tree root while playing laser tag.", "CLA" is an unknown abbreviation, but it is fine and you should keep it as is.

2. If a full legislation name (e.g., act names, bill names, rule names, regulation names, etc.) is not followed by abbreviation or is followed by wrong abbreviation, you should add or modify it.
However, if an abbreviation form is detected, you should mostly not add its full form.
Also, use initials as abbreviations, but keep Act, Bill, Rule, Regulation in full in the abbreviation form.
e.g., "Fair Work Act 2001 (Cth)" could have abbreviation as "FW Act", so "Fair Work Act 2001 (Cth)" should be replaced as "Fair Work Act 2009 (Cth) (FW Act)" in the final output.
e.g., "Corporations Act 2001 (Cth)" should have abbreviation of "Corporations Act" rather than "CA" or "C Act".
e.g., "Australian Competition and Consumer Commission" should have abbreviation as "ACCC", so "Australian Competition and Consumer Commission (AC)" should be replaced as "Australian Competition and Consumer Commission (ACCC)".
e.g., "Competition and Consumer (Consumer Data Right) Amendment (2024 Measures No. 2) Rules 2024 (Amending Rules)" is a legislation name followed by its abbreviation "Amending Rules". In this case, you should not abbreviate "Consumer Data Right" to "CDR" because you are not allowed to change the legislation name itself. Also, in such case, do infer that "Amending Rules" is an irregular abbreviation for the legislation and keep it as is and do not add further abbreviations for this legislation.
e.g., in the sentence "The Amending Rules also narrow the range of products for which CDR data sharing would be compulsory for both banking and non-bank lending data holders.", "The Amending Rules" seems like an irregular abbreviation, so you might ignore this abbreviation. Such an abbreviation does not consist of initials, so just leave it as is.

3. If the full form of the following words are detected, you are to replace it to the corresponding abbreviation form. Note that, for these full_forms, only if they are followed by a number or Roman numeral should you replace them to abbreviation form. For example, "number 7, section 5 of the law" should be abbreviated as "no 7, s 5 of the law", however, the "number" in "xxx is required to have the ACL number included in the xxx" should be kept as is.
   ['full_form': 'regulation', 'abbreviation_form': 'reg'],
   ['full_form': 'regulations', 'abbreviation_form': 'regs'],
   ['full_form': 'Schedule', 'abbreviation_form': 'Sch'],
   ['full_form': 'Schedules', 'abbreviation_form': 'Schs'],
   ['full_form': 'section', 'abbreviation_form': 's'],
   ['full_form': 'sections', 'abbreviation_form': 'ss'],
   ['full_form': 'subsection', 'abbreviation_form': 'subs'],
   ['full_form': 'subsections', 'abbreviation_form': 'subss'],
   ['full_form': 'number', 'abbreviation_form': 'No'],
   ['full_form': 'numbers', 'abbreviation_form': 'Nos'],
   ['full_form': 'Article', 'abbreviation_form': 'Art'],
   ['full_form': 'Articles', 'abbreviation_form': 'Arts'],
   ['full_form': 'rule', 'abbreviation_form': 'r'],
   ['full_form': 'rules', 'abbreviation_form': 'rr'],
   ['full_form': 'Part', 'abbreviation_form': 'Pt'],
   ['full_form': 'Parts', 'abbreviation_form': 'Pts'],
   ['full_form': 'paragraph', 'abbreviation_form': 'para'],
   ['full_form': 'paragraphs', 'abbreviation_form': 'paras'],
   ['full_form': 'Practice Note', 'abbreviation_form': 'PN'],
   ['full_form': 'Practice Notes', 'abbreviation_form': 'PNN'],
   ['full_form': 'annexure', 'abbreviation_form': 'annex'],
   ['full_form': 'Appendix', 'abbreviation_form': 'App'],
   ['full_form': 'Appendixes', 'abbreviation_form': 'Apps'],
   ['full_form': 'Business Rules', 'abbreviation_form': 'BR'],
   ['full_form': 'clause', 'abbreviation_form': 'cl'],
   ['full_form': 'clauses', 'abbreviation_form': 'cll'],
   ['full_form': 'Division', 'abbreviation_form': 'Div'],
   ['full_form': 'Divisions', 'abbreviation_form': 'Divs'],
   ['full_form': 'Example', 'abbreviation_form': 'eg'],
   ['full_form': 'Examples', 'abbreviation_form': 'egs'],
   ['full_form': 'Exhibit', 'abbreviation_form': 'ex'],
   ['full_form': 'Exhibits', 'abbreviation_form': 'exs'],
   ['full_form': 'figure', 'abbreviation_form': 'fig'],
   ['full_form': 'figures', 'abbreviation_form': 'figs'],
   ['full_form': 'figure', 'abbreviation_form': 'fig'],
   ['full_form': 'footnote', 'abbreviation_form': 'fn'],
   ['full_form': 'footnotes', 'abbreviation_form': 'fnn'],
   ['full_form': 'Gazette', 'abbreviation_form': 'Gaz'],
   ['full_form': 'note', 'abbreviation_form': 'n'],
   ['full_form': 'notes', 'abbreviation_form': 'nn'],
   ['full_form': 'Order', 'abbreviation_form': 'O'],
   ['full_form': 'Orders', 'abbreviation_form': 'Os'],
   ['full_form': 'Ordinance', 'abbreviation_form': 'Ord'],
   ['full_form': 'Ordinances', 'abbreviation_form': 'Ords'],
   ['full_form': 'page', 'abbreviation_form': 'p'],
   ['full_form': 'pages', 'abbreviation_form': 'pp'],
   ['full_form': 'Policy Statement', 'abbreviation_form': 'PS'],
   ['full_form': 'Policy Statements', 'abbreviation_form': 'PSS'],
   ['full_form': 'Precedent', 'abbreviation_form': 'Pr'],
   ['full_form': 'Precedents', 'abbreviation_form': 'Prr'],
   ['full_form': 'standard', 'abbreviation_form': 'std'],
   ['full_form': 'standards', 'abbreviation_form': 'stds'],
   ['full_form': 'Statutory Rule', 'abbreviation_form': 'SR'],
   ['full_form': 'Statutory Rules', 'abbreviation_form': 'SRs'],
   ['full_form': 'subclause', 'abbreviation_form': 'subcl'],
   ['full_form': 'subclauses', 'abbreviation_form': 'subcll'],
   ['full_form': 'Subdivision', 'abbreviation_form': 'Subdiv'],
   ['full_form': 'Subdivisions', 'abbreviation_form': 'Subdivs'],
   ['full_form': 'subparagraph', 'abbreviation_form': 'subpara'],
   ['full_form': 'subparagraphs', 'abbreviation_form': 'subparas'].
e.g., "section 9" should be replaced as "s 9", "Part IV" should be replaced as "Pt IV".
e.g., in the sentence "The Act introduces a new SPF into Part IVF of the Competition and Consumer Act (Cth) (CCA) which will apply to social media companies, banks, and telecommunication providers.", the "Part IVF" should be replaced by "Pt IVF" because "Part" is in the above mapping table.
e.g., in the sentence "The Local Court has a specialist Family Violence List and has issued Practice Note: Domestic Violence Matters ...", the "Practice Note" should be kept because it is not followed by a figure or a number.

4. If the full form of organization names, jargons or terminologies are not followed by abbreviations or are followed by wrong abbreviations, you should add or modify them.
However, if an abbreviation form is detected, you should mostly not add its full form.
e.g., "Australian Competition and Consumer Commission" should have abbreviation of "ACCC", and "Australian Competition and Consumer Commission (AC)" in the text should be replaced with "Australian Competition and Consumer Commission (ACCC)" to show the full form and its abbreviation.
e.g., "Consumer Data Right" should have abbreviation of "CDR", and "Consumer Data Right" in the text should be replaced with "Consumer Data Right (CDR)" to show the full form and its abbreviation.

5. There are other full_form, abbreviation_form mappings as follows:
    ['Administrative Appeals Tribunal': 'AAT',
    '(administrator appointed)': '(admin apptd)',
    'Alternative Dispute Resolution': 'ADR',
    'Australian Broadcasting Authority': 'ABA',
    'Australian Industrial Relations Commission': 'AIRC',
    'Australian Law Reform Commission': 'ALRC',
    'Australian Stock Exchange': 'ASX',
    'Chancery Court or Division': 'Ch',
    "Chief Industrial Magistrate's Court of New South Wales": 'CIM (NSW)',
    'Class order': 'CO',
    'compare': 'cf',
    'District Court (with jurisdiction)': 'NSWDC',
    'edition': 'ed',
    'editor/editors': 'Ed/Eds',
    'Equal Opportunity Commission (jurisdiction follows)': 'EOC (Vic)',
    'Equal Opportunity Tribunal (jurisdiction follows)': 'EOT (NSW)',
    'Equity Court or Division': 'Eq',
    'European Community': 'EC',
    'European Union': 'EU',
    'Exchequer Court or Division': 'Exch',
    '(and) following': 'ff',
    'for example, for instance': 'eg',
    'Goods and Services Tax': 'GST',
    'Government': 'Govt',
    'Government Insurance Office': 'GIO',
    'gram': 'gm',
    'High Court of New Zealand': 'HC (NZ)',
    'House of Lords': 'HL',
    'Human Rights and Equal Opportunity Commission': 'HREOC',
    'Imperial': 'Imp',
    'Industrial Appeal Court of Western Australia': 'IAC (WA)',
    'Industrial Commission (jurisdiction follows)': 'IC (SA)',
    'Industrial Court (jurisdiction follows)': 'IC (NSW)',
    'Industrial Relations Commission (jurisdiction follows)': 'IRC (NSW)',
    'Industrial Relations Court (jurisdiction follows)': 'IRC of A',
    'International': 'Int',
    'International Centre for Settlement of Investment Disputes Review': 'ICSID Rev',
    'International Labour Organisation': 'ILO',
    'kilogram': 'kg',
    'kilometre': 'km',
    "King's Bench Court or Division": 'KB',
    'Land and Environment Court of New South Wales': 'NSWLEC',
    'Limited': 'Ltd',
    'litre': 'L',
    'Local Government Court': 'Loc Govt Ct',
    'metre': 'm',
    'Migration Review Tribunal': 'MRT',
    'millilitre': 'mL',
    'National Companies and Securities Commission': 'NCSC',
    'operation on proclamation': 'opn on proc',
    'Pay as You Go Tax': 'PAYG',
    'Planning and Environment Court of Queensland': 'PEC (Qld)',
    'Private': 'Pte',
    'Privy Council (Judicial Committee of the)': 'PC',
    'Proprietary': 'Pty',
    '(provisional liquidator appointed)': '(prov liq apptd)',
    'Public Limited Company': 'plc',
    "Queen's Bench Court or Division (jurisdiction follows)": 'QB (Ontario)',
    'Queensland Government Industrial Gazette': 'QGIG',
    '(receiver and manager appointed)': '(rec and mgr apptd)',
    'Refugee Review Tribunal': 'RRT',
    'repatriation': 'repat',
    'Rex, Regina, The King, The Queen': 'R',
    'State Government Insurance Office (jurisdiction follows)': 'SGIO (SA)',
    'Steamship': 'SS',
    'Social Security Appeals Tribunal': 'SSAT',
    'subparagraph/subparagraphs': 'subpara/subparas',
    'Supreme Court of the United States': 'SC (US)',
    'Sydney Futures Exchange': 'SFE',
    'that is': 'ie',
    'trading as': 't/as',
    'Trade Practices Commission': 'TPC',
    'United Nations Treaty Series': 'UNTS',
    'volume/volumes': 'vol/vols',
    '(voluntary administrator appointed)': '(vol admin apptd)',]
If the full forms are detected, you are to replace or add the abbreviation form according to the above instructions.
However, if an abbreviation form is detected, you should mostly not add its full form.
e.g., in the sentence "data sharing would be voluntary in relation to for example, asset finance (except auto finance), consumer leases, margin loans and reverse mortgages.", "for example" should be replaced by "eg" according to the mapping relationship above.
6. Sometimes abbreviations alone appear at the **start** of the sentence, you should replace the abbreviation with its corresponding full form. If you are not completely sure about the full form, you just insert "(wrong abbr detected)" after the abbreviation. By "at the start of the sentence" I mean it is at the very start of the text given, or directly following a period only.
e.g., "CDR regime operates under a co-regulator model." starts with the abbreviation form "CDR", which is not allowed. So, the "CDR" at the beginning of the sentence should be replaced as "Consumer Data Right" in the output, which means that the output for "CDR regime operates under a co-regulator model." should be "Consumer Data Right regime operates under a co-regulator model."
e.g., in the sentence "AKK is in charge of setting security requirements.", there is no way to check what has the abbreviation form of "AKK", so your modified output should be "AKK(wrong abbr detected) is in charge of setting security requirements."
Note that you only replace the abbreviation form with full form if the abbreviation form is at the beginning of the sentence. Keep others exactly same.
e.g., the "CDR" in "the definition of a ‘CDR consumer’ encompasses both..." will be kept the same.
e.g., the "CCA" and "CDR" in "...unlike other provisions of the CCA, the CDR is not.." will be kept the same.
e.g., the "Chapter" in "Chapter 17 of ASX Listing Rules deals with trading halts, suspension of trading of shares and removal of shares from trading." should be kept as is.
e.g., in the sentence "That is because s 5H of the CLA deals with the duty to warn of risks, whereas ...", the "s 5H" should be kept the same because it is a abbreviation form in the middle of the sentence.
7. Do **NOT** change any standalone abbreviation forms unless they are at the very beginning of the sentence. Changing any standalone abbreviation forms that are in the middle and end of the sentence is restrictly prohibited and will introduce very high risk. So aviod doing so by every effort!
e.g., "I am an ACCC member" should be kept as is, which means you should not modify "ACCC" here.

8. You should change the some multiple acts replace with new format:
e.g., "ss 60I(9)(b), FLA and 66H(8)(b), FCA xxx" replace with "s 60I(9)(b), FLA and s 66H(8)(b), FCA"

The snippet you should check is given in the following <text> tag pairs:
<text>
{}
</text>

You should never change any of the other texts that are not related to your abbreviation task.
e.g.,'Door to door' should never be replaced by 'Door-to-door'.

The following are some combined examples and outputs for your reference, and your output should be in the same manner.
Input text 1:
"S 56AI(3) of the CCA, the definition of a ‘CDR consumer’ encompasses both individuals and business. Further, unlike other provisions of the CCA, the CDR is not limited to goods or services acquired for personal, domestic and household use, or that are valued below the $100,000 threshold."
Output:
<modified_text>
Section 56AI(3) of the CCA, the definition of a ‘CDR consumer’ encompasses both individuals and business. Further, unlike other provisions of the CCA, the CDR is not limited to goods or services acquired for personal, domestic and household use, or that are valued below the $100,000 threshold.
</modified_text>
Reason for modification:
1. Instruction 6: S 56AI(3) at the start of the sentence, the abbreviation form of "S" should be replace to the complete form of "Section".
2. Instruction 1: "CCA" and "CDR" are abbreviations in the middle of the sentence, so, they should be kept as is.

Input text 2:
"Chapter 17 of ASX Listing Rules deals with trading halts, suspension of trading of shares and removal of shares from trading."
Output:
<modified_text>
Chapter 17 of ASX Listing Rules (AL Rules) deals with trading halts, suspension of trading of shares and removal of shares from trading.
</modified_text>
Reason for modification:
1. Instruction 6: "Chapter" is at the starting of the sentence, so, the complete form should be kept.
2. Instruction 2: "ASX Listing Rules" is a legislation not followed by abbreviation. So, "(AL Rules)" should be added after it. ("A" and "L" come from the initial of "ASX" and "Listing").

Input text 3:
"The Amending Rules also narrow the range of products for which CDR data sharing would be compulsory for both banking and non-bank lending data holders. CDR data sharing would be voluntary in relation to for example, asset finance (except auto finance), consumer leases, margin loans and reverse mortgages."
Output:
<modified_text>
The Amending Rules also narrow the range of products for which CDR data sharing would be compulsory for both banking and non-bank lending data holders. Consumer Data Right data sharing would be voluntary in relation to eg, asset finance (except auto finance), consumer leases, margin loans and reverse mortgages.
</modified_text>
Reason for modification:
1. Instruction 6: The abbreviation "CDR" appears at the start of the second sentence and is replaced with its full form "Consumer Data Right".
2. Instruction 5: The phrase "for example" is replaced with its abbreviation "eg".

You should strictly output the modified text within the <modified_text> tag pairs, and also show the place you changed and its reason. It is important that you should try your best to modify on every potential error.
You should output your reason first and provide <modified_text> accordingly.
"""


prompt_revision_reg_check = """
You are a professional legal editor to conduct the process of text checking and proofreading.
You should plan to use available tools to deal with four types of text checking task.

Task descriptions are as follows:
- Identify quotation if there are any quotations in text, for example: ‘High-Level‘ should let quotation be true. 
- Identify reference link if there are any links in text. Output "link: true" for text with links.
- Identify some consecutive uppercase strings, incomplete words like (s, para), special phrase like (for example, Practice note, that is), that might be potentially considered as special names and could be abbreviated in text. Output "abbr: true" for such text.
- Identify legislation acts, bills, rules or regulations in text. Output "leg_ac: true" for text with legislation acts, bills, rules or regulations. However, if the text only contains a legislation reference, such as "s73, Property Law Act 2023 (Qld)", or "s 11(7), Supreme Court Civil Procedure Act 1932 (Tas)", then output "leg_ac: false" for such text.
- Identify words or terms that are capitalized or should be potentially capitalized for later capitalization checking use. If you are not sure, then output "true" for those texts that contains capitalized words in the middle of sentences. Output "capital: true" for such text that contains capitalized words in the middle of sentences.
- Identify whether there are legal case names, titles of books, newspapers, magazines etc. (but not chapters or articles within those publications) and government reports, consultation papers names, unusual Latin phrases or foreign phrases that have not yet been absorbed into English, scientific names and the phrase "Practice Tips" in the text. Also identify whether there are styling related tags like <i>, <b>, or <u> in the text. If so, return true for emphasis.
- Identify whether the text contains numbers in numeral form or in words and that is not a part of legislation reference, and whether the paragraph contains year range. If so, return true for num.
- Identify whether the paragraph input **only** contains a legislation reference, such as "s73, Property Law Act 2023 (Qld)", or "s 11(7), Supreme Court Civil Procedure Act 1932 (Tas)". If so, strictly return false for quotation, abbr, leg_ac, capital, heading, emphasis and num, no matter whether the legislation reference is correct or not, make sure you setting leg_ac to false in such case.

You might be given one or multiple pieces of texts or paragraphs to check, and here are two mock examples to show you the texts and outputs:

input_text: "<Xml><Paragraph paraId=1dd>Combating scams has been seen as ‘reasonable steps’ enduring enforcement and compliance priority for the Australian Competition and Consumer Commission. The ACCC attempts to prevent scams by</Paragraph><Paragraph paraId=1sx Style=Heading1, >xxx: s 27, xxx</Paragraph><Paragraph paraId=2>"Combating scams has been seen as an enduring enforcement and compliance priority for the Australian Competition Act 2025.</Paragraph><Paragraph paraId=3>s 28, Children (Criminal Proceedings) Act 1987 (NSW)<Links><Link RevId=7 Uri="https://www.accc.gov.au/national-anti-scam-centre" /></Links></Paragraph><Paragraph paraId=2sx>xxx: for example, xxx</Paragraph><Paragraph paraId=3asd>xxx 'Not Clear' xxx</Paragraph><Paragraph paraId=6a2s>ss 60I(9)(b), xxx and 66H(8)(b), xxx which xxx</Paragraph><Paragraph paraId=tesd2>xxx section 28R xxx Act xxx</Paragraph><Paragraph paraId=231dsd>xxx ‘xxx’ xxx</Paragraph><Paragraph paraId=dsd>xxx The high court xxx</Paragraph><Paragraph paraId=888>xxx are contained in the Privacy Act 1988 (Cth) (Privacy Act) but xxx<Run Bold="false" Italic="true">Privacy Act 1988</Run></Paragraph><Paragraph paraId=68ko>xxx agreed between the Minister and xxx</Paragraph><Paragraph paraId=7D6F9052>s 65, Law of Property Act 2000 (NT)</Paragraph><Paragraph paraId=3c89>The Framework was the outcome of several phases of consultations from 2019–2022</Paragraph></Xml>"

Example for the yaml output you should give:

- quotation: true
  link: false
  abbr: true
  leg_ac: false
  capital: false
  heading: false
  emphasis: false
  num: false
  para_id: 1dd

- quotation: false
  link: false
  abbr: true
  leg_ac: false
  capital: false
  heading: true
  emphasis: false
  num: false
  para_id: 1sx
  
- quotation: false
  link: false
  abbr: false
  leg_ac: true
  capital: false
  heading: false
  emphasis: false
  num: false
  para_id: 2

- quotation: false
  link: true
  abbr: true
  leg_ac: false
  capital: false
  heading: false
  emphasis: false
  num: false
  para_id: 3

- quotation: false
  link: false
  abbr: true
  leg_ac: false
  capital: false
  heading: false
  emphasis: false
  num: false
  para_id: 2sx

- quotation: true
  link: false
  abbr: false
  leg_ac: false
  capital: true
  heading: false
  emphasis: false
  num: false
  para_id: 3asd

- quotation: false
  link: false
  abbr: true
  leg_ac: false
  capital: false
  heading: false
  emphasis: false
  num: false
  para_id: 6a2s

- quotation: false
  link: false
  abbr: true
  leg_ac: true
  capital: false
  heading: false
  emphasis: false
  num: false
  para_id: tesd2

- quotation: true
  link: false
  abbr: false
  leg_ac: false
  capital: false
  heading: false
  emphasis: false
  num: false
  para_id: 231dsd

- quotation: false
  link: false
  abbr: false
  leg_ac: false
  capital: true
  heading: false
  emphasis: false
  num: false
  para_id: dsd

- quotation: false
  link: false
  abbr: false
  leg_ac: false
  capital: false
  emphasis: true
  heading: false
  num: false
  para_id: 888

- quotation: false
  link: false
  abbr: false
  leg_ac: false
  capital: true
  heading: false
  emphasis: false
  num: false
  para_id: 68ko

- quotation: false
  link: false
  abbr: false
  leg_ac: false
  capital: false
  heading: false
  emphasis: false
  num: false
  para_id: 7D6F9052

- quotation: false
  link: false
  abbr: false
  leg_ac: false
  capital: true
  heading: false
  emphasis: false
  num: true
  para_id: 3c89  

Now your input_text is:\n
{}
Output YAML ONLY without ```yaml as start.
"""

prompt_capital_check = """
You are a professional legal editor to conduct the process of text checking and proofreading.
You should plan to deal with capital check task. You must only follow the instructions below and output the reason why you did modifications by giving out the instruction number.

Capital Check Task instructions are as follows:
1. All proper nouns need to be capitalized, but not abbreviated forms of proper nouns.
e.g., '... the high court dismissed the application. In doing so, the court ...' replace with '... The High Court dismissed the application. In doing so, the court ...'
e.g., '... see guidance note ...' replace with '... See Guidance Note ...'
e.g., '... title ...' replace with '... Title ...'

The snippet you should check and modify is given in the following <text> tag pairs:
<text>
{}
</text>

You should never change any of the other texts that are not related to your Capital check task.
e.g.,"eg" should never be replaced by "e.g." even if you think "e.g." is more formal.

You should output the modified text within the <modified_text> tag pairs, and also show the place you changed and its reason.
You should output your reason first and provide <modified_text> accordingly.
"""

prompt_completeness_check = """
You are a professional legal editor to conduct the process of text checking and proofreading.
You should plan to deal with completeness check task. For this task, you should check whether the text is complete and contains all necessary components in terms of grammar, punctuation, and structure. The texts you are given are legal texts as the bullet point item. And this task is intended to check the completeness of the text in the bullet point item.
e.g., "contracts for the carriage of goods by sea;" is not complete because it lacks a subject or verb.
e.g., "the memorandum or articles of association, constitution or other agreement regulating the affairs of a company, registrable body, co-operative, society, association, industrial organisation or partnership; or" is not complete because it ends with a semicolon and does not have a subject or verb.
e.g., "medicine" is obviously not complete because it is just a single word without any context or structure.

The input text you should check is given in the following <text> tag pairs:
<text>
{}
</text>
You should output the reason why the input text is complete or not complete, you should output "true" or "false" for the completeness of the text in the <completeness> tag pairs.
Note that whether the given text starting with a capital letter or not is not important for the completeness check task, so you should not consider it.
# Example:
Your given input text is:
contracts for the carriage of goods by sea;
Your output should be:
Reasoning:
The given text is not complete because it lacks a subject or verb.
<completeness>
false
</completeness>
"""