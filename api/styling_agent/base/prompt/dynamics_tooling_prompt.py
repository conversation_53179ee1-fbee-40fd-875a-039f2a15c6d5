instruction = """You are a professional legal editor to conduct the process of text checking and proofreading. You should follow some rules in the following and output a comprehensive content that fully covers the requirements of the rules:
"""


quote_rule = """
Rule {num}: Quotation Check Task instructions are as follows, you should plan to deal with quotation check task. You must strictly follow the instructions below and output the reason why you did modifications by giving out the rule number and instruction number: 
# Instructions:
1. Use double quote marks, with singles inside the doubles if required. `“”` and `""` are both considered as double quotation marks, and `‘’` and `''` are both considered as single quotation marks. Standalone single quote marks should be replaced with double quote marks.
e.g., in the sentence `The CEO was once told us that “Our new initiative, dubbed “Project Phoenix”, will “revitalize core markets” by Q4.”`, Project Phoenix and revitalize core markets should be wrapped by single quotation marks (‘’) because they are inside double quotation marks.
e.g., in the sentence `A person will have ‘caused’ the death of an individual if the conduct substantially contributes to the death.`, caused should be wrapped by double quotation marks (“”) because standalone single quotation marks should be prohibited.
e.g., in the sentence `some random person claimed, ‘it is obvious that “kkk” was permitted under the Criminal Code.’`, double quotations and single quotations should be switched because double quotations should be outside single quotations. So, the modified correct text should be `some random person claimed, “it is obvious that ‘kkk’ was permitted under the Criminal Code”.`.
Note: When replacing quotations, check whether contents in quotations contains ending punctuations. e.g., `This is an 'exciting event.'` should be replaced as `This is an "exciting event".`, because quoted content should not include the ending punctuation. However, if the content quoted intentionally includes an ending punctuation as a whole, you should keep the punctuation included. e.g., in the sentence `... entitled “AI and the Courts in 2025: Where are we, and how did we get here?” (Paper).`, the closing punctuation "?" should be retained because it is part of the quote.
Note: If only an open quotation mark or a close quotation mark is seen, you should delete it and treat the text as unwrapped.
2. A **single** quoted contents of more than one sentences in length (more than 50 words) should be set as a separate block of text starting on a new line, and introduced by a colon(":"). When following this instruction, you should also pay attention to the quotation marks in the long quotation. Since contents in long quotation will be separated into a new block, and no outside double quotation marks, so the standalone quotations inside should be double quotations. 
In such situations, if there is any punctuation right before the colon you added, just remove it, and the colon should be followed by a new line and the previously quoted content block.
e.g., the text block `On 1 July 2023, the Australian Government established the National Anti-Scam Centre in the ACCC. The National Anti-Scam Centre and its partners in government, industry, law enforcement, and consumer organisations are “... collectively committed to making Australia a harder target for scammers and reducing the devastating financial and emotional harm caused by scams...”` should be modified as:
`On 1 July 2023, the Australian Government established the National Anti-Scam Centre in the ACCC. The National Anti-Scam Centre and its partners in government, industry, law enforcement, and consumer organisations are:\n
... collectively committed to making Australia a harder target for scammers and reducing the devastating financial and emotional harm caused by scams...`
because the quoted contents are too long, so the contents in the quotations marks itself should be in one separate block, and quotations marks are replaced by a colon and '\n' before the content block.
Make sure to check this instruction, you must first check the length of the quoted content, if it is more than 50 words, then you should separate it into a new block and replace the quotation marks with a colon and '\n' before the content block.

Quotation Check Task only deals with the above two instructions, and you should never change any other text that is not related to quotation check task.

"""

content_input = """
The snippet you should check and modify is given in the following <text> tag pairs:
<text>
{}
</text>

"""

modified_text_output_general_phase_2 = """
You must first list out your tasks, and then strictly do all listed checks above only and output the modified text within the <modified_text> tag pairs, and also show the place you changed and its reason. You **only** do those checks with rule number and instruction number.
Also, after outputting the modified text, you should then output in the <commented_text> tag pairs the modified text with the name of the tool that modified it **right after** the modified content. 
The format should be as the following examples:
# Example1
Input text:
Chapter 17 of ASX Listing Rules deals with trading halts, suspension of trading of shares and removal of shares from trading.
Your output should be:
The check I will do is Abbreviation Check.
Reasoning:
Abbreviation Check":
- "ASX Listing Rules" is not abbreviated, so add abbreviation (AL Rules) after it.
<modified_text>
Chapter 17 of ASX Listing Rules (AL Rules) deals with trading halts, suspension of trading of shares and removal of shares from trading.
</modified_text>
<commented_text>
Chapter 17 of ASX Listing Rules (AL Rules)[1][Abbreviation Check Tool] deals with trading halts, suspension of trading of shares and removal of shares from trading.
</commented_text>
# Example2
Input text:
The judge manually introduced this test quotation to see whether it’d work or not, saying that the case was 'something that should be described in double quote marks', and 'a good test of the automated quotation check'.
Your output should be:
The check I will do is Quotation Check.
Reasoning:
Quotation Check:
- "something that should be described in double quote marks" is wrapped by single quotation marks, so it should be replaced by double quotation marks. Also, it contains 9 words, which is less than 50 words, so it should not be separated into a new block.
- "a good test of the automated quotation check" is wrapped by single quotation marks, so it should be replaced by double quotation marks. Also, it contains 8 words, which is less than 50 words, so it should not be separated into a new block.
<modified_text>
The judge manually introduced this test quotation to see whether it’d work or not, saying that the case was "something that should be described in double quote marks", and "a good test of the automated quotation check".
</modified_text>
<commented_text>
The judge manually introduced this test quotation to see whether it’d work or not, saying that the case was "something that should be described in double quote marks"[1][Quotation Check Tool], and "a good test of the automated quotation check"[2][Quotation Check Tool].
</commented_text>
# Example3
Input text:
Practice Tips: Credit information is regulated separately under <i>Pt 111A</i> of the Privacy Act notwithstanding that it is a type of personal information.
Your output should be:
The check I will do is Emphasis Check.
Reasoning:
Emphasis Check:
- "Practice Tips:" is not italicized and underlined, so it should be wrapped by <i> and <u> tag pairs.
- "Pt 111A" is wrongly italicized, so the <i> and </i> tags are removed.
<modified_text>
<i><u>Practice Tip:</u></i> Credit information is regulated separately under Pt 111A of the Privacy Act notwithstanding that it is a type of personal information.
</modified_text>
<commented_text>
<i><u>Practice Tip:</u></i>[1][Emphasis Check Tool] Credit information is regulated separately under Pt 111A[2][Emphasis Check Tool] of the Privacy Act notwithstanding that it is a type of personal information.
</commented_text>
# Example4:
Input text:
that the <u><i>Australian Competition and Consumer Commission</i></u>  (ACCC) has been consulted.
Your output should be:
The checks I will do are Capital Check, Grammar Check, Spelling Check, and Emphasis Check.
Reasoning:
Capital Check:
- "Australian Competition and Consumer Commission" is a proper noun and is correctly capitalised.
- "ACCC" is an abbreviation and should not be capitalised as a proper noun, which is correct here.
- No other proper nouns or words like "government" or "minister" are present.
Grammar Check:
- There is an extra space before the parenthesis in "(ACCC)", which should be removed for correct grammar.
Spelling Check:
- All words are correctly spelled according to Australian English.
Emphasis Check:
- "Australian Competition and Consumer Commission" is the official name of a government agency and should not be italicised, so the <i><u> tag pairs here are removed.
- "ACCC" is an abbreviation and should not be italicised.
- No legal case names, book titles, consultation papers, Latin/foreign phrases, scientific names, or "Practice Tips:" present.
Double check: All checks above have been conducted, and the only required modification is the removal of the extra space before the parenthesis.
<modified_text>
that the Australian Competition and Consumer Commission (ACCC) has been consulted.
</modified_text>
<commented_text>
that the Australian Competition and Consumer Commission[1][Emphasis Check Tool] [2][Grammar Check Tool](ACCC) has been consulted.
</commented_text>

The contents in the <commented_text> tag pairs should be the same as the <modified_text> tag pairs, but with the name of the tool that modified it in square brackets **right after** the modification. The name of the tool should be in the format of "Abbreviation Check Tool", "Quotation Check Tool", "Legislation Check Tool", etc. depending on which tool you are using. Only use the name of the tool that modified the text, and using any other names is strictly prohibited.
If no modification is made, you should keep the <modified_text> and <commented_text> tag pairs the same as the original text in the <text> tag pairs.
For the <commented_text>, for each tool you use, if no modification is made, you must not insert any comment for that tool. That is, if no modification made by a certain tool, inserting a comment for that tool is strictly prohibited.
You should output your reason first and provide <modified_text>, <commented_text> accordingly after the reasoning. No matter conducting modifications or not, you should always output the reasoning for each check you do, and in the last part of reasoning, you should double check whether you do all checks that come with instructions only. Also remember that you should never change any text that is not related to the checks you do.
"""

capital_rule = """
Rule {num}: Capital Check Task instructions are as follows:
1. All proper nouns need to be capitalized, but not abbreviated forms of proper nouns.
e.g., '... the high court dismissed the application. In doing so, the court ...' replace with '... The High Court dismissed the application. In doing so, the court ...'
e.g., '... see guidance note ...' replace with '... See Guidance Note ...'
e.g., '... title ...' replace with '... Title ...'
2. Some words like 'government', 'minister', etc. should be capitalized when they are used as proper nouns, but not when they are used as common nouns.
3. Word like "guidance note" should be capitalized as "Guidance Note" when it is used as a proper noun, but not when it is used as a common noun.
e.g., for the sentence "This guidance note reviews ...", the "guidance note" is used as a common noun, so it should be kept as is.
e.g., for the sentence "..., see Guidance Note: Title ...", the "Guidance Note" is used as a proper noun, so it should be capitalized.
4. Here are some **DO NOT** for capital check task:
- Do not capitalize the first letter of the input text if it is a lowercase letter.
- Do not change the capitalization of any guidance note titles. e.g., "see Guidance Note: Force majeure clauses", the "Force majeure clauses" should be kept as is.
"""

legislation_rule = """

Rule {num}: Legislation Check Task instructions are as follows, you should plan to deal with legislation check task. You must strictly follow the instructions below and output the reason why you did modifications by giving out the rule number and the instruction number:
# Instructions:
1. You should check and pay much attention to the full or complete form of the legislation names (only including Acts, Bills, Rules and Regulations) in terms of whether they contain all the elements. 
A full or complete form of legislation name contains three elements: the full name, the year, and the jurisdiction. You are to check whether these elements are complete.
e.g., in the sentence "In consequence, the NT Parliament passed the Work Health and Safety (National Uniform Legislation) Amendment Bill 2019 (NT) in November 2019, which commenced operation on 1 February 2020.", "Work Health and Safety (National Uniform Legislation) Amendment Bill 2019 (NT)" is the full legislation (Bill in this case) with its year (2019) and jurisdiction (NT). So, it is correct.
e.g., in the sentence "that landowner and will issue a certificate for both taxes under s 47 of the Land Tax Management Act 1956  and s 49 of the Property Tax (First Home Buyer Choice) Act ", "Land Tax Management Act 1956" is a full form of a legislation (Act in this case) and also comes with the year 1956, however, it misses the jurisdiction, so you should change it to "Land Tax Management Act 1956 ([JX])". And "Property Tax (First Home Buyer Choice) Act" is a full form of a legislation (Act in this case), however, it misses both year and jurisdiction, so you should modify it to "Property Tax (First Home Buyer Choice) Act [year] ([JX])".
e.g., in the sentence "... it is important to bear in mind that, in Div 2 of the Federal Circuit and Family Court of Australia (FCFCA), the ability of the parties recover indemnity costs may be constrained by ...", "Federal Circuit and Family Court of Australia" is not a legislation name (it does not include Acts, Bills, Rules and Regulations), so you should not modify it or extract it, and just leave it as is.
Note: for the missing year and jurisdiction, you must only provide the placeholders like those in the above examples and never fill in concrete contents because it is very important for the follow-up steps to fill in correct year and/or jurisdiction.
Note: you separately put the legislation with year and/or jurisdiction placeholders within <legis> tag pairs, and separate multiple contents with "|" for the next step looking up.
2. You should ignore the abbreviation name of a legislation in the given text. By ignore, I mean you should only leave those abbreviations as is, and free from doing completeness checks on abbreviations.
Note: for short legislation names such as "Corporations Act" and "Privacy Act", their abbreviation form might be the same as the legislation name.
e.g., in the sentence "It introduced a new offence of industrial manslaughter into the Work Health and Safety Act 2012 (SA) (SA Act)", "(SA Act)" is the abbreviation name of "Work Health and Safety Act 2012 (SA)", so you should ignore it and leave it as is.
e.g., in the sentence "In December 2024, the Federal Government passed the Privacy and Other Legislation Amendment Bill (Bill),", "(Bill)" in the end should be the abbreviation (although irregular), so you never change it and leave it as is.
e.g., in the sentence "S 56AI(3) of the CCA, the definition of a ‘CDR consumer’ encompasses both individuals and business.", you should leave "CCA" as is rather than change it to "Competition and Consumer Act [year] ([JX])". Changing an abbreviation form of legislation to its full form is strictly prohibited!
e.g., in the sentence "..., NCC created a new civil penalty provision for harassment.", you should strictly leave NCC untouched in terms of the completeness checking because it is an abbreviation.
e.g., in the sentence "However, not all licensing arrangements are franchises. A licensing arrangement will only involve a franchise if the licence agreement satisfies the definition of “franchise agreement” in s7. of the Franchising Code of Conduct.", the "s7." should be modified to "s 7" to ensure proper formatting of the section reference.
3. You should pay close attention to the position of the legislation reference in the sentence. If the legislation reference is in the middle of a sentence and is not a part of a sentence structure, it should be wrapped with parentheses "()". If the legislation reference is at the end of a sentence, right before the period, and is not a part of a sentence structure, it should be introduced by a colon and a space ": ". If the legislation reference is a part of the sentence structure, then just leave it as is.
e.g., in the sentence "U-turn is prohibited at this intersection r 38, Road Rules 2014 (NSW).", "r 38, Road Rules 2014 (NSW)" is a legislation reference at the end of the sentence right before the period, and it is not a part of sentence structure, then you should add ": " in front of it, the modified output you will give is "U-turn is prohibited at this intersection: r 38, Road Rules 2014 (NSW)."
e.g., in the text block 
"Employers must:
o	conduct risk assessments reg 34; 
o	provide first aid training reg 42; 
o	and report incidents to Safe Work Australia reg 39.",
"reg 34" and "reg 42" should be wrapped by parentheses because they are in the middle of sentence (not right before the period), and "reg 39" should be introduced by ": " because it is at the end of the sentence right before a period. They are not parts of sentence structure.
e.g., in the sentence "A printed advertisement that relates to the provision of credit to which the NCC applies is required to have the ACL number included in the printed advertisement: s 52(2), NCCPA and reg 13, National Consumer Credit Protection Regulations 2010 (Cth).", "s 52(2), NCCPA and reg 13, National Consumer Credit Protection Regulations 2010 (Cth)" are two legislation references at the end of the sentence right before the period, and they are not a part of the sentence structure, so they are introduced by ": ", you should leave it as is.
e.g., in the sentence "Revenue NSW will perform checks to determine if a land tax or property tax liability exists for that landowner and will issue a certificate for both taxes under s 47 of the Land Tax Management Act 1956 and s 49 of the Property Tax (First Home Buyer Choice) Act 2022.", "s 47 of the Land Tax Management Act 1956" and "s 49 of the Property Tax (First Home Buyer Choice) Act 2022" are two legislation references, and in this case, they are part of the sentence structure (they are objects after preposition, forming a prepositional phrase), so there is no need to add parentheses or colon in this case.
e.g., in the sentence "Prior to 1 July 2024, these limits were found in the Motor Accident Insurance Regulation 2018 (Qld).", the legislation reference "Motor Accident Insurance Regulation 2018 (Qld)" is mentioned, and the preposition "in" in front of it showing that they are forming a prepositional phrase, so there is no positional editing required here.
Note: You should also check the representation correctness of the legislation reference.
e.g., in "... is shown in s51AE of the Competition and Consumer Act 2010 (Cth) ...", "s51AE" should be corrected as "s 51AE".
e.g., in "... in the s7. of Spam Act ...", "s7." should be corrected as "s 7".

You should never change any of the other texts that are not related to your legislation check task. This means that you strictly keep the extraneous comma, space or others that are in the given text.
# Examples
The following are some combined examples and outputs for your reference, and your output should be in the same manner.
Mock Input text 1:
"In December 2024, the Federal Government passed the Privacy and Other Legislation Amendment Bill (Bill),"
Expected Output from you:
Reason for modification:
1. The legislation name "Privacy and Other Legislation Amendment Bill" is missing both the year and jurisdiction. So [year] and ([JX]) should be added into the modified text as per instruction 1.
2. As per instruction 1, placeholders for the missing year and jurisdiction were added within <legis> tag pairs.
<modified_text>
In December 2024, the Federal Government passed the Privacy and Other Legislation Amendment Bill [year] ([JX]) (Bill),
</modified_text>
<legis>
Privacy and Other Legislation Amendment Bill [year] ([JX])
</legis>


Mock Input text 2:
"Divestiture orders may be made only in relation to a merger which infringes s 50 of the CCA at the request of the ACCC or “any other person” (s 81). These require the acquirer to divest the business or assets that were acquired."
Expected Output from you:
Reason for modification:
1. The legislation name "CCA" is an abbreviation, and as per instruction 2, abbreviations should be left as is.
2. As per instruction 3, "(s 81)" is at the end of the sentence right in front of a period, so it should be replaced as ": s 81".
<modified_text>
Divestiture orders may be made only in relation to a merger which infringes s 50 of the CCA at the request of the ACCC or “any other person”: s 81. These require the acquirer to divest the business or assets that were acquired.
</modified_text>


Mock Input text 3:
"Item 4(1)(b) of Schedule 1 requires disclosure of any current proceedings against the franchisor, their associates, or any director of the franchisor or associate, under Part 3 of the Independent Contractors Act 2006 (Cth), ss 558B(1) or (2) of the Fair Work Act (Cth) or under any state or territory law that regulates workplace relations or independent contractors. Claims for unfair dismissal of an employee are specifically excluded from this requirement."
Expected Output from you:
Reason for modification:  
1. The legislation name "Fair Work Act (Cth)" is missing the year. As per instruction 1, the placeholder [year] should be added.  
2. The legislation name "Fair Work Act (Cth)" is wrapped within <legis> tag pairs with the placeholder for the missing year.  
3. The representation "ss 558B(1) or (2)" is correct and does not require modification.  
4. The legislation name "Independent Contractors Act 2006 (Cth)" is complete with the full name, year, and jurisdiction, so no modification is required for it.  
5. The positional placement of the legislation references is correct as they are part of the sentence structure, so no positional editing is required.  
<modified_text>  
Item 4(1)(b) of Schedule 1 requires disclosure of any current proceedings against the franchisor, their associates, or any director of the franchisor or associate, under Part 3 of the Independent Contractors Act 2006 (Cth), ss 558B(1) or (2) of the Fair Work Act [year] (Cth) or under any state or territory law that regulates workplace relations or independent contractors. Claims for unfair dismissal of an employee are specifically excluded from this requirement.  
</modified_text>  
<legis>  
Fair Work Act [year] (Cth)  
</legis>
"""

modified_text_output_legislation_phase_2 = """
You must first list out your tasks, and then strictly do all listed checks above only and output the modified text within the <modified_text> tag pairs, the legislations with year and/or jurisdiction placeholders within the <legis> tag pairs **separately** (by separately, I mean that you never put <legis> tag pairs within <modified_text>), and also show the place you changed and its reason. You **only** do those checks with rule number and instruction number.
Also, after outputting the modified text, you should then output in the <commented_text> tag pairs the modified text with the name of the tool that modified it **right after** the modified content.
The format should be as the following examples:
# Example1
Input text:
Chapter 17 of ASX Listing Rules deals with trading halts, suspension of trading of shares and removal of shares from trading.
Your output should be:
The checks I will do are Abbreviation Check and Legislation Check.
Reasoning:
Abbreviation Check":
- "ASX Listing Rules" is not abbreviated, so add abbreviation (AL Rules) after it.
Legislation Check:
- "ASX Listing Rules" is a complete form of legislation name, but it is missing the year and jurisdiction, so you should add [year] ([JX]) after it.
- The full form of the legislation name is wrapped within <legis> tag pairs.
<modified_text>
Chapter 17 of ASX Listing Rules [year] ([JX]) (AL Rules) deals with trading halts, suspension of trading of shares and removal of shares from trading.
</modified_text>
<legis>
ASX Listing Rules [year] ([JX]) 
</legis>
<commented_text>
Chapter 17 of ASX Listing Rules [year] ([JX])[1][Legislation Check Tool] (AL Rules)[2][Abbreviation Check Tool] deals with trading halts, suspension of trading of shares and removal of shares from trading.
</commented_text>
# Example2
Input text:
The judge manually introduced this test quotation to see whether it’d work or not, saying that the case was 'something that should be described in double quote marks', and 'a good test of the automated quotation check'.
Your output should be:
The checks I will do are Quotation Check and Legislation Check.
Reasoning:
Quotation Check:
- "something that should be described in double quote marks" is wrapped by single quotation marks, so it should be replaced by double quotation marks. Also, it contains 9 words, which is less than 50 words, so it should not be separated into a new block.
- "a good test of the automated quotation check" is wrapped by single quotation marks, so it should be replaced by double quotation marks. Also, it contains 8 words, which is less than 50 words, so it should not be separated into a new block.
Legislation Check:
- There are no legislation names in the text, so no legislation related modifications are needed.
<modified_text>
The judge manually introduced this test quotation to see whether it’d work or not, saying that the case was "something that should be described in double quote marks", and "a good test of the automated quotation check".
</modified_text>
<commented_text>
The judge manually introduced this test quotation to see whether it’d work or not, saying that the case was "something that should be described in double quote marks"[1][Quotation Check Tool], and "a good test of the automated quotation check"[2][Quotation Check Tool].
</commented_text>
# Example3
Input text:
Practice Tips: Credit information is regulated separately under <i>Pt 111A</i> of the Privacy Act notwithstanding that it is a type of personal information.
Your output should be:
The checks I will do are Emphasis Check and Legislation Check.
Reasoning:
Emphasis Check:
- "Practice Tips:" is not italicized and underlined, so it should be wrapped by <i> and <u> tag pairs.
- "Pt 111A" is wrongly italicized, so the <i> and </i> tags are removed.
Legislation Check:
- "Privacy Act" is a complete form of legislation name, but it is missing the year and jurisdiction, so you should add [year] ([JX]) after it.
<modified_text>
<i><u>Practice Tip:</u></i> Credit information is regulated separately under Pt 111A of the Privacy Act [year] ([JX]) notwithstanding that it is a type of personal information.
</modified_text>
<legis>
Privacy Act [year] ([JX])
</legis>
<commented_text>
<i><u>Practice Tip:</u></i>[1][Emphasis Check Tool] Credit information is regulated separately under Pt 111A[2][Emphasis Check Tool] of the Privacy Act [year] ([JX]) notwithstanding that it is a type of personal information.
</commented_text>
# Example4:
Input text:
that the <u><i>Australian Competition and Consumer Commission</i></u>  (ACCC) has been consulted.
Your output should be:
The checks I will do are Capital Check, Grammar Check, Spelling Check, Legislation Check, and Emphasis Check.
Reasoning:
Capital Check:
- "Australian Competition and Consumer Commission" is a proper noun and is correctly capitalised.
- "ACCC" is an abbreviation and should not be capitalised as a proper noun, which is correct here.
- No other proper nouns or words like "government" or "minister" are present.
Grammar Check:
- There is an extra space before the parenthesis in "(ACCC)", which should be removed for correct grammar.
Spelling Check:
- All words are correctly spelled according to Australian English.
Legislation Check:
- There are no legislation names in the text, so no legislation related modifications are needed.
Emphasis Check:
- "Australian Competition and Consumer Commission" is the official name of a government agency and should not be italicised, so the <i><u> tag pairs here are removed.
- "ACCC" is an abbreviation and should not be italicised.
- No legal case names, book titles, consultation papers, Latin/foreign phrases, scientific names, or "Practice Tips:" present.
Double check: All checks above have been conducted, and the only required modification is the removal of the extra space before the parenthesis.
<modified_text>
that the Australian Competition and Consumer Commission (ACCC) has been consulted.
</modified_text>
<legis>
</legis>
<commented_text>
that the Australian Competition and Consumer Commission[1][Emphasis Check Tool] [2][Grammar Check Tool](ACCC) has been consulted.
</commented_text>

The contents in the <commented_text> tag pairs should be the same as the <modified_text> tag pairs, but with the name of the tool that modified it in square brackets **right after** the modification. The name of the tool should be in the format of "Abbreviation Check Tool", "Quotation Check Tool", "Legislation Check Tool", etc. depending on which tool you are using. Only use the name of the tool that modified the text, and using any other names is strictly prohibited.
If no modification is made, you should keep the <modified_text> and <commented_text> tag pairs the same as the original text in the <text> tag pairs.
For the <commented_text>, for each tool you use, if no modification is made, you must not insert any comment for that tool. That is, if no modification made by a certain tool, inserting a comment for that tool is strictly prohibited.
You should output your reason first and provide <modified_text>, <commented_text> accordingly after the reasoning. No matter conducting modifications or not, you should always output the reasoning for each check you do, and in the last part of reasoning, you should double check whether you do all checks that come with instructions only. Also remember that you should never change any text that is not related to the checks you do.
You must put every and each legislation name with year and/or jurisdiction placeholders within <legis> tag pairs, and separate multiple legislation names with "|". 
"""

abbreviation_rule = """
Rule {num}: Abbreviation Check Task instructions are as follows, you should plan to deal with abbreviation check task. You must strictly follow the instructions below and output the reason why you did modifications by giving out the instruction number:
# Instructions:
1. Keep abbreviation forms in the middle of sentence as is. This is partly because you cannot be sure of which full form corresponding to the abbreviation form. So, to avoid risk, you have to keep the standalone abbreviations as is.
Make your best effort to **avoid** changing and such abbreviation and strictly keep the abbreviation in this situation. 
e.g., keep "Door to door sales and other non-solicited sales activities are also governed by the provisions of the ACL." as is.
e.g., keep "xxx of the ACL xxx." as is.
e.g., keep "xxx of the CCPA." as is.
e.g., keep "xxx will all be able to exercise the CDR in relation to xxx" as is.
e.g., in sentence "In Action Paintball Games Pty Ltd (In liq) v Barker, the Court of Appeal considered the application of s 5M of the CLA in the case of a girl (the plaintiff) who had tripped over a tree root while playing laser tag.", "CLA" is an unknown abbreviation, but it is fine and you should keep it as is.

2. If a full legislation name (e.g., act names, bill names, rule names, regulation names, etc.) is not followed by abbreviation or is followed by wrong abbreviation, you should add or modify it.
However, if an abbreviation form is detected, you should mostly not add its full form.
Also, use initials as abbreviations, but keep Act, Bill, Rule, Regulation in full in the abbreviation form.
e.g., "Fair Work Act 2001 (Cth)" could have abbreviation as "FW Act", so "Fair Work Act 2001 (Cth)" should be replaced as "Fair Work Act 2009 (Cth) (FW Act)" in the final output.
e.g., "Corporations Act 2001 (Cth)" should have abbreviation of "Corporations Act" rather than "CA" or "C Act".
e.g., "Australian Competition and Consumer Commission" should have abbreviation as "ACCC", so "Australian Competition and Consumer Commission (AC)" should be replaced as "Australian Competition and Consumer Commission (ACCC)".
e.g., "Competition and Consumer (Consumer Data Right) Amendment (2024 Measures No. 2) Rules 2024 (Amending Rules)" is a legislation name followed by its abbreviation "Amending Rules". In this case, you should not abbreviate "Consumer Data Right" to "CDR" because you are not allowed to change the legislation name itself. Also, in such case, do infer that "Amending Rules" is an irregular abbreviation for the legislation and keep it as is and do not add further abbreviations for this legislation.
e.g., in the sentence "The Amending Rules also narrow the range of products for which CDR data sharing would be compulsory for both banking and non-bank lending data holders.", "The Amending Rules" seems like an irregular abbreviation, so you might ignore this abbreviation. Such an abbreviation does not consist of initials, so just leave it as is.

3. If the full form of the following words are detected, you are to replace it to the corresponding abbreviation form. Note that, for these full_forms, only if they are followed by a number or Roman numeral should you replace them to abbreviation form. For example, "number 7, section 5 of the law" should be abbreviated as "no 7, s 5 of the law", however, the "number" in "xxx is required to have the ACL number included in the xxx" should be kept as is. Also pay attention that, if the full form shows at the beginning of the sentence, you should not abbreviate it.
   ['full_form': 'regulation', 'abbreviation_form': 'reg'],
   ['full_form': 'regulations', 'abbreviation_form': 'regs'],
   ['full_form': 'Schedule', 'abbreviation_form': 'Sch'],
   ['full_form': 'Schedules', 'abbreviation_form': 'Schs'],
   ['full_form': 'section', 'abbreviation_form': 's'],
   ['full_form': 'sections', 'abbreviation_form': 'ss'],
   ['full_form': 'subsection', 'abbreviation_form': 'subs'],
   ['full_form': 'subsections', 'abbreviation_form': 'subss'],
   ['full_form': 'number', 'abbreviation_form': 'No'],
   ['full_form': 'numbers', 'abbreviation_form': 'Nos'],
   ['full_form': 'Article', 'abbreviation_form': 'Art'],
   ['full_form': 'Articles', 'abbreviation_form': 'Arts'],
   ['full_form': 'rule', 'abbreviation_form': 'r'],
   ['full_form': 'rules', 'abbreviation_form': 'rr'],
   ['full_form': 'Part', 'abbreviation_form': 'Pt'],
   ['full_form': 'Parts', 'abbreviation_form': 'Pts'],
   ['full_form': 'paragraph', 'abbreviation_form': 'para'],
   ['full_form': 'paragraphs', 'abbreviation_form': 'paras'],
   ['full_form': 'Practice Note', 'abbreviation_form': 'PN'],
   ['full_form': 'Practice Notes', 'abbreviation_form': 'PNN'],
   ['full_form': 'annexure', 'abbreviation_form': 'annex'],
   ['full_form': 'Appendix', 'abbreviation_form': 'App'],
   ['full_form': 'Appendixes', 'abbreviation_form': 'Apps'],
   ['full_form': 'Business Rules', 'abbreviation_form': 'BR'],
   ['full_form': 'clause', 'abbreviation_form': 'cl'],
   ['full_form': 'clauses', 'abbreviation_form': 'cll'],
   ['full_form': 'Division', 'abbreviation_form': 'Div'],
   ['full_form': 'Divisions', 'abbreviation_form': 'Divs'],
   ['full_form': 'Example', 'abbreviation_form': 'eg'],
   ['full_form': 'Examples', 'abbreviation_form': 'egs'],
   ['full_form': 'Exhibit', 'abbreviation_form': 'ex'],
   ['full_form': 'Exhibits', 'abbreviation_form': 'exs'],
   ['full_form': 'figure', 'abbreviation_form': 'fig'],
   ['full_form': 'figures', 'abbreviation_form': 'figs'],
   ['full_form': 'figure', 'abbreviation_form': 'fig'],
   ['full_form': 'footnote', 'abbreviation_form': 'fn'],
   ['full_form': 'footnotes', 'abbreviation_form': 'fnn'],
   ['full_form': 'Gazette', 'abbreviation_form': 'Gaz'],
   ['full_form': 'note', 'abbreviation_form': 'n'],
   ['full_form': 'notes', 'abbreviation_form': 'nn'],
   ['full_form': 'Order', 'abbreviation_form': 'O'],
   ['full_form': 'Orders', 'abbreviation_form': 'Os'],
   ['full_form': 'Ordinance', 'abbreviation_form': 'Ord'],
   ['full_form': 'Ordinances', 'abbreviation_form': 'Ords'],
   ['full_form': 'page', 'abbreviation_form': 'p'],
   ['full_form': 'pages', 'abbreviation_form': 'pp'],
   ['full_form': 'Policy Statement', 'abbreviation_form': 'PS'],
   ['full_form': 'Policy Statements', 'abbreviation_form': 'PSS'],
   ['full_form': 'Precedent', 'abbreviation_form': 'Pr'],
   ['full_form': 'Precedents', 'abbreviation_form': 'Prr'],
   ['full_form': 'standard', 'abbreviation_form': 'std'],
   ['full_form': 'standards', 'abbreviation_form': 'stds'],
   ['full_form': 'Statutory Rule', 'abbreviation_form': 'SR'],
   ['full_form': 'Statutory Rules', 'abbreviation_form': 'SRs'],
   ['full_form': 'subclause', 'abbreviation_form': 'subcl'],
   ['full_form': 'subclauses', 'abbreviation_form': 'subcll'],
   ['full_form': 'Subdivision', 'abbreviation_form': 'Subdiv'],
   ['full_form': 'Subdivisions', 'abbreviation_form': 'Subdivs'],
   ['full_form': 'subparagraph', 'abbreviation_form': 'subpara'],
   ['full_form': 'subparagraphs', 'abbreviation_form': 'subparas'].
e.g., "section 9" should be replaced as "s 9", "Part IV" should be replaced as "Pt IV".
e.g., in the sentence "The Act introduces a new SPF into Part IVF of the Competition and Consumer Act (Cth) (CCA) which will apply to social media companies, banks, and telecommunication providers.", the "Part IVF" should be replaced by "Pt IVF" because "Part" is in the above mapping table.
e.g., in the sentence "The Local Court has a specialist Family Violence List and has issued Practice Note: Domestic Violence Matters ...", the "Practice Note" should be kept because it is not followed by a figure or a number.

4. If the full form of organization names, jargons or terminologies are not followed by abbreviations or are followed by wrong abbreviations, you should add or modify them.
However, if an abbreviation form is detected, you should mostly not add its full form.
e.g., "Australian Competition and Consumer Commission" should have abbreviation of "ACCC", and "Australian Competition and Consumer Commission (AC)" in the text should be replaced with "Australian Competition and Consumer Commission (ACCC)" to show the full form and its abbreviation.
e.g., "Consumer Data Right" should have abbreviation of "CDR", and "Consumer Data Right" in the text should be replaced with "Consumer Data Right (CDR)" to show the full form and its abbreviation.

5. There are other full_form, abbreviation_form mappings as follows:
    ['for example': 'eg',
    'for example,': 'eg',
    'for instance': 'eg',
    'for instance,': 'eg',
    'that is': 'ie',
    'that is,': 'ie',]
If the full forms are detected, you are to replace or add the abbreviation form according to the above instructions.
However, if an abbreviation form is detected, you should mostly not add its full form.
e.g., in the sentence "data sharing would be voluntary in relation to for example, asset finance (except auto finance), consumer leases, margin loans and reverse mortgages.", "for example" should be replaced by "eg" according to the mapping relationship above.

6. Sometimes abbreviations alone appear at the **start** of the sentence, you should replace the abbreviation with its corresponding full form. If you are not completely sure about the full form, you just insert "(wrong abbr detected)" after the abbreviation. By "at the start of the sentence" I mean it is at the very start of the text given, or directly following a period only.
e.g., "CDR regime operates under a co-regulator model." starts with the abbreviation form "CDR", which is not allowed. So, the "CDR" at the beginning of the sentence should be replaced as "Consumer Data Right" in the output, which means that the output for "CDR regime operates under a co-regulator model." should be "Consumer Data Right regime operates under a co-regulator model."
e.g., in the sentence "AKK is in charge of setting security requirements.", there is no way to check what has the abbreviation form of "AKK", so your modified output should be "AKK(wrong abbr detected) is in charge of setting security requirements."
Note that you only replace the abbreviation form with full form if the abbreviation form is at the beginning of the sentence. Keep others exactly same.
e.g., the "CDR" in "the definition of a ‘CDR consumer’ encompasses both..." will be kept the same.
e.g., the "CCA" and "CDR" in "...unlike other provisions of the CCA, the CDR is not.." will be kept the same.
e.g., the "Chapter" in "Chapter 17 of ASX Listing Rules deals with trading halts, suspension of trading of shares and removal of shares from trading." should be kept as is.
e.g., in the sentence "That is because s 5H of the CLA deals with the duty to warn of risks, whereas ...", the "s 5H" should be kept the same because it is a abbreviation form in the middle of the sentence.

7. Do **NOT** change any standalone abbreviation forms unless they are at the very beginning of the sentence. Changing any standalone abbreviation forms that are in the middle and end of the sentence is strictly prohibited and will introduce very high risk. So avoid doing so by every effort!
e.g., "I am an ACCC member" should be kept as is, which means you should not modify "ACCC" here.

8. You should pay attention to single and plural forms of the abbreviation. 
e.g. in the sentence "ss 4 FCA and 5 FCA", "ss" is the plural form of "s", so it should be replaced with "s" in the output, which means that the output should be "s 4 FCA and s 5 FCA".


You should never change any of the other texts that are not related to your abbreviation task.
e.g.,'Door to door' should never be replaced by 'Door-to-door'.

# Examples
The following are some combined examples and outputs for your reference, and your output should be in the same manner.
Input text 1:
"S 56AI(3) of the CCA, the definition of a ‘CDR consumer’ encompasses both individuals and business. Further, unlike other provisions of the CCA, the CDR is not limited to goods or services acquired for personal, domestic and household use, or that are valued below the $100,000 threshold."
Output:
Reason for modification:
1. Instruction 6: S 56AI(3) at the start of the sentence, the abbreviation form of "S" should be replace to the complete form of "Section".
2. Instruction 1: "CCA" and "CDR" are abbreviations in the middle of the sentence, so, they should be kept as is.
<modified_text>
Section 56AI(3) of the CCA, the definition of a ‘CDR consumer’ encompasses both individuals and business. Further, unlike other provisions of the CCA, the CDR is not limited to goods or services acquired for personal, domestic and household use, or that are valued below the $100,000 threshold.
</modified_text>

Input text 2:
"Chapter 17 of ASX Listing Rules deals with trading halts, suspension of trading of shares and removal of shares from trading."
Output:
Reason for modification:
1. Instruction 6: "Chapter" is at the starting of the sentence, so, the complete form should be kept.
2. Instruction 2: "ASX Listing Rules" is a legislation not followed by abbreviation. So, "(AL Rules)" should be added after it. ("A" and "L" come from the initial of "ASX" and "Listing").
<modified_text>
Chapter 17 of ASX Listing Rules (AL Rules) deals with trading halts, suspension of trading of shares and removal of shares from trading.
</modified_text>

Input text 3:
"The Amending Rules also narrow the range of products for which CDR data sharing would be compulsory for both banking and non-bank lending data holders. CDR data sharing would be voluntary in relation to for example, asset finance (except auto finance), consumer leases, margin loans and reverse mortgages."
Output:
Reason for modification:
1. Instruction 6: The abbreviation "CDR" appears at the start of the second sentence and is replaced with its full form "Consumer Data Right".
2. Instruction 5: The phrase "for example" is replaced with its abbreviation "eg".
<modified_text>
The Amending Rules also narrow the range of products for which CDR data sharing would be compulsory for both banking and non-bank lending data holders. Consumer Data Right data sharing would be voluntary in relation to eg, asset finance (except auto finance), consumer leases, margin loans and reverse mortgages.
</modified_text>

"""

grammar_rule = """
Rule {num}: For grammar check, you must correct only the wrong grammar, and prevent paraphrase, or improve the original writing, you needs to be checked in accordance with the English grammar, there are some examples：
eg: xxx 7am xxx replace with xxx 7 am xxx
eg: xxx Act s7 xxx replace with xxx Act s 7 xxx
eg: xxx Act 　s7 xxx replace with xxx Act s 7 xxx
eg: xxx - xxx replace with xxx —— xxx
Remember, rewrite or redraft is strictly prohibited during the grammar check, and make sure all the changes are based on English grammar.
Also, there are some cases need to pay attention to:
1. "e.g." in the text should be replaced with "eg" (without period) in the output.
2. "i.e." in the text should be replaced with "ie" (without period) in the output.
"""

spelling_rule = """
Rule {num}: For spelling, all words should follow Australian English spelling. Exception: Do not change spelling of official names of institutions and organisations, eg Australian Labour Party, World Health Organization, World Trade Center. there are some special example：
Use –ise, Not use –ize. Use –our, Not use –or. Use –dg, Not use –dge. Use –re, Not use –er. Use –se (verb), Not use –ce (verb).
eg: xxx about her behavior during xxx replace with xxx about her behaviour during xxx
eg: xxx from an orange-colored one xxx replace with xxx from an orange-coloured one xxx
"""

emphasis_rule = """
Rule {num}: Emphasis Check Task instructions are as follows, you should plan to deal with emphasis check task. You must strictly follow the instructions below and output the reason why you did modifications by giving out the instruction number:
# Instructions:
1. Legal case names should be italicized. 
e.g., in the sentence "In the case of Jones v Dunkel, the court ruled that ...", "Jones v Dunkel" should be italicized.
2. Titles of books, newspapers, magazines etc. (but not chapters or articles within those publications) and government reports should be italicized. However, guidance note titles should not be italicized.
3. Consultation papers name should be italicized.
4. Unusual Latin phrases or foreign phrases that have not yet been absorbed into English should be italicized.
5. Scientific names should be italicized.
e.g., in the sentence "The Angophora costata is known as smooth-barked apple", "The Angophora costata" should be italicized.
6. Bold should be used very rarely, where there's a significant danger that a sentence would be misread.
7. The phrase "Practice Tips:" and "Practice Tip:" should be italicized and underlined.
8. Act names, bill names, rule names and regulation names should not be italicized unless they are part of a text block that is italicized.
9. Other than adding emphasis, you should also remove any existing emphasis that does not meet the requirements above.
e.g., in the sentence "The ACMA maintains a register of industry standards — see the <u><i>ACMA</i></u> website.", the "ACMA" should not be italicized or underlined because it does not belong to any of the categories above. So, you should remove the <u><i> tag pairs around "ACMA".
You should never change any of the other texts that are not related to your emphasis check task.
The <i></i> tag pair in the given text wraps italicized text, the <b></b> tag pair wraps bold text, and the <u></u> tag pair wraps underlined text.
Note that you wrap the text you think should be italicized with <i> tag pairs, bold with <b> tag pairs, or underline with <u> tag pairs. And also, you should remove the existing <i></i>, <b></b>, and <u></u> tag pairs in the given text if they are not meet the requirements of the emphasis check task.
The sequence of the tags does not matter, for example, you can wrap the text with <i><u></u></i> or <u><i></i></u>, both are fine and have the same meaning.
"""

number_rule = """
Rule {num}: Number Check Task instructions are as follows, you should plan to deal with number check task. You must strictly follow the instructions below and output the reason why you did modifications by giving out the instruction number:
# Instructions:
1. Numbers should be written in words if they are less than ten, and in numerals if they are ten or above.
2. Do not begin a sentence with a numeral - reword the sentence or spell the number out in full word.
e.g., "5 people attended the meeting." should be changed to "Five people attended the meeting." if it is at the beginning of the sentence.
3. Use numerals for page and paragraph numbers, dates, money, percentages, times and weights and measures.
e.g., the "three" in "... by the merger parties within the previous three calendar years, having regard to ..." should be changed to "3" because it is followed by a unit of measurement(calender years in this case), so it should be written in numerals.
4. Use comma to separate the thousands in five (or more) digit numerals in running text. If the number is less than five digits, do not use a comma.
e.g., "The population of the city is 1234567." should be changed to "The population of the city is 1,234,567.". However, the "6,084" in "It is said that 6,084 people attended the meeting." should be changed to "6084" because it is less than five digits.
5. When writing a range of years, use an en dash (–) to separate the years, and abbreviate the second year as much as possible.
e.g., "2023-2024" should be changed to "2023–24", and "1960-1998" should be changed to "1960–98". But "1999-2003" should be kept as "1999–2003" because it is not possible to abbreviate the second year. 
6. Keep the numeral form for the lists, such as "1. First item", "2. Second item", etc.
"""

prompt_list_check = """
Note that this text given is the text under a list item, so you should not check the completeness of the text, the capitalization of the beginning of the text, or the punctuation at the end of the text. Also, the beginning of the text should not be treated as the beginning of a sentence.
"""

def create_prompt_with_rules(text, rules_data):
    prompt_parts = [instruction]
    
    for rule_text, rule_num in rules_data:
        prompt_parts.append(rule_text.format(num=rule_num))
    
    prompt_parts.extend([
        content_input.format(text),
        modified_text_output_general_phase_2
    ])
    
    return ''.join(prompt_parts)

if __name__ == "__main__":
    text = """
The judge manually introduced this test quotation to see whether it’d work or not, saying that the case was ‘something that should be described in double quote marks’, and ‘a good test of the automated quotation check’. They also wanted to see if slang terms like g’day, fair dinkum, barbie, arvo and no worries would get caught or not. Finally the judge really wanted to see if a really long quotation like this one would then get caught in the quotation check and set aside as its own thing, rather than being allowed to just sit alongside the rest of the text: “As soon as the great black velvet pall outside my little window was shot with grey, I got up and went downstairs; every board upon the way, and every crack in every board calling after me, \"Stop thief!\" and \"Get up, Mrs. Joe!\" In the pantry, which was far more abundantly supplied than usual, owing to the season, I was very much alarmed by a hare hanging up by the heels, whom I rather thought I caught, when my back was half turned, winking. I had no time for verification, no time for selection, no time for anything, for I had no time to spare. I stole some bread, some rind of cheese, about half a jar of mincemeat (which I tied up in my pocket-handkerchief with my last night’s slice), some brandy from a stone bottle (which I decanted into a glass bottle I had secretly used for making that intoxicating fluid, Spanish-liquorice-water, up in my room: diluting the stone bottle from a jug in the kitchen cupboard), a meat bone with very little on it, and a beautiful round compact pork pie. I was nearly going away without the pie, but I was tempted to mount upon a shelf, to look what it was that was put away so carefully in a covered earthenware dish in a corner, and I found it was the pie, and I took it in the hope that it was not intended for early use, and would not be missed for some time.”
"""
    rules = [
        (quote_rule, 1),
        (legislation_rule, 2), 
        (abbreviation_rule, 3)
    ]
    prompt = create_prompt_with_rules(text, rules)