from typing import Any, Dict, List, Set, TypedDict

from loguru import logger
from lxml import etree

from base.model.document import (
    Annotations,
    BlockElement,
    CommentId,
    DocumentInfo,
    ElementProperties,
    HyperlinkId,
    NativeRevisions,
)
from base.util.link import extract_links


class Paragraph(TypedDict):
    ParaId: str
    Text: str
    Style: ElementProperties
    Runs: list[dict]
    Links: list[dict]


def extract_paragraphs_xml(document_info: DocumentInfo, revision_toggle: bool = False) -> tuple[list[str], list[str]]:
    """Extract paragraph and list fragments from the serialized XML-like JSON document,
    convert them to XML strings in batches, and return both the batched XML chunks
    and their original order IDs.

    Args:
        data (DocumentInfo): Raw JSON obtained from XML serialization, containing:
            - ``Elements``: List of top-level document elements (paragraphs, lists, tables, etc.).
            - ``Annotations``: Dict mapping element IDs to their annotation details.
            - ``NativeRevisions``: Dict of revision metadata for native writers.
        revision_toggle (bool, optional): Whether to include revision information when
            building paragraph content. Defaults to False.

    Returns:
        tuple[list[str], list[str]]:
            - first item: List of XML strings, each containing **up to 50** paragraphs;
            - second item: Ordered list of each paragraph's ``ParaId``, preserving the
              sequence in which they appeared in the source document.

    Note:
        Tables are currently **skipped** but can be enabled by uncommenting the inline
        processing code if cell-level paragraphs are needed in the future.
    """
    paragraphs = handle_paragraphs(document_info, revision_toggle)
    # break paragraphs into list up to 50 elemnet each
    split_xmls = []
    for i in range(0, len(paragraphs), 50):
        split_paras = paragraphs[i : i + 50]
        xml = convert_json_to_xml(split_paras)
        split_xmls.append(xml)

    paragraphs_order = [para["ParaId"] for para in paragraphs]
    return split_xmls, paragraphs_order


def handle_paragraphs(document_info: DocumentInfo, revision_toggle: bool = False) -> list[Paragraph]:
    data_annotations = document_info.annotations
    native_revision = document_info.native_revisions
    paragraphs: list[Paragraph] = []

    for element in document_info.iter_paragraph():
        para = paragraph_helper(element, data_annotations, native_revision, revision_toggle)
        if para:
            paragraphs.append(para)
    return paragraphs


def convert_json_to_xml(data_list: list[Paragraph], pretty_print: bool = False) -> str:
    outer = etree.Element("Xml")

    for data in data_list:
        paragraph = etree.SubElement(outer, "Paragraph", paraId=data["ParaId"])
        paragraph.text = data["Text"]

        p_style = data.get("Style", {})
        if p_style:
            if p_style.style and p_style.style != "Normal" and p_style.style != "":
                paragraph.set("Style", p_style.style)

        links = data.get("Links", [])
        if links:
            links_elem = etree.SubElement(paragraph, "Links")
            for link in links:
                link_elem = etree.SubElement(links_elem, "Link")
                link_elem.set("LinkId", link["Id"])
                link_elem.set("Uri", link["Uri"])

        runs = data.get("Runs", [])
        if runs:
            run_elems = etree.SubElement(paragraph, "Runs")
            for run in runs:
                style = run.get("style", {})
                if run["Text"] == "" or (not style.get("Bold") and not style.get("Italic")):
                    continue

                run_elem = etree.SubElement(run_elems, "Run", Bold="false", Italic="false", Underline="false")
                run_elem.text = run["Text"]
                if style.get("Bold"):
                    run_elem.set("Bold", "true")
                if style.get("Italic"):
                    run_elem.set("Italic", "true")
                if style.get("Underline"):
                    run_elem.set("Underline", "true")

    return etree.tostring(outer, pretty_print=pretty_print, encoding="unicode")


def paragraph_helper(
    element: BlockElement,
    data_annotations: Annotations,
    native_revisions: NativeRevisions,
    revision_toggle: bool,
):
    text = element.plain_text
    skip_flag = skip_paragraphs(text)
    if skip_flag:
        return
    para: Paragraph = {
        "ParaId": element.element_id,
        "Text": text,
        "Style": element.properties,
        "Links": [],
        "Runs": [],
    }
    hyperlinks: Set[HyperlinkId] = set()
    comments: Set[CommentId] = set()
    merged_runs = []

    for run in element.segments:
        # skip if is delete run
        rev = native_revisions.get(run.content_revision_id)
        if rev and rev.rev_type == "DeletedRun":
            continue
        # skip if revision toggle is on and run does not have ContentRevisionId
        if revision_toggle and not run.content_revision_id:
            continue

        if run.hyperlink_id != "":
            hyperlinks.add(run.hyperlink_id)

        if run.comment_ids:
            for comment_id in run.comment_ids:
                comments.add(comment_id)

        run_style = run.properties
        current_bold = run_style.bold
        current_italic = run_style.italic
        current_underline = run_style.underline

        # Check if we can merge with the previous run
        can_merge = False
        if merged_runs:
            last_style = merged_runs[-1].get("style", {})
            last_bold = last_style.get("Bold", False)
            last_italic = last_style.get("Italic", False)
            last_underline = last_style.get("Underline", False)

            # Compare styles - both must match exactly
            if last_bold == current_bold and last_italic == current_italic and last_underline == current_underline:
                can_merge = True

        text = run.segment_text
        if can_merge:
            # Merge with previous run
            merged_runs[-1]["Text"] += text
        else:
            # Create new run
            merged_runs.append(
                {
                    "Text": text,
                    "style": {
                        "Bold": current_bold,
                        "Italic": current_italic,
                        "Underline": current_underline,
                    },
                }
            )
    if not merged_runs:
        return  # skip if no runs

    para["Runs"].extend(merged_runs)
    links: list[dict] = []
    if hyperlinks:
        for hyperlink_id in hyperlinks:
            hyperlink = data_annotations.hyperlinks.get(hyperlink_id)
            uri = hyperlink.uri if hyperlink else ""
            targets = hyperlink.targets if hyperlink else []
            links.append(
                {
                    "Id": hyperlink_id,
                    "Uri": uri,
                    "Targets": targets,
                }
            )

    if comments:
        for comment_id in comments:
            comment = data_annotations.comments.get(comment_id)
            text = comment.text if comment else ""
            # Extract all links from the comment text
            comment_links = extract_links(text)
            for link in comment_links:
                links.append(
                    {
                        "Id": comment_id,
                        "Uri": link,
                    }
                )
    para["Links"].extend(links)
    return para


def skip_paragraphs(text: str) -> bool:
    """Check if the paragraph text should be skipped based on certain conditions."""
    if not text:
        return True
    if text.endswith("End of Document"):
        return True
    # Add more conditions as needed
    return False


def extract_paragraph_data(p_id: str, xml: str, document_info: DocumentInfo) -> Dict[str, Any]:
    """
    Extract all paragraph data (content, links, styles) in a single operation.
    This eliminates redundant XML parsing and improves performance.

    Returns:
        Dict with keys: content, links, par_style_text, run_style_text
    """
    result = {"content": "", "links": [], "par_style_text": "", "run_style_text": ""}

    if not p_id:
        return result

    # Extract content and styles from XML string
    if xml:
        try:
            root = etree.fromstring(xml)
            paragraph = root.find(f".//Paragraph[@paraId='{p_id}']")

            if paragraph is not None:
                # Extract content
                result["content"] = paragraph.text or ""

                # Extract paragraph style
                par_style = paragraph.get("Style")
                result["par_style_text"] = (
                    f"<pStyle>{par_style}</pStyle>" if par_style and not par_style.startswith("Normal") else ""
                )

                # Extract run styles full
                for element in document_info.iter_paragraph():
                    if element.element_id == p_id:
                        run_texts = []
                        for segment in element.segments:
                            hyperlink_flag = True if segment.hyperlink_id else False
                            text = segment.segment_text
                            style = segment.properties
                            if hyperlink_flag:
                                run_texts.append(text)
                                continue
                            if style.bold:
                                text = f"<b>{text}</b>"
                            if style.italic:
                                text = f"<i>{text}</i>"
                            if style.underline:
                                text = f"<u>{text}</u>"
                            run_texts.append(text)
                        result["run_style_text"] = "".join(run_texts)
                        break

        except etree.XMLSyntaxError:
            logger.error(f"Invalid XML format for paragraph ID {p_id}. Unable to parse.")

    result["links"] = _extract_links_from_content(p_id, document_info)
    return result


def _extract_links_from_content(p_id: str, document_info: DocumentInfo) -> List[Dict[str, Any]]:
    """Helper function to extract links from xml_content dictionary."""
    annotations = document_info.annotations

    # Find the target paragraph
    target_element = None
    for element in document_info.iter_paragraph():
        if element.element_id == p_id:
            target_element = element
            break

    if not target_element:
        return []

    result = []
    seen_hyperlinks = set()
    seen_comments = set()

    hyperlinks_data = annotations.hyperlinks
    comments_data = annotations.comments

    for run in target_element.segments:
        # Process hyperlinks
        hyperlink_id = run.hyperlink_id
        if hyperlink_id and hyperlink_id not in seen_hyperlinks:
            seen_hyperlinks.add(hyperlink_id)
            link_data = hyperlinks_data.get(hyperlink_id)
            if link_data:
                result.append(
                    {
                        "Type": "Hyperlink",
                        "Uri": link_data.uri,
                        "Targets": link_data.targets,
                    }
                )

        # Process comments
        comment_ids = run.comment_ids
        for comment_id in comment_ids:
            if comment_id not in seen_comments:
                seen_comments.add(comment_id)
                comment_data = comments_data.get(comment_id)
                if comment_data:
                    text = comment_data.text
                    # Extract all links from the comment text
                    links = extract_links(text)
                    for link in links:
                        result.append(
                            {
                                "Type": "Comment",
                                "Uri": link,
                                "Targets": [comment_id],
                            }
                        )

    return result
