import difflib
import json
import re
from datetime import datetime


def remove_markers(text):
    """Remove all revision markers from the text."""
    return re.sub(r"\{[^}]+\}", "", text)


def apply_deletions(text):
    """
    Similar to remove_markers, but handles deletions.
    When a deletion marker {xxx|delete|content} is found, check if the content after it matches the specified content.
    If it does, skip that content; otherwise, just remove the marker.
    """
    pattern = re.compile(r"\{([^|]+)\|([^|]+)\|([^}]+)\}")
    output = []
    pos = 0
    while True:
        match = pattern.search(text, pos)
        if not match:
            output.append(text[pos:])
            break
        # Add the text before the marker to output
        output.append(text[pos : match.start()])
        tool, action, content = match.groups()
        if action == "delete":
            if text[match.end() : match.end() + len(content)] == content:
                pos = match.end() + len(content)
            else:
                pos = match.end()
        else:
            # for add or other markers, just remove the marker (no content insertion)
            pos = match.end()
    return "".join(output)


def merge_two_texts_consensus(text1, text2):
    """
    Utilizesing difflib to merge two texts input, and generate a consensus text.
    Steps:
        - Compare the opcodes of the two texts.
        - If they are equal, keep them.
        - If there are differences, take the shorter segment from either text.
    """
    s = difflib.SequenceMatcher(None, text1, text2)
    result = []
    for tag, i1, i2, j1, j2 in s.get_opcodes():
        if tag == "equal":
            result.append(text1[i1:i2])
        else:
            # when substitution, deletion, or insertion occurs, choose the shorter segment as consensus.
            seg1 = text1[i1:i2]
            seg2 = text2[j1:j2]
            result.append(seg1 if len(seg1) <= len(seg2) else seg2)
    return "".join(result)


def unified_baseline(tool_texts):
    """
    Apply deletions to all tool texts and merge them into a unified baseline.
    Generate a unified baseline by merging all tool texts.
    """
    updated_texts = [apply_deletions(t) for t in tool_texts]
    baseline = updated_texts[0]
    for t in updated_texts[1:]:
        baseline = merge_two_texts_consensus(baseline, t)
    return baseline


def get_marker_offset(text, marker_match):
    """
    Calculate the offset of a marker in the text
    by removing all markers before it and returning the length of the remaining text.
    """
    preceding = text[: marker_match.start()]
    return len(re.sub(r"\{[^}]+\}", "", preceding))


def map_offsets(original, unified):
    """
    Build an offset mapping from the original text (after remove_markers) to the unified baseline.
    This function returns a dictionary where the key is the original offset and the value is the unified offset.
    """
    s = difflib.SequenceMatcher(None, original, unified)
    offset_map = {}
    orig_pos, unified_pos = 0, 0
    for tag, i1, i2, j1, j2 in s.get_opcodes():
        if tag == "equal":
            for offset in range(i2 - i1):
                offset_map[i1 + offset] = j1 + offset
        orig_pos = i2
        unified_pos = j2
    return offset_map


def merge_markers(tool_texts):
    baseline = unified_baseline(tool_texts)
    markers = []

    for i, text in enumerate(tool_texts):
        original_text = remove_markers(text)
        offset_map = map_offsets(original_text, baseline)

        for match in re.finditer(r"\{([^|]+)\|([^|]+)\|([^}]+)\}", text):
            tool = match.group(1)
            action = match.group(2)
            content = match.group(3)
            orig_offset = get_marker_offset(text, match)

            # mapping to unified baseline offset
            unified_offset = offset_map.get(orig_offset, orig_offset)

            markers.append({"id": i, "tool": tool, "action": action, "content": content, "offset": unified_offset})

    tool_order = {"abbr_check": 0, "legis_check": 1, "quote_check": 2}
    markers.sort(key=lambda m: (m["offset"], tool_order.get(m["tool"], 99)))
    return baseline, markers


def generate_revision_list(baseline, markers, rev_date):
    """Based on the baseline and all markers, generate a list of revisions:
    Args:
        baseline (_type_): _description_
        markers (_type_): _description_
        rev_date (_type_): datetime string, e.g: "2023-10-01 12:00:00"

    Returns:
        _type_: _description_
    """
    pos = 0
    revisions = []
    rev_date = rev_date or datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    for marker in markers:
        # output baseline text between the last marker and the current marker
        if marker["offset"] > pos:
            segment = baseline[pos : marker["offset"]]
            if segment:
                revisions.append(
                    {"Key": "Revision", "Type": "Insert", "Text": [segment], "Author": None, "Date": None}
                )
        rev_type = "Insert" if marker["action"] == "add" else "Delete"
        revisions.append(
            {
                "Key": "Revision",
                "Type": rev_type,
                "Text": [marker["content"]],
                "Comment": "AI Suggestion: " + marker["tool"] + " " + marker["content"],
                "Author": "AI:" + marker["tool"],
                "Date": rev_date,
            }
        )
        pos = marker["offset"]
    # insert the remaining text after the last marker
    if pos < len(baseline):
        segment = baseline[pos:]
        if segment:
            revisions.append({"Key": "Revision", "Type": "Insert", "Text": [segment], "Author": None, "Date": None})
    return revisions


def merge_tools_outputs(tool_texts, rev_date=None):
    """
    Return revision list after merging all tools outputs.

    Args:
        tool_texts (list): List of texts from different tools.
        rev_date (str, optional): Revision date. Defaults to None.
    """
    baseline, markers = merge_markers(tool_texts)
    revisions = generate_revision_list(baseline, markers, rev_date)
    return revisions


if __name__ == "__main__":
    text_quote = (
        "On 20th February 2025, the Scams Prevention Framework Act 2025 received royal assent and came into force a day later. "
        "The Act introduces a new SPF into Part IVF of the Competition and Consumer Act (Cth) which will apply to social media companies, "
        "banks, and telecommunication providers. These service providers will be required to take {quote_check|delete|‘}{quote_check|add|“}"
        "reasonable steps{quote_check|delete|’}{quote_check|add|”}to prevent, detect, report, respond, and disrupt scams while using their services."
    )

    text_legis = (
        "On 20th February 2025, the Scams Prevention Framework Act 2025 {legis_check|add|(Cth)}received royal assent and came into force a day later. "
        "The Act introduces a new SPF into Part IVF of the Competition and Consumer Act {legis_check|add|2010}(Cth) which will apply to social media companies, "
        "banks, and telecommunication providers. These service providers will be required to take ‘reasonable steps’ to prevent, detect, report, respond, and disrupt scams while using their services."
    )

    text_abbr = (
        "On 20th February 2025, the Scams Prevention Framework Act 2025 {abbr_check|add|(SPF Act)}received royal assent and came into force a day later. "
        "The Act introduces a new SPF into P{abbr_check|delete|ar}t IVF of the Competition and Consumer Act (Cth) {abbr_check|add|(CC Act)}which will apply to social media companies, "
        "banks, and telecommunication providers. These service providers will be required to take ‘reasonable steps’ to prevent, detect, report, respond, and disrupt scams while using their services."
    )

    tool_texts = [text_quote, text_legis, text_abbr]
    merged_revisions = merge_tools_outputs(tool_texts)
    print(json.dumps(merged_revisions, ensure_ascii=False, indent=4))
