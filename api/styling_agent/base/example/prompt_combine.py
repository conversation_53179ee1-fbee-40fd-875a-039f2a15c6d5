from base.example.core import Tool<PERSON>xample

PROMPT_COMBINE_EXAMPLE = ToolExample(
    template="""
from base.tool import StylingTool
data = dict($fields)
result = StylingTool(**data).run()
""",
    input_data={
        "para_id": "p3",
    },
    expected="These service providers will be required to take “reasonable steps” to prevent, detect, report, respond, and disrupt scams while using their services by s 28.",
)
