from base.example.core import Tool<PERSON>xample

QUOTATION_EXAMPLE = ToolExample(
    template="""
from base.tool import QuotationCheckTool
data = dict($fields)
result = QuotationCheckTool(**data).run()
""",
    input_data={
        "para_id": "p3",
        "para_content": "These service providers will be required to take ‘reasonable steps’ to prevent, detect, report, respond, and disrupt scams while using their services.",
        "quotation": True,
        "abbr": False,
        "link": False,
        "leg_ac": False,
    },
    expected="These service providers will be required to take “reasonable steps” to prevent, detect, report, respond, and disrupt scams while using their services.",
)
