from copy import deepcopy
from dataclasses import dataclass
from string import Template
from typing import Optional, Union


# @dataclass(frozen=True)
@dataclass
class ToolExample:
    template: Union[str, Template]
    input_data: dict
    expected: str
    mock_predict: Optional[str] = None

    def __post_init__(self):
        if isinstance(self.template, str):
            self.template = Template(self.template)

    @property
    def code_snippet(self) -> str:
        # flattend input data to key=repr(value) format
        fields = ", ".join(
            f"{k}={repr(v)}"
            for k, v in deepcopy(self.input_data).items()
        )
        # inject fields using substitute to avoid {…} conflict
        return self.template.substitute(fields=fields)

    def run_ok(self, ToolCls):
        ns = {"__builtins__": __builtins__}
        exec(self.code_snippet, ns)
        assert ns["result"]["para_content"] == self.expected, (
            f"Expected {self.expected}, but got {ns['result']['para_content']}"
        )

    def run_mock(self, Tool<PERSON>ls, monkeypatch):
        if self.mock_predict:
            def fake_llm(self_, prompt, **kw):
                return self.mock_predict

            monkeypatch.setattr(ToolCls, "llm_predict", fake_llm)

        ns = {"__builtins__": __builtins__}
        exec(self.code_snippet, ns)

        assert ns["result"]["para_content"] == self.expected
