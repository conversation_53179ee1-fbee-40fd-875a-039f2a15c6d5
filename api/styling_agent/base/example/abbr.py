from base.example.core import Tool<PERSON>xample


ABBR_EXAMPLE = ToolExample(
    template="""
from base.tool import AbbrTool
data = dict($fields)
result = AbbrTool(**data).run()
""",
    input_data={
        "para_id": "p1",
        "para_content": "It introduces a new SPF into s 687, Part IVF of the act, which will apply to social media companies, banks, and telecommunication providers.",
        "abbr": True,
        "link": False,
        "quotation": False,
        "leg_ac": False,
    },
    expected="It introduces a new SPF into s 687, Pt IVF of the act, which will apply to social media companies, banks, and telecommunication providers.",
)
