[tool.poetry]
name = "base"
version = "0.1.0"
description = ""
authors = ["Your Name <<EMAIL>>"]
readme = "README.md"

[tool.poetry.dependencies]
python = ">=3.9, <3.13"
common = { path = "../common", develop = false }
pyyaml = "6.0.2"
requests = "2.32.3"
llm-proxy = {version = "0.22.0", source = "proxy"}
pymysql = "^1.1.1"
python-dotenv = "^1.1.0"
merge3 = "^0.0.15"
python-multipart = "^0.0.20"
dnspython = "^2.7.0"

[tool.poetry.group.test.dependencies]
pytest = "^8.3.5"
pytest-cov = "^6.1.1"
pytest-mock = "^3.14.0"
hypothesis = "^6.131.19"

[tool.poetry.group.dev.dependencies]
types-lxml = "^2025.3.30"

[[tool.poetry.source]]
name = "proxy"
url = "https://cert-proxy-api.search.use1.dev-fos.nl.lexis.com/dist/"
priority = "explicit"

[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"

# Ruff configuration for linting and formatting
[tool.ruff]
# Enable pycodestyle (`E`) and Pyflakes (`F`) codes by default.
# Unlike Flake8, Ruff doesn't enable pycodestyle warnings (`W`) or
# McCabe complexity (`C901`) by default.
select = [
    "E",     # pycodestyle errors
    "W",     # pycodestyle warnings
    "F",     # Pyflakes
    "UP",    # pyupgrade
    "B",     # flake8-bugbear
    "SIM",   # flake8-simplify
    "I",     # isort
    "N",     # pep8-naming
    "C90",   # mccabe
    "RUF",   # Ruff-specific rules
    "PIE",   # flake8-pie
    "T20",   # flake8-print
    "PYI",   # flake8-pyi
    "PT",    # flake8-pytest-style
    "Q",     # flake8-quotes
    "RSE",   # flake8-raise
    "RET",   # flake8-return
    "SLF",   # flake8-self
    "SLOT",  # flake8-slots
    "TCH",   # flake8-type-checking
    "ARG",   # flake8-unused-arguments
    "PTH",   # flake8-use-pathlib
    "ERA",   # eradicate
    "PD",    # pandas-vet
    "PGH",   # pygrep-hooks
    "PL",    # Pylint
    "TRY",   # tryceratops
    "FLY",   # flynt
    "PERF",  # Perflint
    "FURB",  # refurb
    "LOG",   # flake8-logging
    "G",     # flake8-logging-format
]

# Ignore specific rules that might be too strict or not applicable
ignore = [
    "E501",    # Line too long (handled by formatter)
    "W503",    # Line break before binary operator (conflicts with formatter)
    "W504",    # Line break after binary operator (conflicts with formatter)
    "E203",    # Whitespace before ':' (conflicts with formatter)
    "PLR0913", # Too many arguments to function call
    "PLR0912", # Too many branches
    "PLR0915", # Too many statements
    "PLR2004", # Magic value used in comparison
    "TRY003",  # Avoid specifying long messages outside the exception class
    "B008",    # Do not perform function calls in argument defaults
    "PD901",   # df is a bad variable name
    "G004",    # Logging statement uses f-string
    "ARG002",  # Unused method argument
    "ARG001",  # Unused function argument
    "FLY002",  # Consider f-string instead of string join
]

# Allow fix for all enabled rules (when `--fix`) is provided.
fixable = ["ALL"]
unfixable = []

# Exclude a variety of commonly ignored directories.
exclude = [
    ".bzr",
    ".direnv",
    ".eggs",
    ".git",
    ".git-rewrite",
    ".hg",
    ".ipynb_checkpoints",
    ".mypy_cache",
    ".nox",
    ".pants.d",
    ".pyenv",
    ".pytest_cache",
    ".pytype",
    ".ruff_cache",
    ".svn",
    ".tox",
    ".venv",
    ".vscode",
    "__pypackages__",
    "_build",
    "buck-out",
    "build",
    "dist",
    "node_modules",
    "site-packages",
    "venv",
    "migrations",
    "__pycache__",
]

# Same as Black.
line-length = 88
indent-width = 4

# Assume Python 3.9+
target-version = "py39"

[tool.ruff.lint]
# Enable additional rules
extend-select = [
    "C4",    # flake8-comprehensions
    "DTZ",   # flake8-datetimez
    "EM",    # flake8-errmsg
    "FA",    # flake8-future-annotations
    "ICN",   # flake8-import-conventions
    "ISC",   # flake8-implicit-str-concat
    "T10",   # flake8-debugger
    "YTT",   # flake8-2020
]

# Allow unused variables when underscore-prefixed.
dummy-variable-rgx = "^(_+|(_+[a-zA-Z0-9_]*[a-zA-Z0-9]+?))$"

[tool.ruff.lint.mccabe]
# Flag errors (`C901`) whenever the complexity level exceeds 10.
max-complexity = 10

[tool.ruff.lint.per-file-ignores]
# Tests can use magic values, assertions, and longer lines
"test_*.py" = ["PLR2004", "S101", "E501"]
"tests/*.py" = ["PLR2004", "S101", "E501"]
"**/test_*.py" = ["PLR2004", "S101", "E501"]
"conftest.py" = ["PLR2004"]
# __init__.py files can have unused imports
"__init__.py" = ["F401"]

[tool.ruff.lint.isort]
# Sort imports
force-single-line = false
force-sort-within-sections = true
known-first-party = ["base"]
known-third-party = ["pytest", "requests", "yaml"]
section-order = ["future", "standard-library", "third-party", "first-party", "local-folder"]

[tool.ruff.lint.pycodestyle]
# Use a more relaxed max-doc-length
max-doc-length = 100

[tool.ruff.lint.pydocstyle]
# Use Google-style docstrings
convention = "google"

[tool.ruff.format]
# Like Black, use double quotes for strings.
quote-style = "double"

# Like Black, indent with spaces, rather than tabs.
indent-style = "space"

# Like Black, respect magic trailing commas.
skip-magic-trailing-comma = false

# Like Black, automatically detect the appropriate line ending.
line-ending = "auto"

# Enable auto-formatting of code examples in docstrings.
docstring-code-format = true

# Set the line length limit used when formatting code snippets in docstrings.
docstring-code-line-length = "dynamic"

[tool.ruff.lint.flake8-pytest-style]
# Configuration for pytest style checks
fixture-parentheses = false
mark-parentheses = false

[tool.ruff.lint.flake8-quotes]
# Prefer double quotes
inline-quotes = "double"
multiline-quotes = "double"

[tool.ruff.lint.flake8-type-checking]
# Enforce type checking imports
strict = true
