# Optional since it can be assumed when an URN or asset id, asset name, asset area id, and asset area name are provided.
repository: asset
 
# Required: either an asset id, asset name, area id, and area name OR an URN. Corresponds with details in Glitz (http://glitz.lexisai.aws.lexis.com)
# If both an URN and asset/asset-area blocks are provided then the URN will take precedent.
# Together the asset fields form a URN string. It's formatted as "urn:asset:<assetid>:<assetname>:<assetareaid>:<assetareaname>:<functionname>".
# Use the "bifrost stat" command to view the formatted URN.
# When the fields are combined to form the URN all characters are made lower-cased and all spaces are replaced with dashes.
asset:
  urn: urn:asset:5633:LCP-PG:12069:styling_agent
  # Either an URN or all of the below details are required. DO NOT set both since they tend to get out of sync and it leads to confusion. If both are provided then the URN will used and not the other fields.
  id: 5633
  name: LCP-PG         # Can have letters, numbers, and spaces
  area:
    id: 12069
    name: styling_agent  # Can have letters, numbers, and spaces