import concurrent.futures
import json
import os
from functools import partial
from typing import Call<PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, TypedDict

import boto3
import openpyxl
from openpyxl.styles import Alignment, Border, Font, PatternFill, Side
from openpyxl.utils import get_column_letter

os.environ["JOB_PREFIX"] = "local-run"
os.environ["ENV"] = "dev"
# os.environ["LLM_DEBUG"] = "true"
# os.environ["LLM_DEFAULT_TIMEOUT"] = "10"

from base.agent import Agent
from base.config.settings import config
from base.tool import DataLoadTool, PreProcessTool, RevisionRegTool
from loguru import logger

TOOL_NAMES = Literal[
    "RevisionRegTool",
    "QuotationCheckTool",
    "CheckLinkTool",
    "LegislationCheckTool",
    "RevisionExtractTool",
    "DataLoadTool",
    "AbbrTool",
    "MergeParagraphResultTool",
    "WordUpdateTool",
]

agent = Agent(config)


class LoadedData(TypedDict):
    ParaId: str
    Text: str


class ToolParams(TypedDict):
    para_id: str
    para_content: str
    abbr: bool
    link: bool
    quotation: bool
    leg_ac: bool


class ResultEntry(TypedDict):
    """Structure for a single tool processing result.

    Example:
    {
        "tool_name": "AbbrTool",
        "para_id": "4DCC2791",
        "para_content": "Sections 5R–5T of the Civil Liability"
    }
    """

    tool_name: TOOL_NAMES
    para_id: str
    para_content: str


class ParagraphResult(TypedDict):
    """Structure for results of a single paragraph."""

    raw_content: str
    results: list[ResultEntry]


class ParagraphToolConfig(TypedDict):
    para_content: str
    enable_tools: list[TOOL_NAMES]
    disable_tools: list[TOOL_NAMES]


def load_data(s3_file_path: str) -> list[LoadedData]:
    """Load data from S3 file path.

    Args:
        s3_file_path (str): S3 file path.

    Returns:
        list[dict]: List of data.
        Example:
        [
            {
                "ParaId": "4DCC2791",
                "Text": "Sections 5R–5T of the Civil Liability Act."
            }
        ]
    """
    data_load_tool = DataLoadTool(path=s3_file_path)
    return data_load_tool.run()


def param_builder(data_item: LoadedData, tool_name: TOOL_NAMES) -> ToolParams:
    """Build parameters for a tool call.

    Args:
        data_item (LoadedData): The data item to process.
        tool_name (TOOL_NAMES): The name of the tool to call.

    Returns:
        ToolParams: The parameters for the tool call.
    """
    return {
        "para_id": data_item["ParaId"],
        "para_content": data_item["Text"],
        "abbr": tool_name == "AbbrTool",
        "link": tool_name == "CheckLinkTool",
        "quotation": tool_name == "QuotationCheckTool",
        "leg_ac": tool_name == "LegislationCheckTool",
    }


class ToolRunner:
    """A class to run tools concurrently using multi-threading.

    This class provides methods to execute multiple tool calls in parallel,
    significantly improving performance when processing large datasets.
    """

    def __init__(self, max_workers: int | None = None):
        """Initialize the ToolRunner with a specified number of worker threads.

        Args:
            max_workers: Maximum number of worker threads to use. If None,
                         it will default to the number of processors on the machine,
                         multiplied by 5 (which is typically a good balance for I/O-bound tasks).
        """
        self.max_workers = max_workers
        logger.info(f"ToolRunner initialized with max_workers={max_workers}")

    def _execute_tool(self, tool_name: TOOL_NAMES, data: ToolParams) -> ResultEntry:
        """Execute a single tool call.

        Args:
            tool_name: Name of the tool to run.
            data: Parameters for the tool.

        Returns:
            The result of the tool call.
        """
        logger.debug(f"Executing tool {tool_name} with para_id={data['para_id']}")
        tool_class = agent.get_tools()[tool_name]
        tool_instance = tool_class(**data)
        result = tool_instance.run()
        logger.debug(f"Tool {tool_name} execution completed for para_id={data['para_id']}")
        return {
            "tool_name": tool_name,
            "para_id": result["para_id"],
            "para_content": result["para_content"],
        }

    def run_batch(self, tool_name: TOOL_NAMES, data_list: list[ToolParams]) -> list[ResultEntry]:
        """Run the same tool on multiple data items concurrently.

        Args:
            tool_name: Name of the tool to run.
            data_list: List of parameter sets for the tool.

        Returns:
            List of results from all tool calls.
        """
        logger.info(f"Starting batch execution of {tool_name} on {len(data_list)} items")
        with concurrent.futures.ThreadPoolExecutor(max_workers=self.max_workers) as executor:
            # Create a partial function with the tool_name already filled in
            fn = partial(self._execute_tool, tool_name)
            # Map the function to all data items and collect results
            results = list(executor.map(fn, data_list))
        logger.info(f"Completed batch execution of {tool_name}, got {len(results)} results")
        return results

    def run_mixed_batch(self, tasks: list[Tuple[TOOL_NAMES, ToolParams]]) -> list[ResultEntry]:
        """Run different tools with different parameters concurrently.

        Args:
            tasks: List of (tool_name, params) tuples.

        Returns:
            List of results from all tool calls.
        """
        total_tasks = len(tasks)
        logger.info(f"Starting execution of {total_tasks} total tasks")

        with concurrent.futures.ThreadPoolExecutor(max_workers=self.max_workers) as executor:
            # Submit all tasks to the executor
            future_to_task = {
                executor.submit(self._execute_tool, tool_name, params): (
                    tool_name,
                    params,
                )
                for tool_name, params in tasks
            }

            # Collect results as they complete
            results = []
            completed = 0
            for future in concurrent.futures.as_completed(future_to_task):
                results.append(future.result())
                completed += 1
                if completed % 10 == 0 or completed == total_tasks:
                    logger.info(f"Progress: {completed}/{total_tasks} tasks completed ({completed / total_tasks:.1%})")

        logger.info(f"Completed all {total_tasks} tasks")
        return results

    def process_loaded_data(
        self,
        loaded_data: list[LoadedData],
        para_id_to_tools: dict[str, ParagraphToolConfig],
        param_builder: Callable[[LoadedData, TOOL_NAMES], ToolParams] = param_builder,
    ) -> dict[str, ParagraphResult]:
        """Process all items in loaded_data with all specified tools concurrently.

        Args:
            loaded_data: List of data items to process.
            para_id_to_tools: Dictionary mapping paragraph IDs to tool configurations.
            param_builder: Function that builds tool parameters from a data item and tool name.

        Returns:
            Dictionary mapping paragraph IDs to result data containing:
                - raw_content: Original paragraph text
                - results: List of tool processing results
        """
        # Initialize results structure
        results_by_para_id = {}

        # Create all tasks
        all_tasks = []
        for data_item in loaded_data:
            para_id = data_item["ParaId"]
            raw_content = data_item["Text"]

            # Initialize entry for this paragraph ID with raw content
            if para_id not in results_by_para_id:
                results_by_para_id[para_id] = {
                    "raw_content": raw_content,
                    "results": [],
                }

            # Get enabled tools for this paragraph
            try:
                enable_tool_names = para_id_to_tools[para_id].get("enable_tools")
            except KeyError:
                logger.info(f"Para ID {para_id} not found in para_id_to_tools")
                continue

            if enable_tool_names is None:
                raise ValueError(f"No tools found for data item {para_id}")

            # Create tasks for enabled tools
            for tool_name in enable_tool_names:
                params = param_builder(data_item, tool_name)
                all_tasks.append((tool_name, params))

            # Add placeholder results for disabled tools
            disable_tool_names = para_id_to_tools[para_id].get("disable_tools", [])
            for tool_name in disable_tool_names:
                results_by_para_id[para_id]["results"].append(
                    {"para_id": para_id, "para_content": "", "tool_name": tool_name}
                )

        # Run all tasks concurrently
        all_results = self.run_mixed_batch(all_tasks)

        # Add results to the appropriate paragraph entries
        for result in all_results:
            para_id = result["para_id"]
            results_by_para_id[para_id]["results"].append(result)

        return results_by_para_id


class ResultHandler:
    """Handle the results of a ToolRunner.

    Creates an Excel file to save the results, organized by paragraph ID with proper formatting.

    Headers: para_id, tool_name, original_para_text, tool_output

    Args:
        results (Dict[TOOL_NAMES, List[ToolCallResult]]): The results of a ToolRunner.
    """

    def __init__(self, results: dict[str, ParagraphResult]):
        self.paragraph_results = results
        self.headers = [
            "para_id",
            "tool_name",
            "original_para_text",
            "tool_output",
            "correct",
        ]
        # Column widths in characters
        self.column_widths = {
            "para_id": 12,
            "tool_name": 20,
            "original_para_text": 50,
            "tool_output": 50,
            "correct": 10,
        }
        # Excel style configurations
        self.header_fill = PatternFill(start_color="E0E0E0", end_color="E0E0E0", fill_type="solid")
        self.header_font = Font(bold=True)
        self.wrap_text_alignment = Alignment(wrap_text=True, vertical="top")
        self.border = Border(
            left=Side(style="thin"),
            right=Side(style="thin"),
            top=Side(style="thin"),
            bottom=Side(style="thin"),
        )

    def _organize_results_by_paragraph(self) -> dict[str, list[dict]]:
        """Organize tool results by paragraph ID.

        Returns:
            dict[str, list[dict]]: Results organized by paragraph ID.
        """
        results_by_para_id = {}
        for para_id, paragraph_result in self.paragraph_results.items():
            tool_results = paragraph_result["results"]

            for result in tool_results:
                para_id = result["para_id"]
                if para_id not in results_by_para_id:
                    results_by_para_id[para_id] = []

                original_para_content = paragraph_result["raw_content"]
                if original_para_content is None:
                    raise ValueError(f"Para ID {para_id} not found in DataLoadTool results.")

                results_by_para_id[para_id].append(
                    {
                        "para_id": str(para_id),  # Ensure para_id is a string
                        "tool_name": str(result["tool_name"]),  # Ensure tool_name is a string
                        "original_para_text": original_para_content,
                        "tool_output": result["para_content"],
                        "correct": None,
                    }
                )

        return results_by_para_id

    def _init_excel_file(self) -> openpyxl.Workbook:
        """Initialize an Excel workbook with proper styling.

        Returns:
            openpyxl.Workbook: A new Excel workbook.
        """
        workbook = openpyxl.Workbook()
        worksheet = workbook.active
        worksheet.title = "Results"

        # Set column headers
        for col_idx, header in enumerate(self.headers, 1):
            cell = worksheet.cell(row=1, column=col_idx)
            cell.value = header
            cell.fill = self.header_fill
            cell.font = self.header_font
            cell.border = self.border

            # Set column width
            width = self.column_widths.get(header, 15)  # Default width if not specified
            worksheet.column_dimensions[get_column_letter(col_idx)].width = width

        # Enable filtering on header row
        worksheet.auto_filter.ref = f"A1:{get_column_letter(len(self.headers))}1"

        return workbook

    def _write_results_to_excel(self, workbook: openpyxl.Workbook, results_by_para_id: dict[str, list[dict]]) -> int:
        """Write results to Excel with proper styling.

        Args:
            workbook: The Excel workbook to write to.
            results_by_para_id: Results organized by paragraph ID.

        Returns:
            int: The number of rows written.
        """
        worksheet = workbook.active
        row_idx = 2  # Start from row 2 (after headers)

        # Calculate appropriate row height for text cells
        row_height = 100  # Default row height for cells with potentially long text

        for para_id in results_by_para_id.keys():
            para_results = results_by_para_id[para_id]
            for result in para_results:
                # Write each field to its cell
                for col_idx, header in enumerate(self.headers, 1):
                    cell = worksheet.cell(row=row_idx, column=col_idx)
                    cell.value = result.get(header)
                    cell.border = self.border

                    # Apply wrapping text alignment for content fields
                    if header in ["original_para_text", "tool_output"]:
                        cell.alignment = self.wrap_text_alignment

                # Set row height for this row
                worksheet.row_dimensions[row_idx].height = row_height

                row_idx += 1

        return row_idx - 2  # Number of data rows written

    def save(self, file_path: str):
        """Save the results to an Excel file, organized by paragraph ID with proper formatting.

        Args:
            file_path (str): Path to the output Excel file.
        """
        # If file_path ends with .csv, change to .xlsx
        if file_path.lower().endswith(".csv"):
            file_path = file_path[:-4] + ".xlsx"

        logger.info(f"Saving results to Excel file: {file_path}")

        # Organize results by paragraph ID
        results_by_para_id = self._organize_results_by_paragraph()
        logger.debug(f"Organized results by paragraph ID, found {len(results_by_para_id)} unique paragraphs")

        # Initialize Excel file and write data
        try:
            workbook = self._init_excel_file()
            row_count = self._write_results_to_excel(workbook, results_by_para_id)

            # Save the workbook
            workbook.save(file_path)

            logger.info(f"Successfully wrote {row_count} rows to {file_path}")
        except Exception as e:
            logger.error(f"Error writing to Excel file: {e}")
            raise


def run_tool_pipeline(
    input_file_path: str,
    output_file_path: str,
    max_items: int | None = None,
) -> None:
    """Run the complete tool processing pipeline.

    This function performs the following steps:
    1. Loads data from the input file, the file can be a local JSON file or an S3 file path
       (e.g., s3://5633-pg-dev-styling-agent/jobs/2025-05-16/test5/local-run_202505160530/test5_original.docx)
       If the file is a local JSON file, it will be loaded directly.
       If the file is an S3 file path, it will be loaded using the DataLoadTool.
       The loaded data is expected to be a list of dictionaries with "ParaId" and "Text" keys.
       Example:
       [
           {
               "ParaId": "4DCC2791",
               "Text": "Sections 5R–5T of the Civil Liability Act."
           }
       ]
    2. Processes the loaded data with the specified tools
    3. Saves the results to a CSV file with the specified path.

    Args:
        input_file_path (str): Path to the input JSON file, or S3 file path
        output_file_path (str): Path to the output CSV file
        max_items (int, optional): Maximum number of items to process. If None, all items are processed.
    """
    logger.info(f"Starting tool pipeline with input: {input_file_path}, output: {output_file_path}")

    try:
        if isinstance(input_file_path, str) and input_file_path.startswith("s3://"):
            loaded_data = load_data(input_file_path)
        elif input_file_path.endswith(".json"):
            with open(input_file_path, "r") as f:
                try:
                    loaded_data = json.load(f)
                except json.JSONDecodeError as e:
                    logger.error(f"Failed to decode JSON from file {input_file_path}: {e}")
                    raise
        else:
            raise ValueError(f"Unsupported input file format: {input_file_path}, expected .json or S3 path(docx)")

        if not loaded_data:
            raise ValueError(f"No data loaded from {input_file_path}")

        logger.info(f"Successfully loaded {len(loaded_data)} items from {input_file_path}")
    except Exception as e:
        logger.error(f"Error loading data from {input_file_path}: {e}")
        raise

    # Prepare data for processing
    if max_items is not None:
        process_data = loaded_data[:max_items]
        logger.info(f"Processing {len(process_data)} out of {len(loaded_data)} total items")
    else:
        process_data = loaded_data

    # Which tools need to be called?
    revision_extract_tool = PreProcessTool(xml_content=loaded_data)
    revision_paragraphs = revision_extract_tool.run()
    revision_reg_tool = RevisionRegTool(revision_paragraphs_xml=revision_paragraphs)
    revision_tool_assignments: list[ToolParams] = revision_reg_tool.run()

    para_id_to_tools: dict[str, ParagraphToolConfig] = {}
    for item in revision_tool_assignments:
        enable_tools: list[TOOL_NAMES] = []
        disable_tools: list[TOOL_NAMES] = []
        if item["quotation"] is True:
            enable_tools.append("QuotationCheckTool")
        else:
            disable_tools.append("QuotationCheckTool")
        if item["link"] is True:
            enable_tools.append("CheckLinkTool")
        else:
            disable_tools.append("CheckLinkTool")
        if item["abbr"] is True:
            enable_tools.append("AbbrTool")
        else:
            disable_tools.append("AbbrTool")
        if item["leg_ac"] is True:
            enable_tools.append("LegislationCheckTool")
        else:
            disable_tools.append("LegislationCheckTool")
        # If the literal of para_id is an integer, it is actually an int here,
        #  converted to a string for consistency
        para_id_to_tools[str(item["para_id"])] = {
            "para_content": item["para_content"],
            "enable_tools": enable_tools,
            "disable_tools": disable_tools,
        }

    # Process data with tools
    logger.info("Processing data with tools...")
    tool_runner = ToolRunner()
    results = tool_runner.process_loaded_data(process_data, para_id_to_tools)

    # Save results
    logger.info("Creating result handler...")
    result_handler = ResultHandler(results)
    result_handler.save(output_file_path)
    logger.info("Pipeline execution completed successfully")


def read_s3_files(s3_file_dir: str) -> list[str]:
    """Read all files from an S3 directory.

    Args:
        s3_file_dir (str): S3 directory path.

    Returns:
        list[str]: List of file paths.
    """
    s3 = boto3.client("s3")
    bucket_name, prefix = s3_file_dir.replace("s3://", "").split("/", 1)
    response = s3.list_objects_v2(Bucket=bucket_name, Prefix=prefix)

    if "Contents" not in response:
        logger.warning(f"No files found in {s3_file_dir}")
        return []

    file_paths = [f"s3://{bucket_name}/{item['Key']}" for item in response["Contents"]]
    return file_paths


def main():
    """Main function to run the tool pipeline."""
    logger.info("Starting tool pipeline execution...")
    s3_file_dir = "s3://5633-pg-dev-styling-agent/uat1_docs/"
    # Read all files from the S3 directory
    input_files = read_s3_files(s3_file_dir)

    # input_files = [
    #     "s3://5633-pg-dev-styling-agent/uat1_docs/uVic — County Court — Form, content and filing of originating processes.docx",
    # ]
    output_dir = os.path.dirname(__file__) + "/output"

    os.makedirs(output_dir, exist_ok=True)
    for i, input_file_path in enumerate(input_files):
        # Extract file name for output file
        if input_file_path.startswith("s3://"):
            file_name = os.path.basename(input_file_path)
            base_name = os.path.splitext(file_name)[0]
        else:
            base_name = os.path.splitext(os.path.basename(input_file_path))[0]

        output_file_path = os.path.join(output_dir, f"{base_name}_result.xlsx")

        logger.info(f"Processing file {i + 1}/{len(input_files)}: {input_file_path}")
        # Process the file
        run_tool_pipeline(
            input_file_path=input_file_path,
            output_file_path=output_file_path,
            max_items=None,
        )
        logger.info(f"Successfully processed {input_file_path}, output saved to {output_file_path}")

    logger.info("Tool pipeline execution completed.")


if __name__ == "__main__":
    main()
