# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Development Commands

### Testing
```bash
# Run all tests with coverage
pytest

# Run specific test modules
pytest test/ut/tool/test_abbr.py
pytest test/ut/handler/test_planner_handler.py

# Run evaluation tests
pytest test/evaluation/test_all_examples.py
```

### Dependencies
```bash
# Install dependencies (Poetry)
poetry install

# Install with test dependencies
poetry install --with test

# Update dependencies
poetry update
```

### Linting and Code Quality
```bash
# The project uses pytest with coverage reporting
# Coverage configuration is in pytest.ini with --cov=base flag

# Local development and testing
# Run the local runner for testing scripts locally
python script_data/local_runner.py

# Test with specific tool runner
python script_data/tool_runner.py
```

## Architecture Overview

### Core Components

**Agent-Based Architecture**: The system is built around an AI agent (`base/agent.py`) that generates Python scripts to process documents through various tools.

**Two-Handler Pattern**:
- `planner_handler.py`: Handles file uploads, generates processing scripts via the Agent
- `executor_handler.py`: Downloads and executes the generated scripts

**Tool System**: Modular tools in `base/tool/` that inherit from `Tool` or `LLMTool` base classes. Available tools include:
- `RevisionRegTool`: Revision tracking and management
- `CheckLinkTool`: Link validation
- `PreProcessTool`: Document preprocessing
- `DataLoadTool`: Data loading and transformation
- `WordUpdateTool`: Word document updating operations
- `StylingTool`: Document styling and formatting (note: referenced but not in current active tools)
- `PatchTool`: Document patching operations
- `ConcurrentTool`: Parallel processing operations
- `AuthorCheckTool`: Author information validation
- `HeadingCheckTool`: Document heading validation

Tools are registered in `base/agent.py:get_tools()` method. Some tools are commented out (QuotationCheckTool, LegislationCheckTool, AbbrTool) indicating they may be under development or deprecated.

### Key Patterns

**Configuration Management**: Uses Pydantic models in `base/config/settings.py` with environment-based configuration and secret management.

**LLM Integration**: Tools can inherit from `LLMTool` for AI-powered operations using `llm-proxy` library with configurable models per tool via `TOOL_MODEL_MAP`. Model configuration includes:
- `AGENT_MODEL`: Model used by the main Agent class
- `TOOL_MODEL_MAP`: JSON mapping of tool names to specific LLM models
- Supported models include OpenAI GPT-4o, GPT-4o-mini, o1-mini, o1, o3-mini variants

**AWS Lambda Deployment**: Two separate Lambda functions for the planning and execution phases, with API Gateway integration as defined in `openapi.yaml`.

**Processing Modes**: 
- `full`: Complete document processing with all tools
- `revision`: Processing only revision paragraphs

### Data Flow

1. File upload → Planner Handler
2. Agent generates Python script using selected tools
3. Script uploaded to S3
4. Executor Handler downloads and runs script
5. Results returned via presigned URL or email

### Client Integrations

**AWS Services**: S3 (storage), SES (email), Lambda (execution)
**External**: Solr (search), MySQL (database), CAAS repository
**LLM**: Multiple model support via `llm-proxy` with tenant-based configuration

### Error Handling

Custom error hierarchy in `base/error/` with specific error types for different components (planner, executor, client operations).

### Testing Structure

- `test/ut/`: Unit tests organized by component
- `test/evaluation/`: End-to-end evaluation tests
- `test/sample/`: Test data and sample files

## Important Notes

- The project uses Poetry for dependency management
- All tools must implement the `Tool` base class interface with required class variables: `name`, `description`, and `example`
- LLM tools use tenant-based authentication and configurable models via `LLM_TENANT` and model mapping
- Scripts are dynamically generated by the Agent and executed in isolated Lambda environments
- File processing supports both synchronous (immediate download URL) and asynchronous (email-based) workflows via `/` and `/v2` endpoints
- Local development scripts are available in `script_data/` for testing without Lambda deployment
- The system processes JSON documents representing structured content with paragraphs, metadata, and formatting information